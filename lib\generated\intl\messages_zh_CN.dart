// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh_CN locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh_CN';

  static String m0(date) => "你的试用将在${date}天后到期。";

  static String m1(images) => "已上传${images}张照片";

  static String m2(price, date) => "下次账单为${price}，日期${date}。";

  static String m3(uid) => "用户ID ${uid} 已复制到剪贴板！";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Regenerate": MessageLookupByLibrary.simpleMessage("重新生成"),
        "a_to_z": MessageLookupByLibrary.simpleMessage("A到Z"),
        "about_us": MessageLookupByLibrary.simpleMessage("关于我们"),
        "access_notex_web": MessageLookupByLibrary.simpleMessage("访问NoteX网页版"),
        "account": MessageLookupByLibrary.simpleMessage("账户"),
        "account_basic": MessageLookupByLibrary.simpleMessage("基础"),
        "account_content_basic": MessageLookupByLibrary.simpleMessage("有限AI体验"),
        "account_content_pro": MessageLookupByLibrary.simpleMessage("解锁无限AI体验"),
        "account_lifetime": MessageLookupByLibrary.simpleMessage("终身"),
        "achieve_more": MessageLookupByLibrary.simpleMessage("成就更多"),
        "action_items": MessageLookupByLibrary.simpleMessage("行动项"),
        "actionable_intelligence":
            MessageLookupByLibrary.simpleMessage("可执行洞察"),
        "active_description": MessageLookupByLibrary.simpleMessage("未找到有效描述。"),
        "active_recall": MessageLookupByLibrary.simpleMessage("主动回忆更快学习"),
        "add_focus": MessageLookupByLibrary.simpleMessage("添加特定重点或要求..."),
        "add_folder": MessageLookupByLibrary.simpleMessage("移动到文件夹"),
        "add_note": MessageLookupByLibrary.simpleMessage("添加笔记"),
        "add_password": MessageLookupByLibrary.simpleMessage("添加密码"),
        "add_password_to_public":
            MessageLookupByLibrary.simpleMessage("为公开链接添加密码"),
        "add_to": MessageLookupByLibrary.simpleMessage("移动到"),
        "add_to_notes": MessageLookupByLibrary.simpleMessage("添加到笔记"),
        "additional_ins": MessageLookupByLibrary.simpleMessage("额外说明（可选）"),
        "advance_mode": MessageLookupByLibrary.simpleMessage("高级模式"),
        "advanced": MessageLookupByLibrary.simpleMessage("高级"),
        "afternoon_content": MessageLookupByLibrary.simpleMessage("小笔记，大影响"),
        "afternoon_content_1":
            MessageLookupByLibrary.simpleMessage("记录想法，释放头脑"),
        "afternoon_content_3": MessageLookupByLibrary.simpleMessage("混乱中的秩序"),
        "afternoon_content_4":
            MessageLookupByLibrary.simpleMessage("你的创意，有序呈现"),
        "afternoon_content_5": MessageLookupByLibrary.simpleMessage("清晰进行中"),
        "afternoon_content_6": MessageLookupByLibrary.simpleMessage("保留重要内容"),
        "ai_audio_transcription_per_day":
            MessageLookupByLibrary.simpleMessage("每天3次AI音频转录*"),
        "ai_chat": MessageLookupByLibrary.simpleMessage("Nova AI"),
        "ai_chat_assistant": MessageLookupByLibrary.simpleMessage("AI聊天助手"),
        "ai_chat_with_notes": MessageLookupByLibrary.simpleMessage("笔记AI聊天"),
        "ai_insight": MessageLookupByLibrary.simpleMessage("AI洞察"),
        "ai_learning": MessageLookupByLibrary.simpleMessage("AI学习"),
        "ai_learning_companion":
            MessageLookupByLibrary.simpleMessage("我是NoteX的Nova AI"),
        "ai_note_create": MessageLookupByLibrary.simpleMessage("AI笔记创建"),
        "ai_note_creation": MessageLookupByLibrary.simpleMessage("AI笔记创建"),
        "ai_note_from": MessageLookupByLibrary.simpleMessage("音频AI笔记"),
        "ai_notes_10":
            MessageLookupByLibrary.simpleMessage("从YouTube和文档生成无限AI笔记"),
        "ai_notes_3":
            MessageLookupByLibrary.simpleMessage("每天3次录音和音频上传AI笔记（每文件最长60分钟）"),
        "ai_notes_from":
            MessageLookupByLibrary.simpleMessage("YouTube、网页、文档\nAI笔记"),
        "ai_short_1": MessageLookupByLibrary.simpleMessage("每天3个AI短视频生成"),
        "ai_short_3": MessageLookupByLibrary.simpleMessage("每天5个AI短视频生成（测试版）"),
        "ai_short_video": MessageLookupByLibrary.simpleMessage("AI短视频"),
        "ai_study_practice": MessageLookupByLibrary.simpleMessage("AI学习练习"),
        "ai_study_tools": MessageLookupByLibrary.simpleMessage("AI学习工具"),
        "ai_summarize": MessageLookupByLibrary.simpleMessage("AI总结"),
        "ai_transcription": MessageLookupByLibrary.simpleMessage("AI转录"),
        "ai_workflow": MessageLookupByLibrary.simpleMessage("AI工作流"),
        "all": MessageLookupByLibrary.simpleMessage("全部"),
        "all_note": MessageLookupByLibrary.simpleMessage("所有笔记"),
        "all_note_in_folder":
            MessageLookupByLibrary.simpleMessage("确定要删除此文件夹？"),
        "all_tabs": MessageLookupByLibrary.simpleMessage("所有标签页"),
        "allow": MessageLookupByLibrary.simpleMessage("允许"),
        "almost_done": MessageLookupByLibrary.simpleMessage("快完成了"),
        "and": MessageLookupByLibrary.simpleMessage("和"),
        "answer": MessageLookupByLibrary.simpleMessage("答案"),
        "anyone_with_link": MessageLookupByLibrary.simpleMessage("有链接的人可查看"),
        "app_feedback": MessageLookupByLibrary.simpleMessage("NoteX应用反馈"),
        "app_store": MessageLookupByLibrary.simpleMessage("在App Store评价"),
        "appearance": MessageLookupByLibrary.simpleMessage("外观"),
        "appreciate_cooperation": MessageLookupByLibrary.simpleMessage(
            "这些信息将帮助我们快速识别和解决你的问题。感谢你帮助我们改进NoteX。"),
        "appreciate_cooperation2":
            MessageLookupByLibrary.simpleMessage("这将帮助我们更有效地调查和解决问题。"),
        "appreciate_cooperation3":
            MessageLookupByLibrary.simpleMessage("感谢你使用和信任NoteX AI！"),
        "are_you_sure": MessageLookupByLibrary.simpleMessage("一次性优惠"),
        "ask_anything": MessageLookupByLibrary.simpleMessage("随便问..."),
        "assist_faster": MessageLookupByLibrary.simpleMessage("为更快帮助你："),
        "assistant": MessageLookupByLibrary.simpleMessage("助手"),
        "at_your_pace": MessageLookupByLibrary.simpleMessage("学习"),
        "audio": MessageLookupByLibrary.simpleMessage("音频"),
        "audio_file": MessageLookupByLibrary.simpleMessage("音频文件"),
        "audio_is_temporary":
            MessageLookupByLibrary.simpleMessage("*音频为临时文件-关闭前保存"),
        "audio_length_err":
            MessageLookupByLibrary.simpleMessage("音频文件超长，请上传较短文件。"),
        "audio_length_limit": MessageLookupByLibrary.simpleMessage("音频时长限制"),
        "audio_process_err":
            MessageLookupByLibrary.simpleMessage("无法处理音频文件，请尝试其他文件。"),
        "audio_recording_ai_notes_daily":
            MessageLookupByLibrary.simpleMessage("每天3次音频与录音AI笔记*"),
        "audio_to_ai_note": MessageLookupByLibrary.simpleMessage("音频转AI笔记"),
        "audio_upload_note": MessageLookupByLibrary.simpleMessage("音频上传"),
        "auto": MessageLookupByLibrary.simpleMessage("自动"),
        "auto_detect": MessageLookupByLibrary.simpleMessage("自动检测"),
        "auto_generate_slides":
            MessageLookupByLibrary.simpleMessage("立即生成引人注目的幻灯片"),
        "auto_renew_after_trial":
            MessageLookupByLibrary.simpleMessage("试用后自动续订，可随时取消"),
        "auto_renewable_after_trial":
            MessageLookupByLibrary.simpleMessage("试用后自动续订，可随时取消"),
        "auto_renewal": MessageLookupByLibrary.simpleMessage("自动续订，可随时取消"),
        "available_credits": MessageLookupByLibrary.simpleMessage("可用积分"),
        "available_transcript":
            MessageLookupByLibrary.simpleMessage("笔记创建成功后可查看转录！"),
        "back_content": MessageLookupByLibrary.simpleMessage("积分"),
        "background_style": MessageLookupByLibrary.simpleMessage("背景风格"),
        "balanced": MessageLookupByLibrary.simpleMessage("平衡"),
        "balanced_description": MessageLookupByLibrary.simpleMessage("主要观点带背景"),
        "basic": MessageLookupByLibrary.simpleMessage("基础计划"),
        "basic_features": MessageLookupByLibrary.simpleMessage("基础AI功能"),
        "beta": MessageLookupByLibrary.simpleMessage("测试版"),
        "between_concepts": MessageLookupByLibrary.simpleMessage("连接概念"),
        "black_friday_sale": MessageLookupByLibrary.simpleMessage("圣诞促销！"),
        "blurred_output_image":
            MessageLookupByLibrary.simpleMessage("风格生成失败！请选择其他风格或更换图片！"),
        "body_error_document_upload":
            MessageLookupByLibrary.simpleMessage("处理文档出错，请返回应用重试。"),
        "body_error_note_document":
            MessageLookupByLibrary.simpleMessage("处理文档出错，请返回应用重试。"),
        "body_error_note_recording":
            MessageLookupByLibrary.simpleMessage("处理录音出错，请返回应用重试。"),
        "body_error_note_upload":
            MessageLookupByLibrary.simpleMessage("处理上传音频出错，请返回应用重试。"),
        "body_error_note_web":
            MessageLookupByLibrary.simpleMessage("处理网页链接出错，请返回应用重试。"),
        "body_error_note_youtube":
            MessageLookupByLibrary.simpleMessage("处理YouTube链接出错，请返回应用重试。"),
        "body_success_note":
            MessageLookupByLibrary.simpleMessage("你的AI笔记已准备好查看。"),
        "bonus_credits_for_new_referred_friends_only":
            MessageLookupByLibrary.simpleMessage("仅限新推荐朋友的额外积分"),
        "boost_comprehension": MessageLookupByLibrary.simpleMessage("提升理解与记忆"),
        "boost_comprehension2": MessageLookupByLibrary.simpleMessage("提升理解"),
        "boost_flashcards_quizzes":
            MessageLookupByLibrary.simpleMessage("通过AI生成闪卡和测验提升理解"),
        "boost_knowledge": MessageLookupByLibrary.simpleMessage("连接"),
        "boost_knowledge_retention":
            MessageLookupByLibrary.simpleMessage("连接概念"),
        "both_you_friends_receive_usage_credits":
            MessageLookupByLibrary.simpleMessage("你和朋友都将获得使用积分。"),
        "brief_service_disruption": MessageLookupByLibrary.simpleMessage(
            "服务短暂中断，请稍后重试。在Discord获取实时状态更新！"),
        "business_uses": MessageLookupByLibrary.simpleMessage("商业用途"),
        "button_below":
            MessageLookupByLibrary.simpleMessage("下方按钮或选择\n特定内容输入类型开始"),
        "buy_one_forever": MessageLookupByLibrary.simpleMessage("一次购买，永久高效。"),
        "by_subscribing": MessageLookupByLibrary.simpleMessage("订阅即表示同意"),
        "by_taping_continue":
            MessageLookupByLibrary.simpleMessage("继续即表示同意我们的"),
        "by_tapping_started":
            MessageLookupByLibrary.simpleMessage("点击“开始”即表示同意我们的"),
        "camera": MessageLookupByLibrary.simpleMessage("相机"),
        "camera_access":
            MessageLookupByLibrary.simpleMessage("\"NoteX\" 想要访问相机"),
        "camera_permission": MessageLookupByLibrary.simpleMessage("相机访问权限"),
        "camera_permission_denied_details":
            MessageLookupByLibrary.simpleMessage("应用程序需要相机访问权限才能拍照。请在设置中授予权限。"),
        "can_improve": MessageLookupByLibrary.simpleMessage("我们可以改进什么？"),
        "cancel": MessageLookupByLibrary.simpleMessage("取消"),
        "cannot_create_pdf_file_from_image":
            MessageLookupByLibrary.simpleMessage("无法从图片创建PDF文件"),
        "cannot_extract_text_from_pdf":
            MessageLookupByLibrary.simpleMessage("无法读取文档。此文档可能为扫描件或仅含图片的PDF。"),
        "card": MessageLookupByLibrary.simpleMessage("卡片"),
        "card_count": MessageLookupByLibrary.simpleMessage("卡片数量"),
        "card_difficulty": MessageLookupByLibrary.simpleMessage("卡片难度"),
        "change": MessageLookupByLibrary.simpleMessage("更改"),
        "change_plan": MessageLookupByLibrary.simpleMessage("更改计划"),
        "chaos_into_clarity": MessageLookupByLibrary.simpleMessage("从混乱转为清晰"),
        "characters": MessageLookupByLibrary.simpleMessage("字符"),
        "chat_empty": MessageLookupByLibrary.simpleMessage("聊天为空"),
        "chat_topic_temporary_stored":
            MessageLookupByLibrary.simpleMessage("临时会话，使用\"保存聊天\"以保留"),
        "check_if_you":
            MessageLookupByLibrary.simpleMessage("确认是否登录正确的Google账户"),
        "check_update": MessageLookupByLibrary.simpleMessage("有新版本可用"),
        "child_detected":
            MessageLookupByLibrary.simpleMessage("检测到儿童，请上传其他图片。"),
        "choose_your_note": MessageLookupByLibrary.simpleMessage("选择你的NoteX"),
        "choose_your_note_experience":
            MessageLookupByLibrary.simpleMessage("选择你的NoteX体验"),
        "click_create_podcast":
            MessageLookupByLibrary.simpleMessage("点击“创建播客”将你的笔记转换为引人入胜的音频"),
        "click_create_short":
            MessageLookupByLibrary.simpleMessage("点击“创建短视频”将笔记转为引人入胜的短视频"),
        "click_create_slide":
            MessageLookupByLibrary.simpleMessage("创建幻灯片放映以将您的笔记显示为幻灯片"),
        "click_start_flashcard":
            MessageLookupByLibrary.simpleMessage("点击“创建闪卡”生成基于转录的闪卡"),
        "click_start_mindmap":
            MessageLookupByLibrary.simpleMessage("点击“创建思维导图”生成基于转录的思维导图"),
        "click_start_quiz":
            MessageLookupByLibrary.simpleMessage("点击“创建测验”生成基于转录的题目"),
        "click_to_flip": MessageLookupByLibrary.simpleMessage("点击翻转"),
        "coming_soon": MessageLookupByLibrary.simpleMessage("即将推出"),
        "community": MessageLookupByLibrary.simpleMessage("社区"),
        "community_feedback": MessageLookupByLibrary.simpleMessage("社区与反馈"),
        "comprehensive": MessageLookupByLibrary.simpleMessage("全面"),
        "comprehensive_description":
            MessageLookupByLibrary.simpleMessage("详细覆盖与支持点"),
        "congratulations": MessageLookupByLibrary.simpleMessage("恭喜！"),
        "connect_friends":
            MessageLookupByLibrary.simpleMessage("轻松导入朋友分享的笔记链接"),
        "connection_fail": MessageLookupByLibrary.simpleMessage("连接失败！"),
        "connection_timeout":
            MessageLookupByLibrary.simpleMessage("连接超时，请检查网络后重试。"),
        "contact_support": MessageLookupByLibrary.simpleMessage("联系支持"),
        "content_account_trial": m0,
        "content_button_flashcard":
            MessageLookupByLibrary.simpleMessage("创建闪卡"),
        "content_button_mindmap":
            MessageLookupByLibrary.simpleMessage("创建思维导图"),
        "content_button_quiz": MessageLookupByLibrary.simpleMessage("创建测验"),
        "content_button_summary": MessageLookupByLibrary.simpleMessage("生成总结"),
        "content_camera_access": MessageLookupByLibrary.simpleMessage(
            "NoteX 需要访问您的相机以捕获、识别和数字化图像中的文本"),
        "content_delete_note": MessageLookupByLibrary.simpleMessage("删除后无法恢复。"),
        "content_delete_note_detail":
            MessageLookupByLibrary.simpleMessage("您确定要删除这条笔记吗？"),
        "content_delete_reminder":
            MessageLookupByLibrary.simpleMessage("确定要删除此提醒？"),
        "content_discard_changes":
            MessageLookupByLibrary.simpleMessage("离开将停止录音并放弃所有更改。"),
        "content_discard_changes_image":
            MessageLookupByLibrary.simpleMessage("关闭将丢弃您拍摄的照片"),
        "content_discard_changes_note":
            MessageLookupByLibrary.simpleMessage("此操作将放弃所有更改，无法撤销。"),
        "content_discard_changes_reminder":
            MessageLookupByLibrary.simpleMessage("离开将关闭提醒通知并丢弃所有更改。"),
        "content_empty_flashcard":
            MessageLookupByLibrary.simpleMessage("会议结束后，自动总结将显示在此。"),
        "content_empty_quiz":
            MessageLookupByLibrary.simpleMessage("会议结束后，自动总结将显示在此。"),
        "content_hour": MessageLookupByLibrary.simpleMessage("内容转"),
        "content_hour_insight": MessageLookupByLibrary.simpleMessage("内容快速转洞察"),
        "content_minute_left": MessageLookupByLibrary.simpleMessage(
            "若录音超过本周免费时长，将本地保存，无AI转录和总结。升级Pro移除限制。"),
        "content_payment_successfully":
            MessageLookupByLibrary.simpleMessage("感谢购买，交易已成功处理。"),
        "content_quarter_01":
            MessageLookupByLibrary.simpleMessage("从录音、文件上传、YouTube链接生成无限AI笔记。"),
        "content_quarter_02":
            MessageLookupByLibrary.simpleMessage("无限AI聊天、思维导图、闪卡、测验、笔记分享。"),
        "content_save_changes":
            MessageLookupByLibrary.simpleMessage("此操作将保存所有更改，永久应用。"),
        "continue_3_day": MessageLookupByLibrary.simpleMessage("继续3天免费试用"),
        "continue_button": MessageLookupByLibrary.simpleMessage("继续"),
        "continue_with_apple": MessageLookupByLibrary.simpleMessage("用Apple继续"),
        "continue_with_email": MessageLookupByLibrary.simpleMessage("用邮箱继续"),
        "continue_with_google":
            MessageLookupByLibrary.simpleMessage("用Google继续"),
        "copied_to_clipboard": MessageLookupByLibrary.simpleMessage("已复制到剪贴板"),
        "copy": MessageLookupByLibrary.simpleMessage("复制"),
        "copy_your_referral_code":
            MessageLookupByLibrary.simpleMessage("复制你的推荐码。"),
        "correct": MessageLookupByLibrary.simpleMessage("正确"),
        "craft_visual_from_every_note":
            MessageLookupByLibrary.simpleMessage("将你的笔记转换为幻灯片"),
        "craft_visual_stories": MessageLookupByLibrary.simpleMessage("转换你的笔记"),
        "create": MessageLookupByLibrary.simpleMessage("创建"),
        "create_folder": MessageLookupByLibrary.simpleMessage("创建文件夹"),
        "create_lecture": MessageLookupByLibrary.simpleMessage("创建简明课堂总结"),
        "create_new_folder": MessageLookupByLibrary.simpleMessage("创建新文件夹"),
        "create_note_successfully":
            MessageLookupByLibrary.simpleMessage("笔记创建成功！"),
        "create_notes": MessageLookupByLibrary.simpleMessage("创建AI笔记中..."),
        "create_podcast": MessageLookupByLibrary.simpleMessage("创建播客"),
        "create_reminder": MessageLookupByLibrary.simpleMessage("创建提醒"),
        "create_select_a_language":
            MessageLookupByLibrary.simpleMessage("选择语言"),
        "create_short": MessageLookupByLibrary.simpleMessage("创建短视频"),
        "create_shorts": MessageLookupByLibrary.simpleMessage("创建短视频"),
        "create_slide": MessageLookupByLibrary.simpleMessage("创建幻灯片放映"),
        "creating_note": MessageLookupByLibrary.simpleMessage("创建笔记中..."),
        "creating_quiz": MessageLookupByLibrary.simpleMessage("正在生成测验问题"),
        "credit": MessageLookupByLibrary.simpleMessage("积分"),
        "credits": MessageLookupByLibrary.simpleMessage("积分"),
        "credits_can_be_used_to_create_notes_and_access_the_features_within_them":
            MessageLookupByLibrary.simpleMessage(
                "积分可用于创建笔记和访问其功能。若订阅到期，可用积分继续操作。"),
        "credits_earned": MessageLookupByLibrary.simpleMessage("已赚积分"),
        "credits_premium_features":
            MessageLookupByLibrary.simpleMessage("积分与高级功能！"),
        "credits_used": MessageLookupByLibrary.simpleMessage("已用积分"),
        "current_plan": MessageLookupByLibrary.simpleMessage("当前计划"),
        "custom_note_tabs": MessageLookupByLibrary.simpleMessage("自定义笔记标签页"),
        "customize_note_tabs": MessageLookupByLibrary.simpleMessage("自定义笔记标签页"),
        "customize_your_note_view":
            MessageLookupByLibrary.simpleMessage("自定义你的笔记视图"),
        "daily_10": MessageLookupByLibrary.simpleMessage("每天10次"),
        "daily_3": MessageLookupByLibrary.simpleMessage("每天3次"),
        "daily_5": MessageLookupByLibrary.simpleMessage("每天5次"),
        "daily_rewards_limit_reached":
            MessageLookupByLibrary.simpleMessage("每日奖励已达上限，明天再试！"),
        "daily_shorts_limit_reached":
            MessageLookupByLibrary.simpleMessage("每日短视频限制已达（测试版-早期访问）"),
        "daily_shorts_limit_reached_detail":
            MessageLookupByLibrary.simpleMessage(
                "今天短视频生成已用完。此测试功能有每日限制以确保服务稳定。\n明天再来创建更多AI短视频！"),
        "daily_slideshow_limit_reached":
            MessageLookupByLibrary.simpleMessage("今日的显示次数已达到上限"),
        "daily_slideshow_limit_reached_detail":
            MessageLookupByLibrary.simpleMessage(
                "你已经用完了今天所有的幻灯片生成次数。这个测试版功能有每日限制以确保稳定性能。明天再来创建更多由AI驱动的幻灯片演示！"),
        "dark": MessageLookupByLibrary.simpleMessage("暗色"),
        "data": MessageLookupByLibrary.simpleMessage("数据即时获取答案"),
        "day_free_trial_access_all_features":
            MessageLookupByLibrary.simpleMessage("7天免费试用所有功能，仅需"),
        "days": MessageLookupByLibrary.simpleMessage("天"),
        "db_err": MessageLookupByLibrary.simpleMessage("数据库错误，请稍后重试。"),
        "deals_left_at_this_price":
            MessageLookupByLibrary.simpleMessage("此价格的终身优惠名额剩余"),
        "decline_free_trial": MessageLookupByLibrary.simpleMessage("拒绝免费试用"),
        "default_error": MessageLookupByLibrary.simpleMessage("出错！请重试！"),
        "delete": MessageLookupByLibrary.simpleMessage("删除"),
        "delete_account": MessageLookupByLibrary.simpleMessage("删除账户"),
        "delete_account_detail":
            MessageLookupByLibrary.simpleMessage("此操作不可撤销，删除账户将永久移除：所有笔记和录音"),
        "delete_all_note": MessageLookupByLibrary.simpleMessage("删除文件夹内所有笔记"),
        "delete_folder": MessageLookupByLibrary.simpleMessage("删除文件夹"),
        "delete_note": MessageLookupByLibrary.simpleMessage("删除此笔记？"),
        "delete_note_item": MessageLookupByLibrary.simpleMessage("删除笔记"),
        "delete_recording": MessageLookupByLibrary.simpleMessage("删除录音"),
        "delete_recording_confirmation":
            MessageLookupByLibrary.simpleMessage("确定要删除"),
        "delete_recording_setting_confirmation":
            MessageLookupByLibrary.simpleMessage("该音频文件将永久从您的设备中删除。此操作无法撤消。"),
        "delete_reminder": MessageLookupByLibrary.simpleMessage("删除提醒？"),
        "delete_success": MessageLookupByLibrary.simpleMessage("笔记已成功删除。"),
        "delete_this_folder": MessageLookupByLibrary.simpleMessage("删除此文件夹？"),
        "delete_this_item": MessageLookupByLibrary.simpleMessage("删除此项？"),
        "deselect": MessageLookupByLibrary.simpleMessage("取消选择"),
        "detail_unlimited_ai_summaries":
            MessageLookupByLibrary.simpleMessage("从录音、音频、文档和YouTube视频生成无限AI笔记"),
        "developing_quizzes": MessageLookupByLibrary.simpleMessage("开发测验中..."),
        "discard": MessageLookupByLibrary.simpleMessage("放弃"),
        "discard_changes": MessageLookupByLibrary.simpleMessage("放弃更改？"),
        "dissatisfied":
            MessageLookupByLibrary.simpleMessage("感谢你的反馈，我们会努力改进你的下次体验！"),
        "doc": MessageLookupByLibrary.simpleMessage("文档"),
        "document": MessageLookupByLibrary.simpleMessage("上传文档（即将推出）"),
        "document_available":
            MessageLookupByLibrary.simpleMessage("文档将在成功创建笔记后可用！"),
        "document_exceed_limit":
            MessageLookupByLibrary.simpleMessage("文件超过20MB，请选择较小文件。"),
        "document_limit": MessageLookupByLibrary.simpleMessage("文档上传限制"),
        "document_limit_message":
            MessageLookupByLibrary.simpleMessage("免费用户每天可总结1个文档。"),
        "document_note": MessageLookupByLibrary.simpleMessage("文档笔记"),
        "document_tab": MessageLookupByLibrary.simpleMessage("文档"),
        "document_to_ai_note": MessageLookupByLibrary.simpleMessage("文档转AI笔记"),
        "document_type": MessageLookupByLibrary.simpleMessage(
            "支持文件类型：.pdf, .doc, .docx, .txt, .md"),
        "document_upload_note": MessageLookupByLibrary.simpleMessage("文档上传"),
        "document_webview_loading_message":
            MessageLookupByLibrary.simpleMessage("加载文档内容..."),
        "done_button_label": MessageLookupByLibrary.simpleMessage("完成"),
        "donotallow": MessageLookupByLibrary.simpleMessage("不允许"),
        "double_the_benefits": MessageLookupByLibrary.simpleMessage("双倍福利！"),
        "download_audio_file": MessageLookupByLibrary.simpleMessage("分享音频文件"),
        "download_sucess": MessageLookupByLibrary.simpleMessage("下载成功"),
        "duration": MessageLookupByLibrary.simpleMessage("时长"),
        "each_ai_note_generation_uses_1_credit":
            MessageLookupByLibrary.simpleMessage("每次AI笔记生成使用1积分"),
        "each_referral_earns": MessageLookupByLibrary.simpleMessage("每次推荐获得"),
        "early_access": MessageLookupByLibrary.simpleMessage("未来功能\n早期访问"),
        "early_supporters_exclusive_offer":
            MessageLookupByLibrary.simpleMessage("早期支持者专属优惠"),
        "easily_import_shared_note_link":
            MessageLookupByLibrary.simpleMessage("轻松导入朋友分享的笔记链接"),
        "easy": MessageLookupByLibrary.simpleMessage("简单"),
        "edit": MessageLookupByLibrary.simpleMessage("编辑"),
        "edit_folder": MessageLookupByLibrary.simpleMessage("编辑文件夹"),
        "edit_folder_name": MessageLookupByLibrary.simpleMessage("输入文件夹名称"),
        "edit_name": MessageLookupByLibrary.simpleMessage("编辑名称"),
        "edit_note": MessageLookupByLibrary.simpleMessage("编辑笔记"),
        "edit_notes": MessageLookupByLibrary.simpleMessage("编辑笔记"),
        "edit_reminder": MessageLookupByLibrary.simpleMessage("编辑提醒"),
        "edit_transcript": MessageLookupByLibrary.simpleMessage("编辑转录"),
        "edit_transcript_json_fail":
            MessageLookupByLibrary.simpleMessage("时间戳转录编辑失败，请重试。"),
        "edit_transcript_json_success":
            MessageLookupByLibrary.simpleMessage("时间戳转录编辑成功"),
        "email_invalid": MessageLookupByLibrary.simpleMessage("邮箱地址无效。"),
        "email_sent": MessageLookupByLibrary.simpleMessage("检查你的邮箱"),
        "email_sent_success":
            MessageLookupByLibrary.simpleMessage("我们已发送魔法链接，点击邮箱中的链接继续。"),
        "enable_free": MessageLookupByLibrary.simpleMessage("启用免费试用"),
        "enables_swap": MessageLookupByLibrary.simpleMessage("通过选择和交换实现图像重新排序"),
        "english": MessageLookupByLibrary.simpleMessage("英语"),
        "enter_card_count": MessageLookupByLibrary.simpleMessage("输入卡片数量"),
        "enter_email": MessageLookupByLibrary.simpleMessage("输入你的邮箱地址..."),
        "enter_feedback": MessageLookupByLibrary.simpleMessage("输入反馈"),
        "enter_folder_name": MessageLookupByLibrary.simpleMessage("输入文件夹名称"),
        "enter_new_name": MessageLookupByLibrary.simpleMessage("输入新名称"),
        "enter_quiz_count": MessageLookupByLibrary.simpleMessage("输入测验数量"),
        "enter_referral_code": MessageLookupByLibrary.simpleMessage("输入推荐码"),
        "enter_slide_count": MessageLookupByLibrary.simpleMessage("输入幻灯片数量"),
        "enter_title": MessageLookupByLibrary.simpleMessage("输入标题"),
        "enter_valid_email": MessageLookupByLibrary.simpleMessage("请输入有效邮箱地址"),
        "error": MessageLookupByLibrary.simpleMessage("错误"),
        "error_connection": MessageLookupByLibrary.simpleMessage("连接出错，请重试"),
        "error_convert_image": MessageLookupByLibrary.simpleMessage("图像转换失败"),
        "error_logging_in": MessageLookupByLibrary.simpleMessage("连接到互联网"),
        "esc": MessageLookupByLibrary.simpleMessage("退出"),
        "essential": MessageLookupByLibrary.simpleMessage("基础版"),
        "essential_lifetime": MessageLookupByLibrary.simpleMessage("基础终身版"),
        "essential_lifetime_access":
            MessageLookupByLibrary.simpleMessage("基础终身访问"),
        "evening_content": MessageLookupByLibrary.simpleMessage("反思、记录、成长"),
        "evening_content_2": MessageLookupByLibrary.simpleMessage("今日洞察已保存"),
        "evening_content_3": MessageLookupByLibrary.simpleMessage("明天始于今日笔记"),
        "evening_content_4": MessageLookupByLibrary.simpleMessage("想法整理，心安理得"),
        "evening_content_5": MessageLookupByLibrary.simpleMessage("现在保存，未来感谢"),
        "evening_content_6": MessageLookupByLibrary.simpleMessage("进展已保留"),
        "every_note_you_take": MessageLookupByLibrary.simpleMessage("为幻灯片"),
        "experience": MessageLookupByLibrary.simpleMessage("体验"),
        "export": MessageLookupByLibrary.simpleMessage("导出"),
        "export_as": MessageLookupByLibrary.simpleMessage("导出为"),
        "export_audio": MessageLookupByLibrary.simpleMessage("导出音频文件"),
        "export_failed": MessageLookupByLibrary.simpleMessage("导出失败，请稍后重试。"),
        "export_flashcard": MessageLookupByLibrary.simpleMessage("导出闪卡"),
        "export_mind_map": MessageLookupByLibrary.simpleMessage("导出思维导图为"),
        "export_pdf": MessageLookupByLibrary.simpleMessage("导出总结"),
        "export_quiz": MessageLookupByLibrary.simpleMessage("导出测验"),
        "export_to_pdf_share_notes":
            MessageLookupByLibrary.simpleMessage("导出PDF与分享笔记"),
        "export_transcript": MessageLookupByLibrary.simpleMessage("导出转录"),
        "extracting_text_from_document":
            MessageLookupByLibrary.simpleMessage("从文档中提取文本"),
        "fail": MessageLookupByLibrary.simpleMessage("失败"),
        "fail_create_pdf": MessageLookupByLibrary.simpleMessage("创建PDF文件失败"),
        "fail_to_load_document":
            MessageLookupByLibrary.simpleMessage("加载文档失败！"),
        "fail_to_load_video": MessageLookupByLibrary.simpleMessage("加载视频失败"),
        "failed_get_anonymous_user":
            MessageLookupByLibrary.simpleMessage("获取匿名用户失败"),
        "failed_to_delete_recording":
            MessageLookupByLibrary.simpleMessage("删除录音失败"),
        "failed_to_load_slideshow":
            MessageLookupByLibrary.simpleMessage("无法从系统加载幻灯片集。请再试一次以获得更好的体验。"),
        "failed_to_save_file": MessageLookupByLibrary.simpleMessage("保存文件失败"),
        "feedback": MessageLookupByLibrary.simpleMessage("反馈"),
        "file_import": MessageLookupByLibrary.simpleMessage("文件导入"),
        "file_save_success": MessageLookupByLibrary.simpleMessage("文件保存成功"),
        "file_size_err": MessageLookupByLibrary.simpleMessage("文件超限，请上传较小文件。"),
        "filter": MessageLookupByLibrary.simpleMessage("筛选"),
        "filter_and_sort": MessageLookupByLibrary.simpleMessage("筛选与排序"),
        "finalizing": MessageLookupByLibrary.simpleMessage("完成中..."),
        "find_and_replace": MessageLookupByLibrary.simpleMessage("查找与替换"),
        "flash_card_gen_success":
            MessageLookupByLibrary.simpleMessage("闪卡生成成功"),
        "flash_card_iap": MessageLookupByLibrary.simpleMessage("闪卡集"),
        "flashcard": MessageLookupByLibrary.simpleMessage("闪卡"),
        "flashcard_set_not_found":
            MessageLookupByLibrary.simpleMessage("未找到抽认卡集"),
        "flashcard_sets": MessageLookupByLibrary.simpleMessage("闪卡集"),
        "flashcards": MessageLookupByLibrary.simpleMessage("闪卡"),
        "flashcards_for": MessageLookupByLibrary.simpleMessage("的闪卡"),
        "focus_on": MessageLookupByLibrary.simpleMessage("专注重要之事"),
        "folder": MessageLookupByLibrary.simpleMessage("文件夹"),
        "follow_steps_to_get_rewarded":
            MessageLookupByLibrary.simpleMessage("按以下步骤获取奖励"),
        "for_unlimited_experiences":
            MessageLookupByLibrary.simpleMessage("享受无限体验。"),
        "free": MessageLookupByLibrary.simpleMessage("免费"),
        "free_30_minutes": MessageLookupByLibrary.simpleMessage("免费：每文件30分钟"),
        "free_messages": MessageLookupByLibrary.simpleMessage("免费消息"),
        "free_recording_limit": MessageLookupByLibrary.simpleMessage("免费录音限制"),
        "free_recording_limit_details":
            MessageLookupByLibrary.simpleMessage("本周剩余%s分钟免费转录和AI总结。"),
        "free_trial": MessageLookupByLibrary.simpleMessage("免费试用"),
        "free_updates": MessageLookupByLibrary.simpleMessage("终身免费更新与增强"),
        "free_usage": MessageLookupByLibrary.simpleMessage("每周免费使用"),
        "free_user_audio":
            MessageLookupByLibrary.simpleMessage("免费用户可转录和总结最长30分钟的音频"),
        "free_user_can": MessageLookupByLibrary.simpleMessage(
            "免费用户每天可总结1个YouTube视频（最多30分钟）。"),
        "friendly": MessageLookupByLibrary.simpleMessage("友好"),
        "friendly_description": MessageLookupByLibrary.simpleMessage("对话式带表情"),
        "front_content": MessageLookupByLibrary.simpleMessage("你获得"),
        "future_features": MessageLookupByLibrary.simpleMessage("未来功能可能有使用限额"),
        "gen_ai": MessageLookupByLibrary.simpleMessage("生成AI中..."),
        "gen_ai_voice": MessageLookupByLibrary.simpleMessage("正在生成 AI 语音"),
        "gen_quiz_bgr": MessageLookupByLibrary.simpleMessage("正在生成测验背景"),
        "generate_audio": MessageLookupByLibrary.simpleMessage("生成音频"),
        "generate_content":
            MessageLookupByLibrary.simpleMessage("生成YouTube内容的智能总结"),
        "generate_note_fail": MessageLookupByLibrary.simpleMessage("生成AI笔记失败！"),
        "generate_shorts_step_1":
            MessageLookupByLibrary.simpleMessage("打造你的故事..."),
        "generate_shorts_step_2":
            MessageLookupByLibrary.simpleMessage("添加完美语音..."),
        "generate_shorts_step_3":
            MessageLookupByLibrary.simpleMessage("让它惊艳！这视频值得分享 #NoteXAI"),
        "generate_shorts_study_guides":
            MessageLookupByLibrary.simpleMessage("生成短视频与学习指南"),
        "generate_transcript_notes":
            MessageLookupByLibrary.simpleMessage("我们将生成转录、笔记和学习指南"),
        "generate_video": MessageLookupByLibrary.simpleMessage("生成视频"),
        "generating_ai_note": MessageLookupByLibrary.simpleMessage("生成AI笔记"),
        "generating_summary":
            MessageLookupByLibrary.simpleMessage("生成AI总结中..."),
        "get_fail": MessageLookupByLibrary.simpleMessage("获取测验/闪卡/思维导图失败，请重试！"),
        "get_more_done": MessageLookupByLibrary.simpleMessage("高效完成，"),
        "get_more_done_stay_on_track":
            MessageLookupByLibrary.simpleMessage("高效完成，保持正轨"),
        "get_now": MessageLookupByLibrary.simpleMessage("立即获取"),
        "get_offer_now": MessageLookupByLibrary.simpleMessage("立即获取优惠"),
        "get_start": MessageLookupByLibrary.simpleMessage("开始"),
        "go_back": MessageLookupByLibrary.simpleMessage("返回"),
        "go_email": MessageLookupByLibrary.simpleMessage("前往邮件"),
        "go_pro": MessageLookupByLibrary.simpleMessage("升级Pro"),
        "go_unlimited": MessageLookupByLibrary.simpleMessage("无限畅享！"),
        "good_afternoon": MessageLookupByLibrary.simpleMessage("下午好！"),
        "good_evening": MessageLookupByLibrary.simpleMessage("晚上好！"),
        "good_morning": MessageLookupByLibrary.simpleMessage("早上好！"),
        "got_it": MessageLookupByLibrary.simpleMessage("明白了！"),
        "hard": MessageLookupByLibrary.simpleMessage("困难"),
        "hello_welcome": MessageLookupByLibrary.simpleMessage("欢迎回来 👋"),
        "help_legal": MessageLookupByLibrary.simpleMessage("帮助与法律"),
        "help_us_grow": MessageLookupByLibrary.simpleMessage("帮助我们成长！"),
        "hi": MessageLookupByLibrary.simpleMessage("嗨"),
        "hope_enjoy_app":
            MessageLookupByLibrary.simpleMessage("希望你喜欢我们的应用，感谢支持！"),
        "hours": MessageLookupByLibrary.simpleMessage("小时"),
        "how_will_you_use_notex":
            MessageLookupByLibrary.simpleMessage("你将如何使用NoteX？"),
        "http_failed": MessageLookupByLibrary.simpleMessage("HTTP请求失败，请稍后重试。"),
        "idea1":
            MessageLookupByLibrary.simpleMessage("如未登录，请使用Google或Apple账户登录"),
        "idea2": MessageLookupByLibrary.simpleMessage("简述问题"),
        "idea3": MessageLookupByLibrary.simpleMessage("提供相关细节（设备、系统版本）"),
        "idea4": MessageLookupByLibrary.simpleMessage("说明问题开始的时间"),
        "idea5": MessageLookupByLibrary.simpleMessage("直接发邮件至"),
        "image": MessageLookupByLibrary.simpleMessage("图片"),
        "image_jpeg": MessageLookupByLibrary.simpleMessage("图片 (.jpeg)"),
        "image_png": MessageLookupByLibrary.simpleMessage("图片 (.png)"),
        "image_quality_too_low":
            MessageLookupByLibrary.simpleMessage("图片质量过低，请使用更高清晰度的图片！"),
        "image_too_large":
            MessageLookupByLibrary.simpleMessage("图片过大，请上传小于10MB的图片。"),
        "images_have_been_uploaded": m1,
        "import_note_links": MessageLookupByLibrary.simpleMessage("导入笔记链接"),
        "import_notes": MessageLookupByLibrary.simpleMessage("导入分享的笔记"),
        "improve_responses":
            MessageLookupByLibrary.simpleMessage("你的回答将帮助我们优化"),
        "initializing_camera":
            MessageLookupByLibrary.simpleMessage("正在初始化相机..."),
        "insight_instantly": MessageLookupByLibrary.simpleMessage("内容即时转洞察"),
        "insights_instantly": MessageLookupByLibrary.simpleMessage("即时洞察"),
        "instant_answers_from_your":
            MessageLookupByLibrary.simpleMessage("从你的"),
        "instant_answers_from_your_meeting_data":
            MessageLookupByLibrary.simpleMessage("从会议数据即时获取答案"),
        "instant_answers_meeting":
            MessageLookupByLibrary.simpleMessage("从你的会议"),
        "instantly": MessageLookupByLibrary.simpleMessage("即时"),
        "interactive_ai_flashcards":
            MessageLookupByLibrary.simpleMessage("交互式AI思维导图"),
        "interactive_flash": MessageLookupByLibrary.simpleMessage("交互式闪卡"),
        "interactive_flashcards":
            MessageLookupByLibrary.simpleMessage("无限交互式闪卡、思维导图"),
        "interactive_flashcards_quiz":
            MessageLookupByLibrary.simpleMessage("交互式闪卡与测验"),
        "introduce_guidance": MessageLookupByLibrary.simpleMessage(
            "感谢你的联系。为更有效地调查和解决问题，请按以下步骤操作："),
        "introduce_guidance2": MessageLookupByLibrary.simpleMessage(
            "我们理解遇到问题时的挫败感，特别是涉及重要笔记或录音时。我们的支持团队通常在12小时内解决问题。"),
        "inv_audio": MessageLookupByLibrary.simpleMessage("无效音频文件，请上传支持的音频格式。"),
        "inv_yt_url":
            MessageLookupByLibrary.simpleMessage("无效YouTube链接，请提供有效链接。"),
        "invalid_code": MessageLookupByLibrary.simpleMessage("无效码，请重试。"),
        "invalid_file_type":
            MessageLookupByLibrary.simpleMessage("文件格式错误，请重新上传。"),
        "invalid_token": MessageLookupByLibrary.simpleMessage("无效令牌"),
        "invite_friends": MessageLookupByLibrary.simpleMessage("邀请朋友，双方均获"),
        "join_discord": MessageLookupByLibrary.simpleMessage("加入Discord"),
        "join_noteX_ai_lets_level_up_together":
            MessageLookupByLibrary.simpleMessage("加入NoteX AI，一起升级！"),
        "language": MessageLookupByLibrary.simpleMessage("语言"),
        "language_tip": MessageLookupByLibrary.simpleMessage("选择音频主要语言以获得最佳效果"),
        "language_tip_1":
            MessageLookupByLibrary.simpleMessage("选择主要语言以获得最佳转录效果"),
        "language_tip_2": MessageLookupByLibrary.simpleMessage("混合对话请选多语言"),
        "language_tip_3":
            MessageLookupByLibrary.simpleMessage("通话将自动暂停录音，返回应用继续"),
        "latest_ai_models": MessageLookupByLibrary.simpleMessage("最新AI模型"),
        "learn_faster_through": MessageLookupByLibrary.simpleMessage("通过"),
        "learn_faster_through_active_recall":
            MessageLookupByLibrary.simpleMessage("通过主动回忆更快学习"),
        "learn_smart": MessageLookupByLibrary.simpleMessage("智能学习"),
        "learn_unlimited": MessageLookupByLibrary.simpleMessage("智能学习，无限畅享！"),
        "lecture_notes_study_materials":
            MessageLookupByLibrary.simpleMessage("课堂笔记与学习材料"),
        "let_ai_handle": MessageLookupByLibrary.simpleMessage("让AI处理细节"),
        "let_note_ai": MessageLookupByLibrary.simpleMessage("让NoteX AI将信息"),
        "let_start": MessageLookupByLibrary.simpleMessage("开始吧"),
        "lets_create_your_first_ai_note":
            MessageLookupByLibrary.simpleMessage("创建你的首个AI笔记！"),
        "lifetime": MessageLookupByLibrary.simpleMessage("终身计划"),
        "lifetime_pro_access_level_up_together":
            MessageLookupByLibrary.simpleMessage("一起升级 ✨"),
        "lifetime_setting": MessageLookupByLibrary.simpleMessage("终身"),
        "lifetime_spots_remaining":
            MessageLookupByLibrary.simpleMessage("剩余终身名额"),
        "light": MessageLookupByLibrary.simpleMessage("亮色"),
        "limited_notes": MessageLookupByLibrary.simpleMessage("每日笔记数量有限"),
        "limited_offer": MessageLookupByLibrary.simpleMessage("限时优惠"),
        "limited_time": MessageLookupByLibrary.simpleMessage("限时优惠"),
        "limited_time_02": MessageLookupByLibrary.simpleMessage("限时优惠"),
        "link": MessageLookupByLibrary.simpleMessage("链接"),
        "link_error": MessageLookupByLibrary.simpleMessage("链接错误"),
        "link_expired": MessageLookupByLibrary.simpleMessage("邮箱链接已过期。"),
        "link_invalid":
            MessageLookupByLibrary.simpleMessage("链接无效、已过期或已使用，请请求新链接重试。"),
        "loading": MessageLookupByLibrary.simpleMessage("加载中"),
        "loading_content": MessageLookupByLibrary.simpleMessage("加载内容..."),
        "local_recording": MessageLookupByLibrary.simpleMessage("智能录音"),
        "login_failed": MessageLookupByLibrary.simpleMessage("登录失败。"),
        "login_info_1": MessageLookupByLibrary.simpleMessage("在任何设备上访问笔记"),
        "login_info_2": MessageLookupByLibrary.simpleMessage("AWS提供企业级安全"),
        "login_info_3": MessageLookupByLibrary.simpleMessage("你的数据保持私密"),
        "login_info_4":
            MessageLookupByLibrary.simpleMessage("在notexapp.com上访问网页版"),
        "login_success": MessageLookupByLibrary.simpleMessage("登录成功！"),
        "login_title": MessageLookupByLibrary.simpleMessage("随时随地提升效率！"),
        "login_title_2": MessageLookupByLibrary.simpleMessage("NoteX 2.0介绍"),
        "login_unsuccessful":
            MessageLookupByLibrary.simpleMessage("登录失败，请重试或更换登录方式。"),
        "logout": MessageLookupByLibrary.simpleMessage("退出登录"),
        "logout_detail": MessageLookupByLibrary.simpleMessage("确定要退出登录？"),
        "logout_question_mark": MessageLookupByLibrary.simpleMessage("退出登录？"),
        "loved_by": MessageLookupByLibrary.simpleMessage("深受"),
        "make_the_interface_feel_more_like_you":
            MessageLookupByLibrary.simpleMessage(
                "让界面更符合你的风格 — 主题、字体和布局偏好尽在指尖。"),
        "making_amazing":
            MessageLookupByLibrary.simpleMessage("让它看起来惊艳！这个测验视频值得分享 #NoteXAI"),
        "manage_recordings": MessageLookupByLibrary.simpleMessage("管理录音"),
        "map_all_together": MessageLookupByLibrary.simpleMessage("正在整合所有内容"),
        "markdown_md": MessageLookupByLibrary.simpleMessage("Markdown (.md)"),
        "max_30": MessageLookupByLibrary.simpleMessage("最多30个问题。"),
        "max_30_cards_per_set":
            MessageLookupByLibrary.simpleMessage("每集最多30张卡"),
        "max_30_quiz_sets": MessageLookupByLibrary.simpleMessage("每组最多30个问题"),
        "max_3_flashcard_sets": MessageLookupByLibrary.simpleMessage("最多3个闪卡集"),
        "max_3_quiz_sets": MessageLookupByLibrary.simpleMessage("最多3个测验集"),
        "max_60_min_per_file":
            MessageLookupByLibrary.simpleMessage("* 每文件最长60分钟"),
        "max_ai": MessageLookupByLibrary.simpleMessage("最大AI转录："),
        "maybe_later": MessageLookupByLibrary.simpleMessage("稍后"),
        "medium": MessageLookupByLibrary.simpleMessage("中等"),
        "meeting_data": MessageLookupByLibrary.simpleMessage("数据即时获取答案"),
        "migrating_your_notes":
            MessageLookupByLibrary.simpleMessage("迁移你的笔记..."),
        "migration_complete": MessageLookupByLibrary.simpleMessage("迁移完成！"),
        "mind_map": MessageLookupByLibrary.simpleMessage("思维导图"),
        "mind_map_gen_success":
            MessageLookupByLibrary.simpleMessage("思维导图生成成功"),
        "mind_map_iap": MessageLookupByLibrary.simpleMessage("思维导图"),
        "mind_map_study": MessageLookupByLibrary.simpleMessage("思维导图，\n学习指南"),
        "minute_60_per_file":
            MessageLookupByLibrary.simpleMessage("基础版：每文件60分钟"),
        "minute_free": MessageLookupByLibrary.simpleMessage(
            "本周30分钟免费转录和AI总结已用完。升级Pro无限使用或下周重置。"),
        "minutes": MessageLookupByLibrary.simpleMessage("分钟"),
        "minutes_free_left": MessageLookupByLibrary.simpleMessage("剩余免费分钟"),
        "minutes_remaining": MessageLookupByLibrary.simpleMessage("剩余分钟"),
        "mixed": MessageLookupByLibrary.simpleMessage("混合"),
        "month": MessageLookupByLibrary.simpleMessage("月"),
        "monthly": MessageLookupByLibrary.simpleMessage("月付"),
        "more_summarize": MessageLookupByLibrary.simpleMessage("总结会议、播客、教程等"),
        "morning_content": MessageLookupByLibrary.simpleMessage("捕捉今日灵感"),
        "morning_content_2": MessageLookupByLibrary.simpleMessage("清晰头脑，清晰路径"),
        "morning_content_3": MessageLookupByLibrary.simpleMessage("今日笔记塑造明天"),
        "morning_content_4": MessageLookupByLibrary.simpleMessage("第一想法，最佳想法"),
        "morning_content_5": MessageLookupByLibrary.simpleMessage("以清晰开始"),
        "morning_content_6": MessageLookupByLibrary.simpleMessage("值得保留的创意"),
        "most_popular": MessageLookupByLibrary.simpleMessage("最受欢迎"),
        "multi_language": MessageLookupByLibrary.simpleMessage("多语言"),
        "multiple_people_detected":
            MessageLookupByLibrary.simpleMessage("检测到多人，请上传单人图片！"),
        "multiply_knowledge_with_friends":
            MessageLookupByLibrary.simpleMessage("与朋友共享知识"),
        "my_notes": MessageLookupByLibrary.simpleMessage("我的笔记"),
        "name": MessageLookupByLibrary.simpleMessage("名称"),
        "network_error": MessageLookupByLibrary.simpleMessage("网络错误，请检查网络后重试。"),
        "neutral": MessageLookupByLibrary.simpleMessage("中立"),
        "neutral_description":
            MessageLookupByLibrary.simpleMessage("直截了当，事实呈现"),
        "new_new": MessageLookupByLibrary.simpleMessage("新的"),
        "new_note": MessageLookupByLibrary.simpleMessage("新笔记"),
        "new_recording": MessageLookupByLibrary.simpleMessage("新录音 - "),
        "newest_first": MessageLookupByLibrary.simpleMessage("最新优先"),
        "next_bill_date": m2,
        "no": MessageLookupByLibrary.simpleMessage("否"),
        "no_generated": MessageLookupByLibrary.simpleMessage("未生成测验/闪卡，点击创建！"),
        "no_input":
            MessageLookupByLibrary.simpleMessage("未提供输入，请上传音频或输入YouTube链接。"),
        "no_internet": MessageLookupByLibrary.simpleMessage("无网络连接"),
        "no_internet_connection":
            MessageLookupByLibrary.simpleMessage("无网络连接！"),
        "no_notes_found":
            MessageLookupByLibrary.simpleMessage("此筛选下无笔记。\n请重置筛选条件"),
        "no_notes_in_folder": MessageLookupByLibrary.simpleMessage("此文件夹中无笔记。"),
        "no_payment_now": MessageLookupByLibrary.simpleMessage("✓ 现在无需付款"),
        "no_person_detected":
            MessageLookupByLibrary.simpleMessage("未检测到人，请上传包含人物的图片！"),
        "no_recording_credit":
            MessageLookupByLibrary.simpleMessage("录音额度不足，请升级计划。"),
        "no_recordings": MessageLookupByLibrary.simpleMessage("无录音"),
        "no_results_found": MessageLookupByLibrary.simpleMessage("未找到结果"),
        "no_speech_detected": MessageLookupByLibrary.simpleMessage("未检测到语音"),
        "no_summary": MessageLookupByLibrary.simpleMessage("此笔记无总结。"),
        "no_transcript": MessageLookupByLibrary.simpleMessage("此笔记无转录。"),
        "no_upload_credit":
            MessageLookupByLibrary.simpleMessage("上传额度不足，请升级计划。"),
        "no_url_provided": MessageLookupByLibrary.simpleMessage("未提供导出URL。"),
        "no_voice_available": MessageLookupByLibrary.simpleMessage("无可用语音"),
        "not_found_audio": MessageLookupByLibrary.simpleMessage("未找到音频文件"),
        "not_open_mail": MessageLookupByLibrary.simpleMessage("无法打开邮件！"),
        "not_open_web": MessageLookupByLibrary.simpleMessage("无法打开网页！"),
        "not_summarized_note":
            MessageLookupByLibrary.simpleMessage("缺少总结！点击按钮让AI开始工作👇"),
        "note": MessageLookupByLibrary.simpleMessage("笔记"),
        "noteX": MessageLookupByLibrary.simpleMessage("NoteX"),
        "noteX_lifetime_essential":
            MessageLookupByLibrary.simpleMessage("NoteX基础终身版"),
        "noteX_pro_lifetime":
            MessageLookupByLibrary.simpleMessage("NoteX Pro终身版"),
        "note_404": MessageLookupByLibrary.simpleMessage("未找到笔记，请检查笔记ID后重试。"),
        "note_not_ready":
            MessageLookupByLibrary.simpleMessage("笔记未准备好导出，请等待处理完成。"),
        "note_reminders": MessageLookupByLibrary.simpleMessage("笔记提醒"),
        "note_sharing": MessageLookupByLibrary.simpleMessage("笔记分享"),
        "note_tabs": MessageLookupByLibrary.simpleMessage("笔记标签页"),
        "note_taker": MessageLookupByLibrary.simpleMessage("#1 AI笔记工具"),
        "notes": MessageLookupByLibrary.simpleMessage("笔记"),
        "notex_empty": MessageLookupByLibrary.simpleMessage("NoteX为空"),
        "notex_experience": MessageLookupByLibrary.simpleMessage("你的NoteX体验"),
        "nothing_restore": MessageLookupByLibrary.simpleMessage("无内容可恢复"),
        "noti_default_description":
            MessageLookupByLibrary.simpleMessage("准备好开始录音吧！🚀"),
        "noti_default_title": MessageLookupByLibrary.simpleMessage("录音时间到"),
        "noti_req_description":
            MessageLookupByLibrary.simpleMessage("通知可能包括提醒、声音和图标徽章，可在设置中配置。"),
        "noti_req_title": MessageLookupByLibrary.simpleMessage("‘NoteX’想发送通知"),
        "notifications": MessageLookupByLibrary.simpleMessage("通知"),
        "notifications_note_created":
            MessageLookupByLibrary.simpleMessage("你的笔记已成功创建"),
        "notifications_note_ready":
            MessageLookupByLibrary.simpleMessage("笔记准备好时通知"),
        "nova_ai_assistant_mind_mapping":
            MessageLookupByLibrary.simpleMessage("Nova AI助手与思维导图"),
        "nova_ai_chat": MessageLookupByLibrary.simpleMessage("Nova AI聊天"),
        "nova_chat": MessageLookupByLibrary.simpleMessage("Nova聊天"),
        "number_of_flash": MessageLookupByLibrary.simpleMessage("创建闪卡"),
        "number_of_quiz": MessageLookupByLibrary.simpleMessage("测验数量"),
        "of_index": MessageLookupByLibrary.simpleMessage("共"),
        "of_user": MessageLookupByLibrary.simpleMessage("用户喜爱"),
        "offer_expires": MessageLookupByLibrary.simpleMessage("优惠截至"),
        "ok": MessageLookupByLibrary.simpleMessage("确定"),
        "oldest_first": MessageLookupByLibrary.simpleMessage("最旧优先"),
        "on_track": MessageLookupByLibrary.simpleMessage("保持正轨"),
        "on_your_android": MessageLookupByLibrary.simpleMessage(
            "在Android手机或平板上，打开Google Play商店"),
        "onboarding_generate_audio_video_content":
            MessageLookupByLibrary.simpleMessage("将笔记转为"),
        "onboarding_generate_audio_video_full_content":
            MessageLookupByLibrary.simpleMessage("将笔记转为引人入胜的内容"),
        "onboarding_generate_audio_video_sub_content":
            MessageLookupByLibrary.simpleMessage("引人入胜的内容"),
        "onboarding_generate_audio_video_title":
            MessageLookupByLibrary.simpleMessage("生成音频与视频"),
        "once_in_a_lifetime_offer":
            MessageLookupByLibrary.simpleMessage("一生一次的优惠"),
        "one_per_day": MessageLookupByLibrary.simpleMessage("每天1次"),
        "one_time_payment": MessageLookupByLibrary.simpleMessage("一次性付款"),
        "only": MessageLookupByLibrary.simpleMessage("仅"),
        "only_today": MessageLookupByLibrary.simpleMessage("仅限今日"),
        "only_you_can_view_this_note":
            MessageLookupByLibrary.simpleMessage("仅你可查看此笔记"),
        "oops_something_went_wrong":
            MessageLookupByLibrary.simpleMessage("糟糕！\n出现了问题"),
        "open_now": MessageLookupByLibrary.simpleMessage("立即开启"),
        "open_youtube": MessageLookupByLibrary.simpleMessage("打开YouTube"),
        "opportunities": MessageLookupByLibrary.simpleMessage("机会"),
        "or": MessageLookupByLibrary.simpleMessage("或"),
        "or_upper": MessageLookupByLibrary.simpleMessage("或"),
        "organize_assign_action_items":
            MessageLookupByLibrary.simpleMessage("组织与分配行动项"),
        "organize_assign_items":
            MessageLookupByLibrary.simpleMessage("组织与分配行动项"),
        "others": MessageLookupByLibrary.simpleMessage("其他"),
        "output_language": MessageLookupByLibrary.simpleMessage("输出语言"),
        "pace": MessageLookupByLibrary.simpleMessage("节奏"),
        "paste": MessageLookupByLibrary.simpleMessage("粘贴"),
        "paste_url_here": MessageLookupByLibrary.simpleMessage("在此粘贴URL"),
        "paste_youtube_link":
            MessageLookupByLibrary.simpleMessage("粘贴YouTube链接"),
        "payment_required": MessageLookupByLibrary.simpleMessage("免费额度已用尽"),
        "payment_successfully": MessageLookupByLibrary.simpleMessage("支付成功"),
        "pdf_export": MessageLookupByLibrary.simpleMessage("PDF导出"),
        "pdf_pdf": MessageLookupByLibrary.simpleMessage("PDF (.pdf)"),
        "per_year": MessageLookupByLibrary.simpleMessage("每年"),
        "period": MessageLookupByLibrary.simpleMessage("%s分钟"),
        "personalized_learning": MessageLookupByLibrary.simpleMessage("按你的节奏"),
        "personalized_learning_at":
            MessageLookupByLibrary.simpleMessage("个性化学习"),
        "photos": MessageLookupByLibrary.simpleMessage("照片"),
        "pick_specific_language":
            MessageLookupByLibrary.simpleMessage("选择音频中的主要语言以提高转录准确性"),
        "plan": MessageLookupByLibrary.simpleMessage("计划"),
        "please_select_a_language":
            MessageLookupByLibrary.simpleMessage("请先选择语言以准确转录音频！"),
        "please_select_a_youtube_language":
            MessageLookupByLibrary.simpleMessage("请选择总结语言，这是总结输出的语言"),
        "please_try_again": MessageLookupByLibrary.simpleMessage("请重试"),
        "please_wait": MessageLookupByLibrary.simpleMessage("请稍等"),
        "podcast": MessageLookupByLibrary.simpleMessage("播客"),
        "podcast_name": MessageLookupByLibrary.simpleMessage("播客名称"),
        "policy": MessageLookupByLibrary.simpleMessage("政策"),
        "premium_features": MessageLookupByLibrary.simpleMessage("试用高级功能感受差异"),
        "preparing_video": MessageLookupByLibrary.simpleMessage("准备视频..."),
        "press_back_again_to_exit":
            MessageLookupByLibrary.simpleMessage("再次按返回退出！"),
        "preview_only":
            MessageLookupByLibrary.simpleMessage("仅为预览，背景将根据内容由 AI 生成"),
        "priority_processing": MessageLookupByLibrary.simpleMessage("优先处理"),
        "privacy_policy": MessageLookupByLibrary.simpleMessage("隐私政策"),
        "private": MessageLookupByLibrary.simpleMessage("私密"),
        "pro": MessageLookupByLibrary.simpleMessage("Pro计划"),
        "proLite": MessageLookupByLibrary.simpleMessage("Pro Lite"),
        "pro_01": MessageLookupByLibrary.simpleMessage("Pro"),
        "pro_6_hours": MessageLookupByLibrary.simpleMessage("Pro：每文件6小时"),
        "pro_access_life_time": MessageLookupByLibrary.simpleMessage("终身专业版"),
        "pro_lifetime": MessageLookupByLibrary.simpleMessage("Pro终身版"),
        "process_your_document":
            MessageLookupByLibrary.simpleMessage("处理你的文档..."),
        "process_your_text": MessageLookupByLibrary.simpleMessage("处理你的文本..."),
        "processing_content": MessageLookupByLibrary.simpleMessage("处理内容..."),
        "processing_file": MessageLookupByLibrary.simpleMessage("处理文件中..."),
        "processing_image": MessageLookupByLibrary.simpleMessage("处理图片中..."),
        "processing_note_audio_file":
            MessageLookupByLibrary.simpleMessage("处理你的音频..."),
        "processing_note_recording":
            MessageLookupByLibrary.simpleMessage("处理你的录音..."),
        "processing_note_youtube":
            MessageLookupByLibrary.simpleMessage("处理YouTube视频..."),
        "processing_web_link": MessageLookupByLibrary.simpleMessage("处理网页链接"),
        "producing_flashcards":
            MessageLookupByLibrary.simpleMessage("生成AI闪卡中..."),
        "professional": MessageLookupByLibrary.simpleMessage("商业用途"),
        "professional_description":
            MessageLookupByLibrary.simpleMessage("正式语言，适合工作场景"),
        "professional_style": MessageLookupByLibrary.simpleMessage("专业"),
        "public": MessageLookupByLibrary.simpleMessage("公开"),
        "purchase_fail": MessageLookupByLibrary.simpleMessage("购买失败！请重试！"),
        "purchase_init_fail":
            MessageLookupByLibrary.simpleMessage("无法开始购买，请重试。"),
        "purpose_using": MessageLookupByLibrary.simpleMessage("使用NoteX的"),
        "quantity": MessageLookupByLibrary.simpleMessage("数量"),
        "quarter": MessageLookupByLibrary.simpleMessage("季度"),
        "quarterly": MessageLookupByLibrary.simpleMessage("季付"),
        "question": MessageLookupByLibrary.simpleMessage("问题"),
        "quick_access": MessageLookupByLibrary.simpleMessage("快速访问"),
        "quick_import": MessageLookupByLibrary.simpleMessage("或快速导入"),
        "quickly": MessageLookupByLibrary.simpleMessage("概念"),
        "quiz": MessageLookupByLibrary.simpleMessage("测验"),
        "quiz_count": MessageLookupByLibrary.simpleMessage("测验数量"),
        "quiz_diff": MessageLookupByLibrary.simpleMessage("测验难度"),
        "quiz_gen_success": MessageLookupByLibrary.simpleMessage("测验生成成功"),
        "quiz_iap": MessageLookupByLibrary.simpleMessage("测验集"),
        "quiz_master": MessageLookupByLibrary.simpleMessage("AI测验大师"),
        "quiz_score": MessageLookupByLibrary.simpleMessage("测验分数"),
        "quiz_set": MessageLookupByLibrary.simpleMessage("测验集"),
        "quiz_set_not_found": MessageLookupByLibrary.simpleMessage("未找到测验集"),
        "quizz_for": MessageLookupByLibrary.simpleMessage("的测验"),
        "quizzes": MessageLookupByLibrary.simpleMessage("测验"),
        "rate": MessageLookupByLibrary.simpleMessage("评分"),
        "rate_five_stars": MessageLookupByLibrary.simpleMessage("给五星好评"),
        "rate_us_on_store": MessageLookupByLibrary.simpleMessage("给我们评分"),
        "rating_cmt1": MessageLookupByLibrary.simpleMessage(
            "这款应用太棒了，且不断进步。感谢开发者的努力，推荐优于其他AI笔记应用。"),
        "rating_cmt2": MessageLookupByLibrary.simpleMessage("超爱这款应用！会议的完美伴侣。"),
        "rating_cmt3": MessageLookupByLibrary.simpleMessage("学习时间减半，更多时间喝咖啡！"),
        "rating_cmt4": MessageLookupByLibrary.simpleMessage(
            "这款应用令人震撼！不仅转录完美，还提供出色的总结、提纲和行动项，简直天才！"),
        "rating_cmt5": MessageLookupByLibrary.simpleMessage("强大又好用！应有尽有。"),
        "rating_cmt6": MessageLookupByLibrary.simpleMessage(
            "今天首次尝试输入YouTube演示，秒速生成完整转录、精美脑图和闪卡系列，远超预期。高效、精致、实用，每天都会用！"),
        "rating_cmt7":
            MessageLookupByLibrary.simpleMessage("目前最好的笔记应用，功能丰富实用。"),
        "rating_cmt8":
            MessageLookupByLibrary.simpleMessage("捕捉生物学长讲座每个细节，总结功能在考试准备中救命。"),
        "rating_sub_context_1": MessageLookupByLibrary.simpleMessage("震撼体验！"),
        "rating_sub_context_2": MessageLookupByLibrary.simpleMessage("会议专家"),
        "rating_sub_context_3": MessageLookupByLibrary.simpleMessage("省时利器"),
        "rating_sub_context_4": MessageLookupByLibrary.simpleMessage("最佳AI笔记"),
        "rating_sub_context_5": MessageLookupByLibrary.simpleMessage("超爱这款应用"),
        "rating_sub_context_6": MessageLookupByLibrary.simpleMessage("最佳AI笔记"),
        "rating_sub_context_7": MessageLookupByLibrary.simpleMessage("最好用的笔记"),
        "rating_sub_context_8": MessageLookupByLibrary.simpleMessage("目前最佳"),
        "record": MessageLookupByLibrary.simpleMessage("录音"),
        "record_audio": MessageLookupByLibrary.simpleMessage("录音"),
        "record_audio_coming_soon":
            MessageLookupByLibrary.simpleMessage("录音（即将推出）"),
        "record_over_x_min": MessageLookupByLibrary.simpleMessage("录音超%s分钟"),
        "record_over_x_min_details":
            MessageLookupByLibrary.simpleMessage("录音将本地保存，无AI转录和总结。完成后可移除限制。"),
        "record_summarize_lecture":
            MessageLookupByLibrary.simpleMessage("录音并总结课堂"),
        "recording": MessageLookupByLibrary.simpleMessage("录音"),
        "recording_in_progress": MessageLookupByLibrary.simpleMessage("录音进行中"),
        "recording_in_progress_content":
            MessageLookupByLibrary.simpleMessage("录音中..."),
        "recording_paused": MessageLookupByLibrary.simpleMessage("录音已暂停"),
        "recording_paused_content":
            MessageLookupByLibrary.simpleMessage("按下继续"),
        "recording_permission_denied":
            MessageLookupByLibrary.simpleMessage("录音权限被拒绝！"),
        "recording_permission_denied_details":
            MessageLookupByLibrary.simpleMessage("请在设置中允许"),
        "recording_quality": MessageLookupByLibrary.simpleMessage("录音质量"),
        "recording_schedule": MessageLookupByLibrary.simpleMessage("录音计划"),
        "recording_voice_note": MessageLookupByLibrary.simpleMessage("录音笔记"),
        "redeem_7_days_for_0": MessageLookupByLibrary.simpleMessage("0元兑换7天"),
        "redeem_credits": MessageLookupByLibrary.simpleMessage("兑换"),
        "refer_now": MessageLookupByLibrary.simpleMessage("立即推荐"),
        "refer_rewards": MessageLookupByLibrary.simpleMessage("推荐与奖励"),
        "referral": MessageLookupByLibrary.simpleMessage("推荐"),
        "referral_02": MessageLookupByLibrary.simpleMessage("推荐"),
        "referral_already_used":
            MessageLookupByLibrary.simpleMessage("推荐码已被使用。"),
        "referral_code": MessageLookupByLibrary.simpleMessage("推荐码"),
        "referral_credits": MessageLookupByLibrary.simpleMessage("推荐积分"),
        "referral_not_found": MessageLookupByLibrary.simpleMessage("未找到推荐码。"),
        "referral_self_use":
            MessageLookupByLibrary.simpleMessage("不能使用自己的推荐码。"),
        "referral_time_expired":
            MessageLookupByLibrary.simpleMessage("推荐码24小时后过期。"),
        "referral_validation_err":
            MessageLookupByLibrary.simpleMessage("推荐验证错误。"),
        "reload_tap": MessageLookupByLibrary.simpleMessage("错误，点击重载"),
        "remain_recording_length":
            MessageLookupByLibrary.simpleMessage("根据录音时长剩余30秒至5分钟..."),
        "reminders_record_audio":
            MessageLookupByLibrary.simpleMessage("设置每周录音时间"),
        "remove_all_limits": MessageLookupByLibrary.simpleMessage("移除所有限制"),
        "replace": MessageLookupByLibrary.simpleMessage("替换"),
        "replace_all": MessageLookupByLibrary.simpleMessage("全部替换"),
        "report_issue": MessageLookupByLibrary.simpleMessage("如何报告问题？"),
        "report_issue2": MessageLookupByLibrary.simpleMessage("我们随时为你提供帮助："),
        "required": MessageLookupByLibrary.simpleMessage("示例：文件夹A"),
        "reset": MessageLookupByLibrary.simpleMessage("重置"),
        "restart_now": MessageLookupByLibrary.simpleMessage("立即重启"),
        "restore": MessageLookupByLibrary.simpleMessage("恢复"),
        "restore_fail_message":
            MessageLookupByLibrary.simpleMessage("如需帮助，请联系 <EMAIL>"),
        "restore_fail_title": MessageLookupByLibrary.simpleMessage("无可恢复项目"),
        "restore_purchase": MessageLookupByLibrary.simpleMessage("恢复购买"),
        "restore_success_title": MessageLookupByLibrary.simpleMessage("恢复成功"),
        "retention": MessageLookupByLibrary.simpleMessage("与记忆"),
        "retention_quickly": MessageLookupByLibrary.simpleMessage("概念"),
        "retry": MessageLookupByLibrary.simpleMessage("重试"),
        "sale_off": MessageLookupByLibrary.simpleMessage("折扣"),
        "satisfied": MessageLookupByLibrary.simpleMessage("感谢你的反馈！"),
        "satisfied_quality": MessageLookupByLibrary.simpleMessage("此笔记清晰有用吗？"),
        "save": MessageLookupByLibrary.simpleMessage("保存"),
        "save_50": MessageLookupByLibrary.simpleMessage("节省50%"),
        "save_changes": MessageLookupByLibrary.simpleMessage("保存更改？"),
        "save_chat": MessageLookupByLibrary.simpleMessage("保存聊天"),
        "save_file": MessageLookupByLibrary.simpleMessage("文件已保存"),
        "saved_chat": MessageLookupByLibrary.simpleMessage("已保存的聊天"),
        "saved_successfully": MessageLookupByLibrary.simpleMessage("保存成功"),
        "saving_recording": MessageLookupByLibrary.simpleMessage("保存录音到设备"),
        "search": MessageLookupByLibrary.simpleMessage("搜索"),
        "search_emoji": MessageLookupByLibrary.simpleMessage("搜索表情"),
        "search_in_files": MessageLookupByLibrary.simpleMessage("在文件中搜索"),
        "searching_all_notes": MessageLookupByLibrary.simpleMessage("搜索所有笔记"),
        "seconds": MessageLookupByLibrary.simpleMessage("秒"),
        "select_a_language":
            MessageLookupByLibrary.simpleMessage("在保存前选择录音使用的语言。"),
        "select_all": MessageLookupByLibrary.simpleMessage("全选"),
        "select_and_reorder": MessageLookupByLibrary.simpleMessage(
            "选择并重新排列你的笔记模块。继续操作至少需要 4 个标签页。"),
        "select_language": MessageLookupByLibrary.simpleMessage("选择语言"),
        "select_your_note": MessageLookupByLibrary.simpleMessage("选择你的笔记模块。"),
        "select_your_primary_use_case":
            MessageLookupByLibrary.simpleMessage("选择你的主要使用场景"),
        "server_err": MessageLookupByLibrary.simpleMessage("未知服务器错误。"),
        "server_error": MessageLookupByLibrary.simpleMessage("服务器错误"),
        "setting": MessageLookupByLibrary.simpleMessage("设置"),
        "settings": MessageLookupByLibrary.simpleMessage("设置"),
        "seven_day_free": MessageLookupByLibrary.simpleMessage("7天免费，然后"),
        "share": MessageLookupByLibrary.simpleMessage("分享给朋友"),
        "share_audio_file": MessageLookupByLibrary.simpleMessage("分享音频"),
        "share_code_friends":
            MessageLookupByLibrary.simpleMessage("通过邮件、社交媒体或消息分享给朋友。"),
        "share_file": MessageLookupByLibrary.simpleMessage("分享音频文件"),
        "share_note": MessageLookupByLibrary.simpleMessage("分享笔记"),
        "share_note_link": MessageLookupByLibrary.simpleMessage("分享笔记"),
        "share_only": MessageLookupByLibrary.simpleMessage("仅分享"),
        "share_referral_code_start_earning_credits":
            MessageLookupByLibrary.simpleMessage("分享推荐码开始赚取积分！"),
        "share_summary": MessageLookupByLibrary.simpleMessage("复制总结"),
        "share_sync": MessageLookupByLibrary.simpleMessage("分享与同步"),
        "share_transcript": MessageLookupByLibrary.simpleMessage("复制转录"),
        "share_with_link": MessageLookupByLibrary.simpleMessage("通过链接分享："),
        "shared": MessageLookupByLibrary.simpleMessage("已分享"),
        "sharing_export": MessageLookupByLibrary.simpleMessage("分享与导出"),
        "short": MessageLookupByLibrary.simpleMessage("简短"),
        "short_description": MessageLookupByLibrary.simpleMessage("仅关键点"),
        "shorts": MessageLookupByLibrary.simpleMessage("短视频"),
        "show_your_love": MessageLookupByLibrary.simpleMessage("通过给我们"),
        "signing_in": MessageLookupByLibrary.simpleMessage("登录中..."),
        "skip": MessageLookupByLibrary.simpleMessage("跳过"),
        "slide_count": MessageLookupByLibrary.simpleMessage("创建幻灯片放映"),
        "slide_count_tooltip":
            MessageLookupByLibrary.simpleMessage("每个模板最多12张幻灯片"),
        "slide_range": MessageLookupByLibrary.simpleMessage("创建幻灯片放映"),
        "slide_show": MessageLookupByLibrary.simpleMessage("幻灯片"),
        "smart_learning": MessageLookupByLibrary.simpleMessage("智能学习"),
        "smart_note_big_ideas":
            MessageLookupByLibrary.simpleMessage("智能笔记，大创意"),
        "smart_quizzes": MessageLookupByLibrary.simpleMessage("无限自适应测验"),
        "smart_start": MessageLookupByLibrary.simpleMessage("智能入门包"),
        "sort_by": MessageLookupByLibrary.simpleMessage("排序"),
        "special_gift": MessageLookupByLibrary.simpleMessage("特别礼物"),
        "special_gift_title": MessageLookupByLibrary.simpleMessage("特殊礼物"),
        "special_offer": MessageLookupByLibrary.simpleMessage("特别\n优惠"),
        "speech_language": MessageLookupByLibrary.simpleMessage("语音语言"),
        "start_for_free": MessageLookupByLibrary.simpleMessage("免费开始"),
        "start_free_trial": MessageLookupByLibrary.simpleMessage("开始免费试用"),
        "start_my_7_day_trial": MessageLookupByLibrary.simpleMessage("开始7天试用"),
        "start_record": MessageLookupByLibrary.simpleMessage("开始录音"),
        "start_speaking": MessageLookupByLibrary.simpleMessage("开始讲话"),
        "step1": MessageLookupByLibrary.simpleMessage("在NoteX应用中，进入设置。"),
        "step2":
            MessageLookupByLibrary.simpleMessage("在底部找到应用版本（例如 v1.4.0(6)）。"),
        "step3": MessageLookupByLibrary.simpleMessage("快速点击版本号5次。"),
        "step4": MessageLookupByLibrary.simpleMessage("你的唯一用户ID将自动复制到剪贴板。"),
        "step5": MessageLookupByLibrary.simpleMessage("在下方消息中，请包含："),
        "step51": MessageLookupByLibrary.simpleMessage("你的用户ID（从剪贴板粘贴）。"),
        "step52": MessageLookupByLibrary.simpleMessage("你遇到的问题简述。"),
        "step53": MessageLookupByLibrary.simpleMessage("相关细节（例如设备型号、iOS版本）。"),
        "step6": MessageLookupByLibrary.simpleMessage("发送邮件至 "),
        "student": MessageLookupByLibrary.simpleMessage("学术用途"),
        "style": MessageLookupByLibrary.simpleMessage("样式"),
        "sub_rip": MessageLookupByLibrary.simpleMessage("SubRip (.srt)"),
        "sub_user_have_unlimited": MessageLookupByLibrary.simpleMessage(
            "订阅用户享受无限制使用和无广告高级功能。非订阅用户可继续使用，但有广告和功能限制。付款将在购买确认时从Google Play账户扣除。订阅将自动续订，除非在周期结束前24小时关闭。账户将在当前周期结束前24小时按计划扣费。如有免费试用，购买订阅后未使用部分将失效。可在Google Play的订阅页面管理或关闭自动续订。卸载应用不会取消订阅。"),
        "sub_will_auto_renew":
            MessageLookupByLibrary.simpleMessage("订阅将自动续订，可随时取消。"),
        "submit": MessageLookupByLibrary.simpleMessage("提交"),
        "submit_button": MessageLookupByLibrary.simpleMessage("提交"),
        "subscribe": MessageLookupByLibrary.simpleMessage("订阅"),
        "subscribe_via_web": MessageLookupByLibrary.simpleMessage(
            "如通过网页订阅，请在 notexapp.com/setting 管理"),
        "success": MessageLookupByLibrary.simpleMessage("成功"),
        "successfully": MessageLookupByLibrary.simpleMessage("成功"),
        "suggest_features": MessageLookupByLibrary.simpleMessage("建议功能"),
        "suggested": MessageLookupByLibrary.simpleMessage("建议"),
        "summarize_video": MessageLookupByLibrary.simpleMessage("总结长YouTube视频"),
        "summary": MessageLookupByLibrary.simpleMessage("总结"),
        "summary_language": MessageLookupByLibrary.simpleMessage("总结语言"),
        "summary_style": MessageLookupByLibrary.simpleMessage("总结风格"),
        "summary_successful": MessageLookupByLibrary.simpleMessage("总结创建成功！"),
        "summary_usefulness": MessageLookupByLibrary.simpleMessage("总结实用性"),
        "supercharge": MessageLookupByLibrary.simpleMessage("高效少压力"),
        "support_audio": MessageLookupByLibrary.simpleMessage(
            "支持文件类型：.mp3, .wav, .ogg, .m4a"),
        "support_for_up_to_10_images":
            MessageLookupByLibrary.simpleMessage("支持最多10张图片"),
        "support_image": MessageLookupByLibrary.simpleMessage(
            "支持的图片类型: .png, .jpg, .heif, .heic"),
        "support_over_onehundred_languages":
            MessageLookupByLibrary.simpleMessage("支持100+语言"),
        "support_youtube_and_more": MessageLookupByLibrary.simpleMessage(
            "支持YouTube、网页、TikTok、Instagram、Facebook等"),
        "switch_mode": MessageLookupByLibrary.simpleMessage("切换模式"),
        "sync_from_watch": MessageLookupByLibrary.simpleMessage("从手表同步"),
        "sync_notes": MessageLookupByLibrary.simpleMessage("在电脑浏览器中同步笔记"),
        "system": MessageLookupByLibrary.simpleMessage("系统"),
        "tap_cancel": MessageLookupByLibrary.simpleMessage("点击取消订阅"),
        "tap_menu": MessageLookupByLibrary.simpleMessage("点击菜单-订阅，选择要取消的订阅"),
        "tap_the": MessageLookupByLibrary.simpleMessage("点击"),
        "tap_the_record": MessageLookupByLibrary.simpleMessage("点击录音"),
        "tap_to_record": MessageLookupByLibrary.simpleMessage("点击录下你的想法"),
        "task_create_err":
            MessageLookupByLibrary.simpleMessage("创建任务失败，请稍后重试。"),
        "templates": MessageLookupByLibrary.simpleMessage("模板"),
        "term_and_cond": MessageLookupByLibrary.simpleMessage("条款和条件"),
        "terms": MessageLookupByLibrary.simpleMessage("条款"),
        "terms_of_sub": MessageLookupByLibrary.simpleMessage("订阅条款"),
        "terms_of_use": MessageLookupByLibrary.simpleMessage("使用条款"),
        "text": MessageLookupByLibrary.simpleMessage("添加文本"),
        "text_must_not_exceed_50_chars":
            MessageLookupByLibrary.simpleMessage("文本仅限50个字符"),
        "thank_feedback": MessageLookupByLibrary.simpleMessage("感谢反馈！"),
        "thinking": MessageLookupByLibrary.simpleMessage("思考中..."),
        "thirty_min_per": MessageLookupByLibrary.simpleMessage("每周30分钟"),
        "this_folder_empty":
            MessageLookupByLibrary.simpleMessage("该文件夹为空，快创建你的第一条AI笔记！✨"),
        "this_free_trial":
            MessageLookupByLibrary.simpleMessage("此免费试用仅限新用户，体验一周完整Pro功能后再决定。"),
        "this_is_the_language":
            MessageLookupByLibrary.simpleMessage("这是总结输出的语言"),
        "thousands_trusted":
            MessageLookupByLibrary.simpleMessage("4.8/5评分，数千人信赖"),
        "time": MessageLookupByLibrary.simpleMessage("时间"),
        "time_black_friday": MessageLookupByLibrary.simpleMessage("11月22-30日"),
        "time_black_friday_2":
            MessageLookupByLibrary.simpleMessage("11月22日至30日"),
        "time_out": MessageLookupByLibrary.simpleMessage("请求超时，请重试。"),
        "title": MessageLookupByLibrary.simpleMessage("标题"),
        "title_error_note": MessageLookupByLibrary.simpleMessage("笔记创建失败"),
        "title_success_note": MessageLookupByLibrary.simpleMessage("AI笔记创建成功"),
        "to": MessageLookupByLibrary.simpleMessage("到"),
        "to_day": MessageLookupByLibrary.simpleMessage("今天"),
        "token_expired": MessageLookupByLibrary.simpleMessage("令牌已过期！"),
        "tolower_credits": MessageLookupByLibrary.simpleMessage("积分"),
        "tool_tip_language":
            MessageLookupByLibrary.simpleMessage("在保存录音前选择主要语音语言"),
        "topic_option": MessageLookupByLibrary.simpleMessage("主题（可选）"),
        "total": MessageLookupByLibrary.simpleMessage("总计"),
        "transcribing": MessageLookupByLibrary.simpleMessage("使用最佳AI转录"),
        "transcribing_audio": MessageLookupByLibrary.simpleMessage("转录音频中..."),
        "transcript": MessageLookupByLibrary.simpleMessage("转录"),
        "transcript_context": MessageLookupByLibrary.simpleMessage("转录上下文"),
        "transcript_language": MessageLookupByLibrary.simpleMessage("转录语言"),
        "transcript_line_cannot_be_empty":
            MessageLookupByLibrary.simpleMessage("转录行不能为空"),
        "transcript_line_tool_tip":
            MessageLookupByLibrary.simpleMessage("点击转录项编辑"),
        "transcription_precision":
            MessageLookupByLibrary.simpleMessage("转录精准度"),
        "transform_meetings": MessageLookupByLibrary.simpleMessage("将会议转为"),
        "transform_meetings_into_actionable_intelligence":
            MessageLookupByLibrary.simpleMessage("将会议转为可执行洞察"),
        "translate_note": MessageLookupByLibrary.simpleMessage("翻译笔记"),
        "translating_note": MessageLookupByLibrary.simpleMessage("翻译笔记中..."),
        "translation_completed": MessageLookupByLibrary.simpleMessage("翻译完成"),
        "translation_failed": MessageLookupByLibrary.simpleMessage("翻译失败"),
        "trouble_connecting_to_server":
            MessageLookupByLibrary.simpleMessage("我们连接服务器时遇到了一些问题。请稍后再试。"),
        "try_3_day": MessageLookupByLibrary.simpleMessage("3天免费试用"),
        "try_7_day": MessageLookupByLibrary.simpleMessage("开始7天试用"),
        "try_again": MessageLookupByLibrary.simpleMessage("生成笔记时遇到问题，请重试！"),
        "try_again_button": MessageLookupByLibrary.simpleMessage("重试"),
        "try_pro_free_7_day": MessageLookupByLibrary.simpleMessage("7天免费试用Pro"),
        "type_or_paste_any_text_here": MessageLookupByLibrary.simpleMessage(
            "在此输入或粘贴文本，AI将生成清晰结构化的总结与关键点。"),
        "uidCopied": m3,
        "unable_download_file": MessageLookupByLibrary.simpleMessage("无法下载文件"),
        "unable_load_audio": MessageLookupByLibrary.simpleMessage("无法加载音频："),
        "unable_share_audio": MessageLookupByLibrary.simpleMessage("无法分享音频文件"),
        "unable_to_connect_to_server":
            MessageLookupByLibrary.simpleMessage("请确保您的手机已连接到互联网"),
        "unable_to_extract_web_url":
            MessageLookupByLibrary.simpleMessage("无法从网页URL提取内容"),
        "unable_to_open_store": MessageLookupByLibrary.simpleMessage("无法打开商店"),
        "uncover_opportunities": MessageLookupByLibrary.simpleMessage("发现机会"),
        "unknown_error": MessageLookupByLibrary.simpleMessage("应用遇到未知错误"),
        "unknown_server_error":
            MessageLookupByLibrary.simpleMessage("服务器出错，请重试。"),
        "unlimited_ai_chat":
            MessageLookupByLibrary.simpleMessage("无限AI聊天、思维导图、闪卡、测验"),
        "unlimited_ai_chat_ai_mind_map_flashcard_quiz":
            MessageLookupByLibrary.simpleMessage("无限AI聊天、思维导图、闪卡、测验"),
        "unlimited_ai_note": MessageLookupByLibrary.simpleMessage(
            "从所有来源（YouTube、文档、录音、音频）生成无限AI笔记"),
        "unlimited_ai_notes_from_youtube_and_document":
            MessageLookupByLibrary.simpleMessage("从YouTube和文档生成无限AI笔记"),
        "unlimited_audio_youtube_website_to_ai_notes":
            MessageLookupByLibrary.simpleMessage("无限音频、YouTube、文档和网站转AI笔记"),
        "unlimited_everything":
            MessageLookupByLibrary.simpleMessage("体验无限AI笔记、优先服务和高级功能"),
        "unlimited_youtube_document_ai_notes":
            MessageLookupByLibrary.simpleMessage("无限YouTube与文档AI笔记"),
        "unlock_all_features": MessageLookupByLibrary.simpleMessage("解锁所有功能"),
        "unlock_essential_life_time":
            MessageLookupByLibrary.simpleMessage("解锁基础终身版"),
        "unlock_lifetime_access":
            MessageLookupByLibrary.simpleMessage("解锁终身访问"),
        "unlock_pro_lifetime": MessageLookupByLibrary.simpleMessage("解锁Pro终身版"),
        "unlock_the_most_ipad":
            MessageLookupByLibrary.simpleMessage("解锁最强大的AI笔记助手"),
        "unlock_the_most_powerful_ai_note_taking_assistant":
            MessageLookupByLibrary.simpleMessage("解锁最强大的AI\n笔记助手"),
        "unlock_toge": MessageLookupByLibrary.simpleMessage("一起解锁"),
        "unlock_together": MessageLookupByLibrary.simpleMessage("一起解锁"),
        "unlock_unlimited_access_to_all_ai_features":
            MessageLookupByLibrary.simpleMessage("解锁对所有AI功能的无限访问"),
        "unlock_unlimited_ai": MessageLookupByLibrary.simpleMessage("解锁无限AI体验"),
        "unsynced_notes": MessageLookupByLibrary.simpleMessage("未同步笔记"),
        "update_available":
            MessageLookupByLibrary.simpleMessage("有新版本可用！请更新到最新版本以获得最佳体验。"),
        "update_failed": MessageLookupByLibrary.simpleMessage("更新社区笔记失败，请重试。"),
        "update_later": MessageLookupByLibrary.simpleMessage("稍后"),
        "update_now": MessageLookupByLibrary.simpleMessage("立即更新！"),
        "update_pro": MessageLookupByLibrary.simpleMessage("升级Pro体验"),
        "update_to_pro": MessageLookupByLibrary.simpleMessage("升级至Pro"),
        "upgrade": MessageLookupByLibrary.simpleMessage("升级"),
        "upgrade_now": MessageLookupByLibrary.simpleMessage("立即升级！"),
        "upgrade_plan": MessageLookupByLibrary.simpleMessage("升级计划"),
        "upgrade_pro_2": MessageLookupByLibrary.simpleMessage("升级Pro"),
        "upgrade_to_full_pro_access":
            MessageLookupByLibrary.simpleMessage("升级到完整专业版访问"),
        "upgrade_to_pro_tier_at_a_special_price":
            MessageLookupByLibrary.simpleMessage("以特惠价升级Pro"),
        "upload": MessageLookupByLibrary.simpleMessage("上传"),
        "upload_audio": MessageLookupByLibrary.simpleMessage("上传音频"),
        "upload_audio_file": MessageLookupByLibrary.simpleMessage("上传音频"),
        "upload_file": MessageLookupByLibrary.simpleMessage("上传文件"),
        "upload_image": MessageLookupByLibrary.simpleMessage("上传图片"),
        "upload_in_progress":
            MessageLookupByLibrary.simpleMessage("上传中，请保持屏幕开启。\n关闭VPN以加快上传。"),
        "uploading_to_server": MessageLookupByLibrary.simpleMessage("上传至安全服务器"),
        "user_disabled": MessageLookupByLibrary.simpleMessage("此邮箱对应的用户已被禁用。"),
        "user_not_found": MessageLookupByLibrary.simpleMessage("未找到用户信息。"),
        "verifying_your_credentials":
            MessageLookupByLibrary.simpleMessage("验证你的凭据"),
        "video": MessageLookupByLibrary.simpleMessage("视频"),
        "video_audio": MessageLookupByLibrary.simpleMessage("录音、视频和文档转AI笔记"),
        "video_captions": MessageLookupByLibrary.simpleMessage("视频字幕"),
        "video_is_temporary":
            MessageLookupByLibrary.simpleMessage("*视频为临时文件-关闭前保存"),
        "visualize_strategies": MessageLookupByLibrary.simpleMessage("可视化策略并"),
        "visualize_strategies_opportunities":
            MessageLookupByLibrary.simpleMessage("可视化策略并发现机会"),
        "visualize_strategies_uncover":
            MessageLookupByLibrary.simpleMessage("可视化策略并发现"),
        "visualize_strategies_uncover_opportunities":
            MessageLookupByLibrary.simpleMessage("可视化策略，发现机会"),
        "voice": MessageLookupByLibrary.simpleMessage("语音"),
        "warning_this_ai_note_taking_app_may_cause_excessive_productivity":
            MessageLookupByLibrary.simpleMessage(
                "警告：这款AI笔记应用可能让你效率爆棚！🚀 使用我的码，我们都能获得额外使用量。码："),
        "watch_sync_empty_message":
            MessageLookupByLibrary.simpleMessage("Apple Watch的录音将显示在此"),
        "web": MessageLookupByLibrary.simpleMessage("网页"),
        "web_link": MessageLookupByLibrary.simpleMessage("网页链接"),
        "web_sync": MessageLookupByLibrary.simpleMessage("网页同步"),
        "website_import": MessageLookupByLibrary.simpleMessage("网站导入"),
        "week": MessageLookupByLibrary.simpleMessage("周"),
        "week_free_limit": MessageLookupByLibrary.simpleMessage("周免费限制已达"),
        "weekly": MessageLookupByLibrary.simpleMessage("周付"),
        "weekly_free_limit_reached":
            MessageLookupByLibrary.simpleMessage("移除所有限制"),
        "weekly_free_limit_reached_details":
            MessageLookupByLibrary.simpleMessage(
                "本周免费转录和AI总结已用完！升级Pro无限使用或下周重置。"),
        "welcome_notex": MessageLookupByLibrary.simpleMessage("欢迎使用NoteX！"),
        "welcome_title": MessageLookupByLibrary.simpleMessage("创建你的\n首个AI笔记"),
        "what_improve": MessageLookupByLibrary.simpleMessage("需要改进什么"),
        "whats_new": MessageLookupByLibrary.simpleMessage("新功能"),
        "word_docx": MessageLookupByLibrary.simpleMessage("Word (.docx)"),
        "work_notes_projects": MessageLookupByLibrary.simpleMessage("工作笔记与项目"),
        "writing_style": MessageLookupByLibrary.simpleMessage("写作风格"),
        "wrong": MessageLookupByLibrary.simpleMessage("错误"),
        "x": MessageLookupByLibrary.simpleMessage("X"),
        "x_skip": MessageLookupByLibrary.simpleMessage("目的？"),
        "year": MessageLookupByLibrary.simpleMessage("年"),
        "yearly": MessageLookupByLibrary.simpleMessage("年付"),
        "yes": MessageLookupByLibrary.simpleMessage("是"),
        "yesterday": MessageLookupByLibrary.simpleMessage("昨天"),
        "you_are_given_a_special_gift_today":
            MessageLookupByLibrary.simpleMessage("今日为你献上特别礼物 🎁"),
        "you_are_pro": MessageLookupByLibrary.simpleMessage("Pro访问"),
        "you_can_update_setting":
            MessageLookupByLibrary.simpleMessage("你可以随时在设置中更新。"),
        "you_have_received": MessageLookupByLibrary.simpleMessage("你已获得"),
        "you_have_received2": MessageLookupByLibrary.simpleMessage(
            "你将有机会赢取NoteX终身Pro！每月30日选出3名幸运儿 🎁"),
        "you_will_get_one_entry_to_win_noteX":
            MessageLookupByLibrary.simpleMessage("赢取NoteX终身Pro的机会！🚀"),
        "you_will_not_be": MessageLookupByLibrary.simpleMessage("删除后无法恢复"),
        "your_learning": MessageLookupByLibrary.simpleMessage("超充电你的学习！"),
        "your_learning_device":
            MessageLookupByLibrary.simpleMessage("NoteX Pro访问"),
        "your_note_are_ready":
            MessageLookupByLibrary.simpleMessage("你的笔记已准备好。"),
        "your_personal_study": MessageLookupByLibrary.simpleMessage("你的个人学习"),
        "your_personal_study_assistant":
            MessageLookupByLibrary.simpleMessage("你的个人学习助手"),
        "your_plan": MessageLookupByLibrary.simpleMessage("你的计划"),
        "your_primary": MessageLookupByLibrary.simpleMessage("你的主要"),
        "your_product": MessageLookupByLibrary.simpleMessage("你的生产力"),
        "your_recording_will_save":
            MessageLookupByLibrary.simpleMessage("录音将本地保存，无AI转录和总结。完成后可移除限制。"),
        "your_referrals": MessageLookupByLibrary.simpleMessage("你的推荐"),
        "youtube": MessageLookupByLibrary.simpleMessage("YouTube"),
        "youtube_import": MessageLookupByLibrary.simpleMessage("YouTube导入"),
        "youtube_link": MessageLookupByLibrary.simpleMessage("YouTube链接"),
        "youtube_transcript_language_guidance":
            MessageLookupByLibrary.simpleMessage("选择YouTube转录语言，此语言将用于生成AI笔记"),
        "youtube_video": MessageLookupByLibrary.simpleMessage("YouTube视频"),
        "youtube_video_note": MessageLookupByLibrary.simpleMessage("YouTube视频"),
        "yt_credit_err":
            MessageLookupByLibrary.simpleMessage("YouTube免费额度不足，请升级计划。"),
        "yt_credit_use_err":
            MessageLookupByLibrary.simpleMessage("使用YouTube免费额度出错，请稍后重试。"),
        "yt_length_err":
            MessageLookupByLibrary.simpleMessage("YouTube视频超10小时限制，请选择较短视频。"),
        "yt_process_err":
            MessageLookupByLibrary.simpleMessage("处理YouTube视频出错，请检查URL后重试。"),
        "yt_sum_limit": MessageLookupByLibrary.simpleMessage("YouTube总结限制"),
        "z_to_a": MessageLookupByLibrary.simpleMessage("Z到A")
      };
}
