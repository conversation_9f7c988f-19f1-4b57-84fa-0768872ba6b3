// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `Something went wrong`
  String get server_error {
    return Intl.message(
      'Something went wrong',
      name: 'server_error',
      desc: '',
      args: [],
    );
  }

  /// `OK`
  String get ok {
    return Intl.message(
      'OK',
      name: 'ok',
      desc: '',
      args: [],
    );
  }

  /// `Yes`
  String get yes {
    return Intl.message(
      'Yes',
      name: 'yes',
      desc: '',
      args: [],
    );
  }

  /// `No`
  String get no {
    return Intl.message(
      'No',
      name: 'no',
      desc: '',
      args: [],
    );
  }

  /// `By tapping the “Get Started” button, you agree to our`
  String get by_tapping_started {
    return Intl.message(
      'By tapping the “Get Started” button, you agree to our',
      name: 'by_tapping_started',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Now!`
  String get upgrade_now {
    return Intl.message(
      'Upgrade Now!',
      name: 'upgrade_now',
      desc: '',
      args: [],
    );
  }

  /// `Let’s start`
  String get let_start {
    return Intl.message(
      'Let’s start',
      name: 'let_start',
      desc: '',
      args: [],
    );
  }

  /// `Subscribe`
  String get subscribe {
    return Intl.message(
      'Subscribe',
      name: 'subscribe',
      desc: '',
      args: [],
    );
  }

  /// `Get Started`
  String get get_start {
    return Intl.message(
      'Get Started',
      name: 'get_start',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Use`
  String get terms_of_use {
    return Intl.message(
      'Terms of Use',
      name: 'terms_of_use',
      desc: '',
      args: [],
    );
  }

  /// `Paste`
  String get paste {
    return Intl.message(
      'Paste',
      name: 'paste',
      desc: '',
      args: [],
    );
  }

  /// `Language`
  String get language {
    return Intl.message(
      'Language',
      name: 'language',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get english {
    return Intl.message(
      'English',
      name: 'english',
      desc: '',
      args: [],
    );
  }

  /// `Open YouTube`
  String get open_youtube {
    return Intl.message(
      'Open YouTube',
      name: 'open_youtube',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get submit_button {
    return Intl.message(
      'Submit',
      name: 'submit_button',
      desc: '',
      args: [],
    );
  }

  /// `Youtube Link`
  String get youtube_link {
    return Intl.message(
      'Youtube Link',
      name: 'youtube_link',
      desc: '',
      args: [],
    );
  }

  /// `Record Audio (coming soon)`
  String get record_audio_coming_soon {
    return Intl.message(
      'Record Audio (coming soon)',
      name: 'record_audio_coming_soon',
      desc: '',
      args: [],
    );
  }

  /// `Upload audio file`
  String get upload_audio_file {
    return Intl.message(
      'Upload audio file',
      name: 'upload_audio_file',
      desc: '',
      args: [],
    );
  }

  /// `Start Record`
  String get start_record {
    return Intl.message(
      'Start Record',
      name: 'start_record',
      desc: '',
      args: [],
    );
  }

  /// `Esc`
  String get esc {
    return Intl.message(
      'Esc',
      name: 'esc',
      desc: '',
      args: [],
    );
  }

  /// `Done`
  String get done_button_label {
    return Intl.message(
      'Done',
      name: 'done_button_label',
      desc: '',
      args: [],
    );
  }

  /// `and`
  String get and {
    return Intl.message(
      'and',
      name: 'and',
      desc: '',
      args: [],
    );
  }

  /// `Press Back again to exit!`
  String get press_back_again_to_exit {
    return Intl.message(
      'Press Back again to exit!',
      name: 'press_back_again_to_exit',
      desc: '',
      args: [],
    );
  }

  /// `Privacy Policy`
  String get privacy_policy {
    return Intl.message(
      'Privacy Policy',
      name: 'privacy_policy',
      desc: '',
      args: [],
    );
  }

  /// `Click to flip`
  String get click_to_flip {
    return Intl.message(
      'Click to flip',
      name: 'click_to_flip',
      desc: '',
      args: [],
    );
  }

  /// `Flashcards for`
  String get flashcards_for {
    return Intl.message(
      'Flashcards for',
      name: 'flashcards_for',
      desc: '',
      args: [],
    );
  }

  /// `Flashcards`
  String get flashcards {
    return Intl.message(
      'Flashcards',
      name: 'flashcards',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get settings {
    return Intl.message(
      'Settings',
      name: 'settings',
      desc: '',
      args: [],
    );
  }

  /// `Go PRO`
  String get go_pro {
    return Intl.message(
      'Go PRO',
      name: 'go_pro',
      desc: '',
      args: [],
    );
  }

  /// `Rate Us on Store`
  String get rate_us_on_store {
    return Intl.message(
      'Rate Us on Store',
      name: 'rate_us_on_store',
      desc: '',
      args: [],
    );
  }

  /// `Send to Friends`
  String get share {
    return Intl.message(
      'Send to Friends',
      name: 'share',
      desc: '',
      args: [],
    );
  }

  /// `Community`
  String get community {
    return Intl.message(
      'Community',
      name: 'community',
      desc: '',
      args: [],
    );
  }

  /// `My notes`
  String get my_notes {
    return Intl.message(
      'My notes',
      name: 'my_notes',
      desc: '',
      args: [],
    );
  }

  /// `Youtube Video`
  String get youtube_video {
    return Intl.message(
      'Youtube Video',
      name: 'youtube_video',
      desc: '',
      args: [],
    );
  }

  /// `Upload Document (Coming Soon)`
  String get document {
    return Intl.message(
      'Upload Document (Coming Soon)',
      name: 'document',
      desc: '',
      args: [],
    );
  }

  /// `Quizz for`
  String get quizz_for {
    return Intl.message(
      'Quizz for',
      name: 'quizz_for',
      desc: '',
      args: [],
    );
  }

  /// `No Internet connection!`
  String get no_internet_connection {
    return Intl.message(
      'No Internet connection!',
      name: 'no_internet_connection',
      desc: '',
      args: [],
    );
  }

  /// `NoteX`
  String get noteX {
    return Intl.message(
      'NoteX',
      name: 'noteX',
      desc: '',
      args: [],
    );
  }

  /// `Paste a Youtube link`
  String get paste_youtube_link {
    return Intl.message(
      'Paste a Youtube link',
      name: 'paste_youtube_link',
      desc: '',
      args: [],
    );
  }

  /// `We'll generate a transcript, notes, and a study guide`
  String get generate_transcript_notes {
    return Intl.message(
      'We\'ll generate a transcript, notes, and a study guide',
      name: 'generate_transcript_notes',
      desc: '',
      args: [],
    );
  }

  /// `Submit`
  String get submit {
    return Intl.message(
      'Submit',
      name: 'submit',
      desc: '',
      args: [],
    );
  }

  /// `Continue`
  String get continue_button {
    return Intl.message(
      'Continue',
      name: 'continue_button',
      desc: '',
      args: [],
    );
  }

  /// `Almost done`
  String get almost_done {
    return Intl.message(
      'Almost done',
      name: 'almost_done',
      desc: '',
      args: [],
    );
  }

  /// `Error with Link`
  String get link_error {
    return Intl.message(
      'Error with Link',
      name: 'link_error',
      desc: '',
      args: [],
    );
  }

  /// `Cancel`
  String get cancel {
    return Intl.message(
      'Cancel',
      name: 'cancel',
      desc: '',
      args: [],
    );
  }

  /// `Retry`
  String get retry {
    return Intl.message(
      'Retry',
      name: 'retry',
      desc: '',
      args: [],
    );
  }

  /// `Record Audio`
  String get record_audio {
    return Intl.message(
      'Record Audio',
      name: 'record_audio',
      desc: '',
      args: [],
    );
  }

  /// `Discard changes?`
  String get discard_changes {
    return Intl.message(
      'Discard changes?',
      name: 'discard_changes',
      desc: '',
      args: [],
    );
  }

  /// `Audio File`
  String get audio_file {
    return Intl.message(
      'Audio File',
      name: 'audio_file',
      desc: '',
      args: [],
    );
  }

  /// `Edit Note`
  String get edit_notes {
    return Intl.message(
      'Edit Note',
      name: 'edit_notes',
      desc: '',
      args: [],
    );
  }

  /// `Edit Transcript`
  String get edit_transcript {
    return Intl.message(
      'Edit Transcript',
      name: 'edit_transcript',
      desc: '',
      args: [],
    );
  }

  /// `Search Emoji`
  String get search_emoji {
    return Intl.message(
      'Search Emoji',
      name: 'search_emoji',
      desc: '',
      args: [],
    );
  }

  /// `Save`
  String get save {
    return Intl.message(
      'Save',
      name: 'save',
      desc: '',
      args: [],
    );
  }

  /// `Save Changes?`
  String get save_changes {
    return Intl.message(
      'Save Changes?',
      name: 'save_changes',
      desc: '',
      args: [],
    );
  }

  /// `Rate us 5 stars`
  String get rate_five_stars {
    return Intl.message(
      'Rate us 5 stars',
      name: 'rate_five_stars',
      desc: '',
      args: [],
    );
  }

  /// `Hope you enjoy our app and thanks for your support !`
  String get hope_enjoy_app {
    return Intl.message(
      'Hope you enjoy our app and thanks for your support !',
      name: 'hope_enjoy_app',
      desc: '',
      args: [],
    );
  }

  /// `Rate`
  String get rate {
    return Intl.message(
      'Rate',
      name: 'rate',
      desc: '',
      args: [],
    );
  }

  /// `Maybe later`
  String get maybe_later {
    return Intl.message(
      'Maybe later',
      name: 'maybe_later',
      desc: '',
      args: [],
    );
  }

  /// `Feedback`
  String get feedback {
    return Intl.message(
      'Feedback',
      name: 'feedback',
      desc: '',
      args: [],
    );
  }

  /// `Upload File`
  String get upload_file {
    return Intl.message(
      'Upload File',
      name: 'upload_file',
      desc: '',
      args: [],
    );
  }

  /// `Auto detect`
  String get auto_detect {
    return Intl.message(
      'Auto detect',
      name: 'auto_detect',
      desc: '',
      args: [],
    );
  }

  /// `Leaving will stop the recording and discard all changes.`
  String get content_discard_changes {
    return Intl.message(
      'Leaving will stop the recording and discard all changes.',
      name: 'content_discard_changes',
      desc: '',
      args: [],
    );
  }

  /// `This action will save all changes, and they will be permanently applied.`
  String get content_save_changes {
    return Intl.message(
      'This action will save all changes, and they will be permanently applied.',
      name: 'content_save_changes',
      desc: '',
      args: [],
    );
  }

  /// `Free Recording Limit`
  String get free_recording_limit {
    return Intl.message(
      'Free Recording Limit',
      name: 'free_recording_limit',
      desc: '',
      args: [],
    );
  }

  /// `Recording Over %s Minutes`
  String get record_over_x_min {
    return Intl.message(
      'Recording Over %s Minutes',
      name: 'record_over_x_min',
      desc: '',
      args: [],
    );
  }

  /// `Your recordings will be saved locally without AI transcriptions and summarization. You can remove all limits to process this recording after it's complete.`
  String get record_over_x_min_details {
    return Intl.message(
      'Your recordings will be saved locally without AI transcriptions and summarization. You can remove all limits to process this recording after it\'s complete.',
      name: 'record_over_x_min_details',
      desc: '',
      args: [],
    );
  }

  /// `Remove All Limits`
  String get remove_all_limits {
    return Intl.message(
      'Remove All Limits',
      name: 'remove_all_limits',
      desc: '',
      args: [],
    );
  }

  /// `Remove All Limits`
  String get weekly_free_limit_reached {
    return Intl.message(
      'Remove All Limits',
      name: 'weekly_free_limit_reached',
      desc: '',
      args: [],
    );
  }

  /// `You've used all free transcriptions and AI summaries for this week! Upgrade to Pro for unlimited access or wait until next week for your free quota to reset.`
  String get weekly_free_limit_reached_details {
    return Intl.message(
      'You\'ve used all free transcriptions and AI summaries for this week! Upgrade to Pro for unlimited access or wait until next week for your free quota to reset.',
      name: 'weekly_free_limit_reached_details',
      desc: '',
      args: [],
    );
  }

  /// `You have %s-minute free transcriptions and AI summaries remaining this week.`
  String get free_recording_limit_details {
    return Intl.message(
      'You have %s-minute free transcriptions and AI summaries remaining this week.',
      name: 'free_recording_limit_details',
      desc: '',
      args: [],
    );
  }

  /// `Delete this note?`
  String get delete_note {
    return Intl.message(
      'Delete this note?',
      name: 'delete_note',
      desc: '',
      args: [],
    );
  }

  /// `You will not be able to recover them afterwards`
  String get content_delete_note {
    return Intl.message(
      'You will not be able to recover them afterwards',
      name: 'content_delete_note',
      desc: '',
      args: [],
    );
  }

  /// `Delete`
  String get delete {
    return Intl.message(
      'Delete',
      name: 'delete',
      desc: '',
      args: [],
    );
  }

  /// `Payment Success`
  String get payment_successfully {
    return Intl.message(
      'Payment Success',
      name: 'payment_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for your purchase. Your transaction has been successfully processed.`
  String get content_payment_successfully {
    return Intl.message(
      'Thank you for your purchase. Your transaction has been successfully processed.',
      name: 'content_payment_successfully',
      desc: '',
      args: [],
    );
  }

  /// `By subscribing you agree to`
  String get by_subscribing {
    return Intl.message(
      'By subscribing you agree to',
      name: 'by_subscribing',
      desc: '',
      args: [],
    );
  }

  /// `Terms and Conditions`
  String get term_and_cond {
    return Intl.message(
      'Terms and Conditions',
      name: 'term_and_cond',
      desc: '',
      args: [],
    );
  }

  /// `Subscriptions will be auto renew. Cancel anytime.`
  String get sub_will_auto_renew {
    return Intl.message(
      'Subscriptions will be auto renew. Cancel anytime.',
      name: 'sub_will_auto_renew',
      desc: '',
      args: [],
    );
  }

  /// `Terms of Subscriptions`
  String get terms_of_sub {
    return Intl.message(
      'Terms of Subscriptions',
      name: 'terms_of_sub',
      desc: '',
      args: [],
    );
  }

  /// `Subscribed users have unlimited use and access to all premium features without ads Non-subscribed users can continuously use the app with advertisements and have limited use of premium features Payment will be charged to Google Play account at confirmation of purchases. The subscription will be auto-renewed unless it is turned off within 24 hours before the end of the period. Your account will be charged according to your plan for renewal within 24 hours to the end of the current period. Any unused portion of a free trial period, if offered, will be forfeited when user purchases a subscription where applicable. You can manage or turn off auto-renew at the subscription page of Google Play after purchase. Note that uninstalling the app will not cancel your subscription.`
  String get sub_user_have_unlimited {
    return Intl.message(
      'Subscribed users have unlimited use and access to all premium features without ads Non-subscribed users can continuously use the app with advertisements and have limited use of premium features Payment will be charged to Google Play account at confirmation of purchases. The subscription will be auto-renewed unless it is turned off within 24 hours before the end of the period. Your account will be charged according to your plan for renewal within 24 hours to the end of the current period. Any unused portion of a free trial period, if offered, will be forfeited when user purchases a subscription where applicable. You can manage or turn off auto-renew at the subscription page of Google Play after purchase. Note that uninstalling the app will not cancel your subscription.',
      name: 'sub_user_have_unlimited',
      desc: '',
      args: [],
    );
  }

  /// `On your Android phone or tablet, open the Google Play Store`
  String get on_your_android {
    return Intl.message(
      'On your Android phone or tablet, open the Google Play Store',
      name: 'on_your_android',
      desc: '',
      args: [],
    );
  }

  /// `Check if you are signed into the correct Google account`
  String get check_if_you {
    return Intl.message(
      'Check if you are signed into the correct Google account',
      name: 'check_if_you',
      desc: '',
      args: [],
    );
  }

  /// `Tap Menu Subscriptions and select the subscription you want to cancel`
  String get tap_menu {
    return Intl.message(
      'Tap Menu Subscriptions and select the subscription you want to cancel',
      name: 'tap_menu',
      desc: '',
      args: [],
    );
  }

  /// `Tap Cancel subscription`
  String get tap_cancel {
    return Intl.message(
      'Tap Cancel subscription',
      name: 'tap_cancel',
      desc: '',
      args: [],
    );
  }

  /// `year`
  String get year {
    return Intl.message(
      'year',
      name: 'year',
      desc: '',
      args: [],
    );
  }

  /// `week`
  String get week {
    return Intl.message(
      'week',
      name: 'week',
      desc: '',
      args: [],
    );
  }

  /// `month`
  String get month {
    return Intl.message(
      'month',
      name: 'month',
      desc: '',
      args: [],
    );
  }

  /// `quarter`
  String get quarter {
    return Intl.message(
      'quarter',
      name: 'quarter',
      desc: '',
      args: [],
    );
  }

  /// `Save 50%`
  String get save_50 {
    return Intl.message(
      'Save 50%',
      name: 'save_50',
      desc: '',
      args: [],
    );
  }

  /// `Enable Free Trial`
  String get enable_free {
    return Intl.message(
      'Enable Free Trial',
      name: 'enable_free',
      desc: '',
      args: [],
    );
  }

  /// `Free trial`
  String get free_trial {
    return Intl.message(
      'Free trial',
      name: 'free_trial',
      desc: '',
      args: [],
    );
  }

  /// `Most popular`
  String get most_popular {
    return Intl.message(
      'Most popular',
      name: 'most_popular',
      desc: '',
      args: [],
    );
  }

  /// `Continue 3-day Free Trial`
  String get continue_3_day {
    return Intl.message(
      'Continue 3-day Free Trial',
      name: 'continue_3_day',
      desc: '',
      args: [],
    );
  }

  /// `Auto-renewable after trial. Cancel anytime`
  String get auto_renewable_after_trial {
    return Intl.message(
      'Auto-renewable after trial. Cancel anytime',
      name: 'auto_renewable_after_trial',
      desc: '',
      args: [],
    );
  }

  /// `Auto-renewal, cancel anytime`
  String get auto_renewal {
    return Intl.message(
      'Auto-renewal, cancel anytime',
      name: 'auto_renewal',
      desc: '',
      args: [],
    );
  }

  /// `Add Note`
  String get add_note {
    return Intl.message(
      'Add Note',
      name: 'add_note',
      desc: '',
      args: [],
    );
  }

  /// `Time to drop your first AI note!  ✨`
  String get this_folder_empty {
    return Intl.message(
      'Time to drop your first AI note!  ✨',
      name: 'this_folder_empty',
      desc: '',
      args: [],
    );
  }

  /// `Yearly`
  String get yearly {
    return Intl.message(
      'Yearly',
      name: 'yearly',
      desc: '',
      args: [],
    );
  }

  /// `Weekly`
  String get weekly {
    return Intl.message(
      'Weekly',
      name: 'weekly',
      desc: '',
      args: [],
    );
  }

  /// `Monthly`
  String get monthly {
    return Intl.message(
      'Monthly',
      name: 'monthly',
      desc: '',
      args: [],
    );
  }

  /// `Restore purchase`
  String get restore_purchase {
    return Intl.message(
      'Restore purchase',
      name: 'restore_purchase',
      desc: '',
      args: [],
    );
  }

  /// `Congratulations!`
  String get congratulations {
    return Intl.message(
      'Congratulations!',
      name: 'congratulations',
      desc: '',
      args: [],
    );
  }

  /// `You've scored `
  String get front_content {
    return Intl.message(
      'You\'ve scored ',
      name: 'front_content',
      desc: '',
      args: [],
    );
  }

  /// ` points`
  String get back_content {
    return Intl.message(
      ' points',
      name: 'back_content',
      desc: '',
      args: [],
    );
  }

  /// `Total`
  String get total {
    return Intl.message(
      'Total',
      name: 'total',
      desc: '',
      args: [],
    );
  }

  /// `Correct`
  String get correct {
    return Intl.message(
      'Correct',
      name: 'correct',
      desc: '',
      args: [],
    );
  }

  /// `Wrong`
  String get wrong {
    return Intl.message(
      'Wrong',
      name: 'wrong',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong with the connection.\nPlease try again`
  String get error_connection {
    return Intl.message(
      'Something went wrong with the connection.\nPlease try again',
      name: 'error_connection',
      desc: '',
      args: [],
    );
  }

  /// `Note is successfully created!`
  String get create_note_successfully {
    return Intl.message(
      'Note is successfully created!',
      name: 'create_note_successfully',
      desc: '',
      args: [],
    );
  }

  /// `Fail to generate AI notes!`
  String get generate_note_fail {
    return Intl.message(
      'Fail to generate AI notes!',
      name: 'generate_note_fail',
      desc: '',
      args: [],
    );
  }

  /// `We encountered a slight hiccup while generating your notes. Please try again!`
  String get try_again {
    return Intl.message(
      'We encountered a slight hiccup while generating your notes. Please try again!',
      name: 'try_again',
      desc: '',
      args: [],
    );
  }

  /// `Question`
  String get question {
    return Intl.message(
      'Question',
      name: 'question',
      desc: '',
      args: [],
    );
  }

  /// `Answer`
  String get answer {
    return Intl.message(
      'Answer',
      name: 'answer',
      desc: '',
      args: [],
    );
  }

  /// `PRO Plan`
  String get pro {
    return Intl.message(
      'PRO Plan',
      name: 'pro',
      desc: '',
      args: [],
    );
  }

  /// `Note`
  String get note {
    return Intl.message(
      'Note',
      name: 'note',
      desc: '',
      args: [],
    );
  }

  /// `X`
  String get x {
    return Intl.message(
      'X',
      name: 'x',
      desc: '',
      args: [],
    );
  }

  /// `Error, tap to reload`
  String get reload_tap {
    return Intl.message(
      'Error, tap to reload',
      name: 'reload_tap',
      desc: '',
      args: [],
    );
  }

  /// `Welcome to NoteX!`
  String get welcome_notex {
    return Intl.message(
      'Welcome to NoteX!',
      name: 'welcome_notex',
      desc: '',
      args: [],
    );
  }

  /// `#1 AI Notetaker`
  String get note_taker {
    return Intl.message(
      '#1 AI Notetaker',
      name: 'note_taker',
      desc: '',
      args: [],
    );
  }

  /// `Recordings, Videos & Docs to AI Notes`
  String get video_audio {
    return Intl.message(
      'Recordings, Videos & Docs to AI Notes',
      name: 'video_audio',
      desc: '',
      args: [],
    );
  }

  /// `Interactive AI Mind Map`
  String get interactive_ai_flashcards {
    return Intl.message(
      'Interactive AI Mind Map',
      name: 'interactive_ai_flashcards',
      desc: '',
      args: [],
    );
  }

  /// `Interactive Flashcards`
  String get interactive_flash {
    return Intl.message(
      'Interactive Flashcards',
      name: 'interactive_flash',
      desc: '',
      args: [],
    );
  }

  /// `Nothing to restore`
  String get nothing_restore {
    return Intl.message(
      'Nothing to restore',
      name: 'nothing_restore',
      desc: '',
      args: [],
    );
  }

  /// `An active description could not be found.`
  String get active_description {
    return Intl.message(
      'An active description could not be found.',
      name: 'active_description',
      desc: '',
      args: [],
    );
  }

  /// `Learn Smart `
  String get learn_smart {
    return Intl.message(
      'Learn Smart ',
      name: 'learn_smart',
      desc: '',
      args: [],
    );
  }

  /// `Go Unlimited!`
  String get go_unlimited {
    return Intl.message(
      'Go Unlimited!',
      name: 'go_unlimited',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited AI Notes from Recordings, Audio files, Documents, and YouTube videos`
  String get detail_unlimited_ai_summaries {
    return Intl.message(
      'Unlimited AI Notes from Recordings, Audio files, Documents, and YouTube videos',
      name: 'detail_unlimited_ai_summaries',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited interactive Flashcards, Mind maps`
  String get interactive_flashcards {
    return Intl.message(
      'Unlimited interactive Flashcards, Mind maps',
      name: 'interactive_flashcards',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited adaptive Quizzes`
  String get smart_quizzes {
    return Intl.message(
      'Unlimited adaptive Quizzes',
      name: 'smart_quizzes',
      desc: '',
      args: [],
    );
  }

  /// `100+ Languages supported`
  String get support_over_onehundred_languages {
    return Intl.message(
      '100+ Languages supported',
      name: 'support_over_onehundred_languages',
      desc: '',
      args: [],
    );
  }

  /// `Rated 4.8/5: Trusted by thousands`
  String get thousands_trusted {
    return Intl.message(
      'Rated 4.8/5: Trusted by thousands',
      name: 'thousands_trusted',
      desc: '',
      args: [],
    );
  }

  /// `Summarize Long YouTube Videos`
  String get summarize_video {
    return Intl.message(
      'Summarize Long YouTube Videos',
      name: 'summarize_video',
      desc: '',
      args: [],
    );
  }

  /// `Generate smart summaries of YouTube content`
  String get generate_content {
    return Intl.message(
      'Generate smart summaries of YouTube content',
      name: 'generate_content',
      desc: '',
      args: [],
    );
  }

  /// `Record & Summarize College Lectures`
  String get record_summarize_lecture {
    return Intl.message(
      'Record & Summarize College Lectures',
      name: 'record_summarize_lecture',
      desc: '',
      args: [],
    );
  }

  /// `Create concise summaries of your lectures`
  String get create_lecture {
    return Intl.message(
      'Create concise summaries of your lectures',
      name: 'create_lecture',
      desc: '',
      args: [],
    );
  }

  /// `Smart Learning`
  String get smart_learning {
    return Intl.message(
      'Smart Learning',
      name: 'smart_learning',
      desc: '',
      args: [],
    );
  }

  /// `Boost your understanding with AI-generated flashcards and quizzes`
  String get boost_flashcards_quizzes {
    return Intl.message(
      'Boost your understanding with AI-generated flashcards and quizzes',
      name: 'boost_flashcards_quizzes',
      desc: '',
      args: [],
    );
  }

  /// `Business Uses`
  String get business_uses {
    return Intl.message(
      'Business Uses',
      name: 'business_uses',
      desc: '',
      args: [],
    );
  }

  /// `Summarize meetings, podcasts, tutorials, and more`
  String get more_summarize {
    return Intl.message(
      'Summarize meetings, podcasts, tutorials, and more',
      name: 'more_summarize',
      desc: '',
      args: [],
    );
  }

  /// `Skip`
  String get skip {
    return Intl.message(
      'Skip',
      name: 'skip',
      desc: '',
      args: [],
    );
  }

  /// `What is your primary`
  String get your_primary {
    return Intl.message(
      'What is your primary',
      name: 'your_primary',
      desc: '',
      args: [],
    );
  }

  /// `purpose for using `
  String get purpose_using {
    return Intl.message(
      'purpose for using ',
      name: 'purpose_using',
      desc: '',
      args: [],
    );
  }

  /// `X?`
  String get x_skip {
    return Intl.message(
      'X?',
      name: 'x_skip',
      desc: '',
      args: [],
    );
  }

  /// `Your responses will help us improve`
  String get improve_responses {
    return Intl.message(
      'Your responses will help us improve',
      name: 'improve_responses',
      desc: '',
      args: [],
    );
  }

  /// `your experience with NoteX`
  String get notex_experience {
    return Intl.message(
      'your experience with NoteX',
      name: 'notex_experience',
      desc: '',
      args: [],
    );
  }

  /// `The application has encountered an unknown error`
  String get unknown_error {
    return Intl.message(
      'The application has encountered an unknown error',
      name: 'unknown_error',
      desc: '',
      args: [],
    );
  }

  /// `Please try again`
  String get please_try_again {
    return Intl.message(
      'Please try again',
      name: 'please_try_again',
      desc: '',
      args: [],
    );
  }

  /// `Try Again`
  String get try_again_button {
    return Intl.message(
      'Try Again',
      name: 'try_again_button',
      desc: '',
      args: [],
    );
  }

  /// `YouTube Summary Limit`
  String get yt_sum_limit {
    return Intl.message(
      'YouTube Summary Limit',
      name: 'yt_sum_limit',
      desc: '',
      args: [],
    );
  }

  /// `Free users can summarize 1 YouTube video (max 30 min) per day.`
  String get free_user_can {
    return Intl.message(
      'Free users can summarize 1 YouTube video (max 30 min) per day.',
      name: 'free_user_can',
      desc: '',
      args: [],
    );
  }

  /// `Weekly Free Limit Reached`
  String get week_free_limit {
    return Intl.message(
      'Weekly Free Limit Reached',
      name: 'week_free_limit',
      desc: '',
      args: [],
    );
  }

  /// `You've used all 30-minute free transcriptions and AI summaries for this week. Upgrade to Pro for unlimited access or wait until next week for your free quota to reset.`
  String get minute_free {
    return Intl.message(
      'You\'ve used all 30-minute free transcriptions and AI summaries for this week. Upgrade to Pro for unlimited access or wait until next week for your free quota to reset.',
      name: 'minute_free',
      desc: '',
      args: [],
    );
  }

  /// `Got it !`
  String get got_it {
    return Intl.message(
      'Got it !',
      name: 'got_it',
      desc: '',
      args: [],
    );
  }

  /// `Your recordings will be saved locally without AI transcriptions and summarization. You can remove all limits to process this recording after it's complete.`
  String get your_recording_will_save {
    return Intl.message(
      'Your recordings will be saved locally without AI transcriptions and summarization. You can remove all limits to process this recording after it\'s complete.',
      name: 'your_recording_will_save',
      desc: '',
      args: [],
    );
  }

  /// `Your recordings will be saved locally without AI transcriptions and summarization if the recording session is over this free usage left this week. You can remove all limits by going Pro.`
  String get content_minute_left {
    return Intl.message(
      'Your recordings will be saved locally without AI transcriptions and summarization if the recording session is over this free usage left this week. You can remove all limits by going Pro.',
      name: 'content_minute_left',
      desc: '',
      args: [],
    );
  }

  /// `The uploaded file is in the wrong format. Please upload it again.`
  String get invalid_file_type {
    return Intl.message(
      'The uploaded file is in the wrong format. Please upload it again.',
      name: 'invalid_file_type',
      desc: '',
      args: [],
    );
  }

  /// `Recording Permission is Denied!`
  String get recording_permission_denied {
    return Intl.message(
      'Recording Permission is Denied!',
      name: 'recording_permission_denied',
      desc: '',
      args: [],
    );
  }

  /// `Go to Setting to allow`
  String get recording_permission_denied_details {
    return Intl.message(
      'Go to Setting to allow',
      name: 'recording_permission_denied_details',
      desc: '',
      args: [],
    );
  }

  /// `Settings`
  String get setting {
    return Intl.message(
      'Settings',
      name: 'setting',
      desc: '',
      args: [],
    );
  }

  /// `Share audio`
  String get share_audio_file {
    return Intl.message(
      'Share audio',
      name: 'share_audio_file',
      desc: '',
      args: [],
    );
  }

  /// `Share audio file`
  String get download_audio_file {
    return Intl.message(
      'Share audio file',
      name: 'download_audio_file',
      desc: '',
      args: [],
    );
  }

  /// `Automated summary will appear here after the meeting is finished.`
  String get content_empty_quiz {
    return Intl.message(
      'Automated summary will appear here after the meeting is finished.',
      name: 'content_empty_quiz',
      desc: '',
      args: [],
    );
  }

  /// `Automated summary will appear here after the meeting is finished.`
  String get content_empty_flashcard {
    return Intl.message(
      'Automated summary will appear here after the meeting is finished.',
      name: 'content_empty_flashcard',
      desc: '',
      args: [],
    );
  }

  /// `Create Quizzes`
  String get content_button_quiz {
    return Intl.message(
      'Create Quizzes',
      name: 'content_button_quiz',
      desc: '',
      args: [],
    );
  }

  /// `Create Flashcards`
  String get content_button_flashcard {
    return Intl.message(
      'Create Flashcards',
      name: 'content_button_flashcard',
      desc: '',
      args: [],
    );
  }

  /// `Create Mindmap`
  String get content_button_mindmap {
    return Intl.message(
      'Create Mindmap',
      name: 'content_button_mindmap',
      desc: '',
      args: [],
    );
  }

  /// ` Minutes Free Left`
  String get minutes_free_left {
    return Intl.message(
      ' Minutes Free Left',
      name: 'minutes_free_left',
      desc: '',
      args: [],
    );
  }

  /// `Purchase fail! Please try again!`
  String get purchase_fail {
    return Intl.message(
      'Purchase fail! Please try again!',
      name: 'purchase_fail',
      desc: '',
      args: [],
    );
  }

  /// `You have used up your free credits`
  String get payment_required {
    return Intl.message(
      'You have used up your free credits',
      name: 'payment_required',
      desc: '',
      args: [],
    );
  }

  /// `Daily rewards limit reached. Try again tomorrow!`
  String get daily_rewards_limit_reached {
    return Intl.message(
      'Daily rewards limit reached. Try again tomorrow!',
      name: 'daily_rewards_limit_reached',
      desc: '',
      args: [],
    );
  }

  /// `Image too large. Upload one under 10MB.`
  String get image_too_large {
    return Intl.message(
      'Image too large. Upload one under 10MB.',
      name: 'image_too_large',
      desc: '',
      args: [],
    );
  }

  /// `Image quality too low. Use a higher quality image!`
  String get image_quality_too_low {
    return Intl.message(
      'Image quality too low. Use a higher quality image!',
      name: 'image_quality_too_low',
      desc: '',
      args: [],
    );
  }

  /// `No person detected. Upload an image with a person!`
  String get no_person_detected {
    return Intl.message(
      'No person detected. Upload an image with a person!',
      name: 'no_person_detected',
      desc: '',
      args: [],
    );
  }

  /// `Child detected. Upload a different image.`
  String get child_detected {
    return Intl.message(
      'Child detected. Upload a different image.',
      name: 'child_detected',
      desc: '',
      args: [],
    );
  }

  /// `Multiple people detected. Upload a single-person image!`
  String get multiple_people_detected {
    return Intl.message(
      'Multiple people detected. Upload a single-person image!',
      name: 'multiple_people_detected',
      desc: '',
      args: [],
    );
  }

  /// `Token has expired!`
  String get token_expired {
    return Intl.message(
      'Token has expired!',
      name: 'token_expired',
      desc: '',
      args: [],
    );
  }

  /// `Invalid token`
  String get invalid_token {
    return Intl.message(
      'Invalid token',
      name: 'invalid_token',
      desc: '',
      args: [],
    );
  }

  /// `Style generation failed! Please choose a different style or change the image!`
  String get blurred_output_image {
    return Intl.message(
      'Style generation failed! Please choose a different style or change the image!',
      name: 'blurred_output_image',
      desc: '',
      args: [],
    );
  }

  /// `Unable to process audio file. Please try again with a different file.`
  String get audio_process_err {
    return Intl.message(
      'Unable to process audio file. Please try again with a different file.',
      name: 'audio_process_err',
      desc: '',
      args: [],
    );
  }

  /// `Error processing YouTube video. Please check the URL and try again.`
  String get yt_process_err {
    return Intl.message(
      'Error processing YouTube video. Please check the URL and try again.',
      name: 'yt_process_err',
      desc: '',
      args: [],
    );
  }

  /// `Invalid YouTube URL. Please provide a valid YouTube link.`
  String get inv_yt_url {
    return Intl.message(
      'Invalid YouTube URL. Please provide a valid YouTube link.',
      name: 'inv_yt_url',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient YouTube free usage. Please upgrade your plan.`
  String get yt_credit_err {
    return Intl.message(
      'Insufficient YouTube free usage. Please upgrade your plan.',
      name: 'yt_credit_err',
      desc: '',
      args: [],
    );
  }

  /// `Error using YouTube free usage. Please try again later.`
  String get yt_credit_use_err {
    return Intl.message(
      'Error using YouTube free usage. Please try again later.',
      name: 'yt_credit_use_err',
      desc: '',
      args: [],
    );
  }

  /// `Invalid audio file. Please upload a supported audio format.`
  String get inv_audio {
    return Intl.message(
      'Invalid audio file. Please upload a supported audio format.',
      name: 'inv_audio',
      desc: '',
      args: [],
    );
  }

  /// `Audio file exceeds maximum length. Please upload a shorter file.`
  String get audio_length_err {
    return Intl.message(
      'Audio file exceeds maximum length. Please upload a shorter file.',
      name: 'audio_length_err',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient recording remaining usage. Please upgrade your plan.`
  String get no_recording_credit {
    return Intl.message(
      'Insufficient recording remaining usage. Please upgrade your plan.',
      name: 'no_recording_credit',
      desc: '',
      args: [],
    );
  }

  /// `Insufficient upload usage. Please upgrade your plan.`
  String get no_upload_credit {
    return Intl.message(
      'Insufficient upload usage. Please upgrade your plan.',
      name: 'no_upload_credit',
      desc: '',
      args: [],
    );
  }

  /// `File size exceeds the limit. Please upload a smaller file.`
  String get file_size_err {
    return Intl.message(
      'File size exceeds the limit. Please upload a smaller file.',
      name: 'file_size_err',
      desc: '',
      args: [],
    );
  }

  /// `No input provided. Please upload an audio file or enter a YouTube URL.`
  String get no_input {
    return Intl.message(
      'No input provided. Please upload an audio file or enter a YouTube URL.',
      name: 'no_input',
      desc: '',
      args: [],
    );
  }

  /// `Oops! Our servers stumbled. Please try again.`
  String get unknown_server_error {
    return Intl.message(
      'Oops! Our servers stumbled. Please try again.',
      name: 'unknown_server_error',
      desc: '',
      args: [],
    );
  }

  /// `Database error. Please try again later.`
  String get db_err {
    return Intl.message(
      'Database error. Please try again later.',
      name: 'db_err',
      desc: '',
      args: [],
    );
  }

  /// `Note not found. Please check the note ID and try again.`
  String get note_404 {
    return Intl.message(
      'Note not found. Please check the note ID and try again.',
      name: 'note_404',
      desc: '',
      args: [],
    );
  }

  /// `No summary available for this note.`
  String get no_summary {
    return Intl.message(
      'No summary available for this note.',
      name: 'no_summary',
      desc: '',
      args: [],
    );
  }

  /// `No transcript available for this note.`
  String get no_transcript {
    return Intl.message(
      'No transcript available for this note.',
      name: 'no_transcript',
      desc: '',
      args: [],
    );
  }

  /// `Error creating task. Please try again later.`
  String get task_create_err {
    return Intl.message(
      'Error creating task. Please try again later.',
      name: 'task_create_err',
      desc: '',
      args: [],
    );
  }

  /// `Failed to update community notes. Please try again.`
  String get update_failed {
    return Intl.message(
      'Failed to update community notes. Please try again.',
      name: 'update_failed',
      desc: '',
      args: [],
    );
  }

  /// `YouTube video exceeds the 10-hour limit. Please choose a shorter video.`
  String get yt_length_err {
    return Intl.message(
      'YouTube video exceeds the 10-hour limit. Please choose a shorter video.',
      name: 'yt_length_err',
      desc: '',
      args: [],
    );
  }

  /// `No internet connection`
  String get no_internet {
    return Intl.message(
      'No internet connection',
      name: 'no_internet',
      desc: '',
      args: [],
    );
  }

  /// `Request timeout. Please try again.`
  String get time_out {
    return Intl.message(
      'Request timeout. Please try again.',
      name: 'time_out',
      desc: '',
      args: [],
    );
  }

  /// `Something went wrong! Please try again!`
  String get default_error {
    return Intl.message(
      'Something went wrong! Please try again!',
      name: 'default_error',
      desc: '',
      args: [],
    );
  }

  /// `Loading`
  String get loading {
    return Intl.message(
      'Loading',
      name: 'loading',
      desc: '',
      args: [],
    );
  }

  /// `30s - 5min remaining based on recording length...`
  String get remain_recording_length {
    return Intl.message(
      '30s - 5min remaining based on recording length...',
      name: 'remain_recording_length',
      desc: '',
      args: [],
    );
  }

  /// `Processing file..`
  String get processing_file {
    return Intl.message(
      'Processing file..',
      name: 'processing_file',
      desc: '',
      args: [],
    );
  }

  /// `Transcribing audio..`
  String get transcribing_audio {
    return Intl.message(
      'Transcribing audio..',
      name: 'transcribing_audio',
      desc: '',
      args: [],
    );
  }

  /// `Generating AI summary..`
  String get generating_summary {
    return Intl.message(
      'Generating AI summary..',
      name: 'generating_summary',
      desc: '',
      args: [],
    );
  }

  /// `Create AI notes..`
  String get create_notes {
    return Intl.message(
      'Create AI notes..',
      name: 'create_notes',
      desc: '',
      args: [],
    );
  }

  /// `Producing AI Flashcards..`
  String get producing_flashcards {
    return Intl.message(
      'Producing AI Flashcards..',
      name: 'producing_flashcards',
      desc: '',
      args: [],
    );
  }

  /// `Developing quizzes..`
  String get developing_quizzes {
    return Intl.message(
      'Developing quizzes..',
      name: 'developing_quizzes',
      desc: '',
      args: [],
    );
  }

  /// `Finalizing..`
  String get finalizing {
    return Intl.message(
      'Finalizing..',
      name: 'finalizing',
      desc: '',
      args: [],
    );
  }

  /// `Enter title`
  String get enter_title {
    return Intl.message(
      'Enter title',
      name: 'enter_title',
      desc: '',
      args: [],
    );
  }

  /// `Failed to get jwt anonymous user`
  String get failed_get_anonymous_user {
    return Intl.message(
      'Failed to get jwt anonymous user',
      name: 'failed_get_anonymous_user',
      desc: '',
      args: [],
    );
  }

  /// `Error converting ui.Image to image.Image`
  String get error_convert_image {
    return Intl.message(
      'Error converting ui.Image to image.Image',
      name: 'error_convert_image',
      desc: '',
      args: [],
    );
  }

  /// `Fail to get quiz/flashcards/mindmap. Please try again!`
  String get get_fail {
    return Intl.message(
      'Fail to get quiz/flashcards/mindmap. Please try again!',
      name: 'get_fail',
      desc: '',
      args: [],
    );
  }

  /// `Export Summary`
  String get export_pdf {
    return Intl.message(
      'Export Summary',
      name: 'export_pdf',
      desc: '',
      args: [],
    );
  }

  /// `Share Note`
  String get share_note_link {
    return Intl.message(
      'Share Note',
      name: 'share_note_link',
      desc: '',
      args: [],
    );
  }

  /// `Failed to create PDF file`
  String get fail_create_pdf {
    return Intl.message(
      'Failed to create PDF file',
      name: 'fail_create_pdf',
      desc: '',
      args: [],
    );
  }

  /// `Generate Summary`
  String get content_button_summary {
    return Intl.message(
      'Generate Summary',
      name: 'content_button_summary',
      desc: '',
      args: [],
    );
  }

  /// `Help Us Grow!`
  String get help_us_grow {
    return Intl.message(
      'Help Us Grow!',
      name: 'help_us_grow',
      desc: '',
      args: [],
    );
  }

  /// `Show your love by giving us a`
  String get show_your_love {
    return Intl.message(
      'Show your love by giving us a',
      name: 'show_your_love',
      desc: '',
      args: [],
    );
  }

  /// `Community & Feedback`
  String get community_feedback {
    return Intl.message(
      'Community & Feedback',
      name: 'community_feedback',
      desc: '',
      args: [],
    );
  }

  /// `Free Usage - Weekly`
  String get free_usage {
    return Intl.message(
      'Free Usage - Weekly',
      name: 'free_usage',
      desc: '',
      args: [],
    );
  }

  /// `Recording`
  String get recording {
    return Intl.message(
      'Recording',
      name: 'recording',
      desc: '',
      args: [],
    );
  }

  /// `Upload Audio`
  String get upload_audio {
    return Intl.message(
      'Upload Audio',
      name: 'upload_audio',
      desc: '',
      args: [],
    );
  }

  /// ` of %s mins`
  String get period {
    return Intl.message(
      ' of %s mins',
      name: 'period',
      desc: '',
      args: [],
    );
  }

  /// `review on the App Store`
  String get app_store {
    return Intl.message(
      'review on the App Store',
      name: 'app_store',
      desc: '',
      args: [],
    );
  }

  /// `AI Summarize`
  String get ai_summarize {
    return Intl.message(
      'AI Summarize',
      name: 'ai_summarize',
      desc: '',
      args: [],
    );
  }

  /// `Copied to clipboard`
  String get copied_to_clipboard {
    return Intl.message(
      'Copied to clipboard',
      name: 'copied_to_clipboard',
      desc: '',
      args: [],
    );
  }

  /// `Copy Transcript`
  String get share_transcript {
    return Intl.message(
      'Copy Transcript',
      name: 'share_transcript',
      desc: '',
      args: [],
    );
  }

  /// `Copy Summary`
  String get share_summary {
    return Intl.message(
      'Copy Summary',
      name: 'share_summary',
      desc: '',
      args: [],
    );
  }

  /// `Contact Support`
  String get contact_support {
    return Intl.message(
      'Contact Support',
      name: 'contact_support',
      desc: '',
      args: [],
    );
  }

  /// `Suggest Features`
  String get suggest_features {
    return Intl.message(
      'Suggest Features',
      name: 'suggest_features',
      desc: '',
      args: [],
    );
  }

  /// `Edit`
  String get edit {
    return Intl.message(
      'Edit',
      name: 'edit',
      desc: '',
      args: [],
    );
  }

  /// `Summary`
  String get summary {
    return Intl.message(
      'Summary',
      name: 'summary',
      desc: '',
      args: [],
    );
  }

  /// `Transcript`
  String get transcript {
    return Intl.message(
      'Transcript',
      name: 'transcript',
      desc: '',
      args: [],
    );
  }

  /// `Flashcards`
  String get flashcard {
    return Intl.message(
      'Flashcards',
      name: 'flashcard',
      desc: '',
      args: [],
    );
  }

  /// `Click 'Create Quizzes' to generate question sets based on the transcript. You can create multiple sets.`
  String get click_start_quiz {
    return Intl.message(
      'Click \'Create Quizzes\' to generate question sets based on the transcript. You can create multiple sets.',
      name: 'click_start_quiz',
      desc: '',
      args: [],
    );
  }

  /// `Click 'Create Flashcards' to generate flashcard sets based on the transcript. You can create multiple sets.`
  String get click_start_flashcard {
    return Intl.message(
      'Click \'Create Flashcards\' to generate flashcard sets based on the transcript. You can create multiple sets.',
      name: 'click_start_flashcard',
      desc: '',
      args: [],
    );
  }

  /// `Click 'Create Mindmap' to generate a set of mindmap based on the transcript`
  String get click_start_mindmap {
    return Intl.message(
      'Click \'Create Mindmap\' to generate a set of mindmap based on the transcript',
      name: 'click_start_mindmap',
      desc: '',
      args: [],
    );
  }

  /// `Hours of content to insights`
  String get content_hour_insight {
    return Intl.message(
      'Hours of content to insights',
      name: 'content_hour_insight',
      desc: '',
      args: [],
    );
  }

  /// `Hours of content to`
  String get content_hour {
    return Intl.message(
      'Hours of content to',
      name: 'content_hour',
      desc: '',
      args: [],
    );
  }

  /// `instantly`
  String get instantly {
    return Intl.message(
      'instantly',
      name: 'instantly',
      desc: '',
      args: [],
    );
  }

  /// `Hours of content to insights instantly`
  String get insight_instantly {
    return Intl.message(
      'Hours of content to insights instantly',
      name: 'insight_instantly',
      desc: '',
      args: [],
    );
  }

  /// `insights instantly`
  String get insights_instantly {
    return Intl.message(
      'insights instantly',
      name: 'insights_instantly',
      desc: '',
      args: [],
    );
  }

  /// `Connect the dots between`
  String get boost_knowledge_retention {
    return Intl.message(
      'Connect the dots between',
      name: 'boost_knowledge_retention',
      desc: '',
      args: [],
    );
  }

  /// `Connect the dots between concepts`
  String get between_concepts {
    return Intl.message(
      'Connect the dots between concepts',
      name: 'between_concepts',
      desc: '',
      args: [],
    );
  }

  /// `Boost comprehension and retention`
  String get boost_comprehension {
    return Intl.message(
      'Boost comprehension and retention',
      name: 'boost_comprehension',
      desc: '',
      args: [],
    );
  }

  /// `Connect the dots`
  String get boost_knowledge {
    return Intl.message(
      'Connect the dots',
      name: 'boost_knowledge',
      desc: '',
      args: [],
    );
  }

  /// `concepts`
  String get quickly {
    return Intl.message(
      'concepts',
      name: 'quickly',
      desc: '',
      args: [],
    );
  }

  /// `between concepts`
  String get retention_quickly {
    return Intl.message(
      'between concepts',
      name: 'retention_quickly',
      desc: '',
      args: [],
    );
  }

  /// `AI Quiz Master`
  String get quiz_master {
    return Intl.message(
      'AI Quiz Master',
      name: 'quiz_master',
      desc: '',
      args: [],
    );
  }

  /// `Generate Audio & Video`
  String get onboarding_generate_audio_video_title {
    return Intl.message(
      'Generate Audio & Video',
      name: 'onboarding_generate_audio_video_title',
      desc: '',
      args: [],
    );
  }

  /// `Transform notes into`
  String get onboarding_generate_audio_video_content {
    return Intl.message(
      'Transform notes into',
      name: 'onboarding_generate_audio_video_content',
      desc: '',
      args: [],
    );
  }

  /// `engaging content`
  String get onboarding_generate_audio_video_sub_content {
    return Intl.message(
      'engaging content',
      name: 'onboarding_generate_audio_video_sub_content',
      desc: '',
      args: [],
    );
  }

  /// `Transform notes into engaging content`
  String get onboarding_generate_audio_video_full_content {
    return Intl.message(
      'Transform notes into engaging content',
      name: 'onboarding_generate_audio_video_full_content',
      desc: '',
      args: [],
    );
  }

  /// `Practice your way to an A+`
  String get personalized_learning_at {
    return Intl.message(
      'Practice your way to an A+',
      name: 'personalized_learning_at',
      desc: '',
      args: [],
    );
  }

  /// `Practice your way to`
  String get personalized_learning {
    return Intl.message(
      'Practice your way to',
      name: 'personalized_learning',
      desc: '',
      args: [],
    );
  }

  /// `pace`
  String get pace {
    return Intl.message(
      'pace',
      name: 'pace',
      desc: '',
      args: [],
    );
  }

  /// `an A+`
  String get at_your_pace {
    return Intl.message(
      'an A+',
      name: 'at_your_pace',
      desc: '',
      args: [],
    );
  }

  /// `Quick Access`
  String get quick_access {
    return Intl.message(
      'Quick Access',
      name: 'quick_access',
      desc: '',
      args: [],
    );
  }

  /// `Supercharge Your Learning!`
  String get your_learning {
    return Intl.message(
      'Supercharge Your Learning!',
      name: 'your_learning',
      desc: '',
      args: [],
    );
  }

  /// `Achieve More, Stress Less`
  String get supercharge {
    return Intl.message(
      'Achieve More, Stress Less',
      name: 'supercharge',
      desc: '',
      args: [],
    );
  }

  /// `NoteX Pro Access`
  String get your_learning_device {
    return Intl.message(
      'NoteX Pro Access',
      name: 'your_learning_device',
      desc: '',
      args: [],
    );
  }

  /// `Creating note ...`
  String get creating_note {
    return Intl.message(
      'Creating note ...',
      name: 'creating_note',
      desc: '',
      args: [],
    );
  }

  /// `Terms`
  String get terms {
    return Intl.message(
      'Terms',
      name: 'terms',
      desc: '',
      args: [],
    );
  }

  /// `Policy`
  String get policy {
    return Intl.message(
      'Policy',
      name: 'policy',
      desc: '',
      args: [],
    );
  }

  /// `Restore`
  String get restore {
    return Intl.message(
      'Restore',
      name: 'restore',
      desc: '',
      args: [],
    );
  }

  /// `Summary is successfully created!`
  String get summary_successful {
    return Intl.message(
      'Summary is successfully created!',
      name: 'summary_successful',
      desc: '',
      args: [],
    );
  }

  /// `Summary missing! Hit that button to get the AI to work👇`
  String get not_summarized_note {
    return Intl.message(
      'Summary missing! Hit that button to get the AI to work👇',
      name: 'not_summarized_note',
      desc: '',
      args: [],
    );
  }

  /// `Transcript will be available after note is successfully created!`
  String get available_transcript {
    return Intl.message(
      'Transcript will be available after note is successfully created!',
      name: 'available_transcript',
      desc: '',
      args: [],
    );
  }

  /// `Feedback for NoteX app`
  String get app_feedback {
    return Intl.message(
      'Feedback for NoteX app',
      name: 'app_feedback',
      desc: '',
      args: [],
    );
  }

  /// `Could not open mail!`
  String get not_open_mail {
    return Intl.message(
      'Could not open mail!',
      name: 'not_open_mail',
      desc: '',
      args: [],
    );
  }

  /// `Could not open web!`
  String get not_open_web {
    return Intl.message(
      'Could not open web!',
      name: 'not_open_web',
      desc: '',
      args: [],
    );
  }

  /// `Unable to load audio:`
  String get unable_load_audio {
    return Intl.message(
      'Unable to load audio:',
      name: 'unable_load_audio',
      desc: '',
      args: [],
    );
  }

  /// `Audio file not found`
  String get not_found_audio {
    return Intl.message(
      'Audio file not found',
      name: 'not_found_audio',
      desc: '',
      args: [],
    );
  }

  /// `Unable to share audio file`
  String get unable_share_audio {
    return Intl.message(
      'Unable to share audio file',
      name: 'unable_share_audio',
      desc: '',
      args: [],
    );
  }

  /// `File has been saved`
  String get save_file {
    return Intl.message(
      'File has been saved',
      name: 'save_file',
      desc: '',
      args: [],
    );
  }

  /// `Unable to download file`
  String get unable_download_file {
    return Intl.message(
      'Unable to download file',
      name: 'unable_download_file',
      desc: '',
      args: [],
    );
  }

  /// `New Recording - `
  String get new_recording {
    return Intl.message(
      'New Recording - ',
      name: 'new_recording',
      desc: '',
      args: [],
    );
  }

  /// `uid {uid} copied to clipboard!`
  String uidCopied(Object uid) {
    return Intl.message(
      'uid $uid copied to clipboard!',
      name: 'uidCopied',
      desc: '',
      args: [uid],
    );
  }

  /// `How to Report an Issue?`
  String get report_issue {
    return Intl.message(
      'How to Report an Issue?',
      name: 'report_issue',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for reaching out. To help us investigate and resolve your issue more effectively, please follow these steps:`
  String get introduce_guidance {
    return Intl.message(
      'Thank you for reaching out. To help us investigate and resolve your issue more effectively, please follow these steps:',
      name: 'introduce_guidance',
      desc: '',
      args: [],
    );
  }

  /// `This information will help our support team quickly identify and address your specific issue. We appreciate your cooperation in improving NoteX for everyone.`
  String get appreciate_cooperation {
    return Intl.message(
      'This information will help our support team quickly identify and address your specific issue. We appreciate your cooperation in improving NoteX for everyone.',
      name: 'appreciate_cooperation',
      desc: '',
      args: [],
    );
  }

  /// `In the NoteX app, go to Settings.`
  String get step1 {
    return Intl.message(
      'In the NoteX app, go to Settings.',
      name: 'step1',
      desc: '',
      args: [],
    );
  }

  /// `Locate the app version at the bottom (e.g., v1.4.0(6)).`
  String get step2 {
    return Intl.message(
      'Locate the app version at the bottom (e.g., v1.4.0(6)).',
      name: 'step2',
      desc: '',
      args: [],
    );
  }

  /// `Tap the app version 5 times quickly.`
  String get step3 {
    return Intl.message(
      'Tap the app version 5 times quickly.',
      name: 'step3',
      desc: '',
      args: [],
    );
  }

  /// `Your unique userID will be automatically copied to your clipboard.`
  String get step4 {
    return Intl.message(
      'Your unique userID will be automatically copied to your clipboard.',
      name: 'step4',
      desc: '',
      args: [],
    );
  }

  /// `In your message below, please include:`
  String get step5 {
    return Intl.message(
      'In your message below, please include:',
      name: 'step5',
      desc: '',
      args: [],
    );
  }

  /// `Your userID (paste it from your clipboard).`
  String get step51 {
    return Intl.message(
      'Your userID (paste it from your clipboard).',
      name: 'step51',
      desc: '',
      args: [],
    );
  }

  /// `A brief description of the issue you're experiencing.`
  String get step52 {
    return Intl.message(
      'A brief description of the issue you\'re experiencing.',
      name: 'step52',
      desc: '',
      args: [],
    );
  }

  /// `Any relevant details (e.g., device model, iOS version).`
  String get step53 {
    return Intl.message(
      'Any relevant details (e.g., device model, iOS version).',
      name: 'step53',
      desc: '',
      args: [],
    );
  }

  /// `Send us an email at `
  String get step6 {
    return Intl.message(
      'Send us an email at ',
      name: 'step6',
      desc: '',
      args: [],
    );
  }

  /// `Go to Email`
  String get go_email {
    return Intl.message(
      'Go to Email',
      name: 'go_email',
      desc: '',
      args: [],
    );
  }

  /// `Learn Smart Go Unlimited!`
  String get learn_unlimited {
    return Intl.message(
      'Learn Smart Go Unlimited!',
      name: 'learn_unlimited',
      desc: '',
      args: [],
    );
  }

  /// `Speech Language`
  String get speech_language {
    return Intl.message(
      'Speech Language',
      name: 'speech_language',
      desc: '',
      args: [],
    );
  }

  /// `New note`
  String get new_note {
    return Intl.message(
      'New note',
      name: 'new_note',
      desc: '',
      args: [],
    );
  }

  /// `Mindmap`
  String get mind_map {
    return Intl.message(
      'Mindmap',
      name: 'mind_map',
      desc: '',
      args: [],
    );
  }

  /// `Edit Note`
  String get edit_note {
    return Intl.message(
      'Edit Note',
      name: 'edit_note',
      desc: '',
      args: [],
    );
  }

  /// `Start My 7-Day Trial`
  String get try_7_day {
    return Intl.message(
      'Start My 7-Day Trial',
      name: 'try_7_day',
      desc: '',
      args: [],
    );
  }

  /// `Try 3 Days Free`
  String get try_3_day {
    return Intl.message(
      'Try 3 Days Free',
      name: 'try_3_day',
      desc: '',
      args: [],
    );
  }

  /// `Move to`
  String get add_to {
    return Intl.message(
      'Move to',
      name: 'add_to',
      desc: '',
      args: [],
    );
  }

  /// `All Notes`
  String get all_note {
    return Intl.message(
      'All Notes',
      name: 'all_note',
      desc: '',
      args: [],
    );
  }

  /// `Folder`
  String get folder {
    return Intl.message(
      'Folder',
      name: 'folder',
      desc: '',
      args: [],
    );
  }

  /// `Shared`
  String get shared {
    return Intl.message(
      'Shared',
      name: 'shared',
      desc: '',
      args: [],
    );
  }

  /// `Create Folder`
  String get create_folder {
    return Intl.message(
      'Create Folder',
      name: 'create_folder',
      desc: '',
      args: [],
    );
  }

  /// `Create`
  String get create {
    return Intl.message(
      'Create',
      name: 'create',
      desc: '',
      args: [],
    );
  }

  /// `Fill the name folder`
  String get enter_folder_name {
    return Intl.message(
      'Fill the name folder',
      name: 'enter_folder_name',
      desc: '',
      args: [],
    );
  }

  /// `Edit Folder`
  String get edit_folder {
    return Intl.message(
      'Edit Folder',
      name: 'edit_folder',
      desc: '',
      args: [],
    );
  }

  /// `Delete Folder`
  String get delete_folder {
    return Intl.message(
      'Delete Folder',
      name: 'delete_folder',
      desc: '',
      args: [],
    );
  }

  /// `Delete this folder?`
  String get delete_this_folder {
    return Intl.message(
      'Delete this folder?',
      name: 'delete_this_folder',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to remove this folder?`
  String get all_note_in_folder {
    return Intl.message(
      'Are you sure you want to remove this folder?',
      name: 'all_note_in_folder',
      desc: '',
      args: [],
    );
  }

  /// `Delete all notes in folder`
  String get delete_all_note {
    return Intl.message(
      'Delete all notes in folder',
      name: 'delete_all_note',
      desc: '',
      args: [],
    );
  }

  /// `Enter the folder name`
  String get edit_folder_name {
    return Intl.message(
      'Enter the folder name',
      name: 'edit_folder_name',
      desc: '',
      args: [],
    );
  }

  /// `Edit Name`
  String get edit_name {
    return Intl.message(
      'Edit Name',
      name: 'edit_name',
      desc: '',
      args: [],
    );
  }

  /// `By continuing, you agree to our`
  String get by_taping_continue {
    return Intl.message(
      'By continuing, you agree to our',
      name: 'by_taping_continue',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Google`
  String get continue_with_google {
    return Intl.message(
      'Continue with Google',
      name: 'continue_with_google',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Apple`
  String get continue_with_apple {
    return Intl.message(
      'Continue with Apple',
      name: 'continue_with_apple',
      desc: '',
      args: [],
    );
  }

  /// `Account`
  String get account {
    return Intl.message(
      'Account',
      name: 'account',
      desc: '',
      args: [],
    );
  }

  /// `Delete account`
  String get delete_account {
    return Intl.message(
      'Delete account',
      name: 'delete_account',
      desc: '',
      args: [],
    );
  }

  /// `What's new`
  String get whats_new {
    return Intl.message(
      'What\'s new',
      name: 'whats_new',
      desc: '',
      args: [],
    );
  }

  /// `Log Out`
  String get logout {
    return Intl.message(
      'Log Out',
      name: 'logout',
      desc: '',
      args: [],
    );
  }

  /// `One-time offer`
  String get are_you_sure {
    return Intl.message(
      'One-time offer',
      name: 'are_you_sure',
      desc: '',
      args: [],
    );
  }

  /// `This free trial is an introductory offer for new users only. Experience all pro features for the whole week before deciding.`
  String get this_free_trial {
    return Intl.message(
      'This free trial is an introductory offer for new users only. Experience all pro features for the whole week before deciding.',
      name: 'this_free_trial',
      desc: '',
      args: [],
    );
  }

  /// `Decline free trial`
  String get decline_free_trial {
    return Intl.message(
      'Decline free trial',
      name: 'decline_free_trial',
      desc: '',
      args: [],
    );
  }

  /// `Start free trial`
  String get start_free_trial {
    return Intl.message(
      'Start free trial',
      name: 'start_free_trial',
      desc: '',
      args: [],
    );
  }

  /// `AI Notes Successfully Created`
  String get title_success_note {
    return Intl.message(
      'AI Notes Successfully Created',
      name: 'title_success_note',
      desc: '',
      args: [],
    );
  }

  /// `Your AI note is now ready for review.`
  String get body_success_note {
    return Intl.message(
      'Your AI note is now ready for review.',
      name: 'body_success_note',
      desc: '',
      args: [],
    );
  }

  /// `Note Creation Unsuccessful`
  String get title_error_note {
    return Intl.message(
      'Note Creation Unsuccessful',
      name: 'title_error_note',
      desc: '',
      args: [],
    );
  }

  /// `There was an issue processing your recording. Please go to the app and try again.`
  String get body_error_note_recording {
    return Intl.message(
      'There was an issue processing your recording. Please go to the app and try again.',
      name: 'body_error_note_recording',
      desc: '',
      args: [],
    );
  }

  /// `There was an issue processing your upload audio. Please go to the app and try again.`
  String get body_error_note_upload {
    return Intl.message(
      'There was an issue processing your upload audio. Please go to the app and try again.',
      name: 'body_error_note_upload',
      desc: '',
      args: [],
    );
  }

  /// `There was an issue processing your document. Please go to the app and try again.`
  String get body_error_note_document {
    return Intl.message(
      'There was an issue processing your document. Please go to the app and try again.',
      name: 'body_error_note_document',
      desc: '',
      args: [],
    );
  }

  /// `There was an issue processing your youtube link. Please go to the app and try again.`
  String get body_error_note_youtube {
    return Intl.message(
      'There was an issue processing your youtube link. Please go to the app and try again.',
      name: 'body_error_note_youtube',
      desc: '',
      args: [],
    );
  }

  /// `Uploading to secure server`
  String get uploading_to_server {
    return Intl.message(
      'Uploading to secure server',
      name: 'uploading_to_server',
      desc: '',
      args: [],
    );
  }

  /// `Saving recording to device`
  String get saving_recording {
    return Intl.message(
      'Saving recording to device',
      name: 'saving_recording',
      desc: '',
      args: [],
    );
  }

  /// `Transcribing with best AI`
  String get transcribing {
    return Intl.message(
      'Transcribing with best AI',
      name: 'transcribing',
      desc: '',
      args: [],
    );
  }

  /// `Generating AI notes`
  String get generating_ai_note {
    return Intl.message(
      'Generating AI notes',
      name: 'generating_ai_note',
      desc: '',
      args: [],
    );
  }

  /// `Processing Your Recording...`
  String get processing_note_recording {
    return Intl.message(
      'Processing Your Recording...',
      name: 'processing_note_recording',
      desc: '',
      args: [],
    );
  }

  /// `Processing YouTube Video...`
  String get processing_note_youtube {
    return Intl.message(
      'Processing YouTube Video...',
      name: 'processing_note_youtube',
      desc: '',
      args: [],
    );
  }

  /// `Select main language for the best transcription results`
  String get language_tip_1 {
    return Intl.message(
      'Select main language for the best transcription results',
      name: 'language_tip_1',
      desc: '',
      args: [],
    );
  }

  /// `For mixed conversation, please choose multi-language`
  String get language_tip_2 {
    return Intl.message(
      'For mixed conversation, please choose multi-language',
      name: 'language_tip_2',
      desc: '',
      args: [],
    );
  }

  /// `Calls will auto-pause recording. Return to app to resume`
  String get language_tip_3 {
    return Intl.message(
      'Calls will auto-pause recording. Return to app to resume',
      name: 'language_tip_3',
      desc: '',
      args: [],
    );
  }

  /// `Select Language`
  String get select_language {
    return Intl.message(
      'Select Language',
      name: 'select_language',
      desc: '',
      args: [],
    );
  }

  /// `Audio Length Limit`
  String get audio_length_limit {
    return Intl.message(
      'Audio Length Limit',
      name: 'audio_length_limit',
      desc: '',
      args: [],
    );
  }

  /// `Free users can transcribe and summarize audio up to 30 minutes in length`
  String get free_user_audio {
    return Intl.message(
      'Free users can transcribe and summarize audio up to 30 minutes in length',
      name: 'free_user_audio',
      desc: '',
      args: [],
    );
  }

  /// `Processing Your Audio...`
  String get processing_note_audio_file {
    return Intl.message(
      'Processing Your Audio...',
      name: 'processing_note_audio_file',
      desc: '',
      args: [],
    );
  }

  /// `minutes remaining`
  String get minutes_remaining {
    return Intl.message(
      'minutes remaining',
      name: 'minutes_remaining',
      desc: '',
      args: [],
    );
  }

  /// `Is this note clear and useful?`
  String get satisfied_quality {
    return Intl.message(
      'Is this note clear and useful?',
      name: 'satisfied_quality',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for your feedback. Your input helps us improve our product results, and we will work to make your experience better next time. Thank you very much!`
  String get dissatisfied {
    return Intl.message(
      'Thank you for your feedback. Your input helps us improve our product results, and we will work to make your experience better next time. Thank you very much!',
      name: 'dissatisfied',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for your feedback!`
  String get satisfied {
    return Intl.message(
      'Thank you for your feedback!',
      name: 'satisfied',
      desc: '',
      args: [],
    );
  }

  /// `Enter your feedback`
  String get enter_feedback {
    return Intl.message(
      'Enter your feedback',
      name: 'enter_feedback',
      desc: '',
      args: [],
    );
  }

  /// `Thank for Feedback!`
  String get thank_feedback {
    return Intl.message(
      'Thank for Feedback!',
      name: 'thank_feedback',
      desc: '',
      args: [],
    );
  }

  /// `What could we improve?`
  String get can_improve {
    return Intl.message(
      'What could we improve?',
      name: 'can_improve',
      desc: '',
      args: [],
    );
  }

  /// `Recording quality`
  String get recording_quality {
    return Intl.message(
      'Recording quality',
      name: 'recording_quality',
      desc: '',
      args: [],
    );
  }

  /// `Transcription precision`
  String get transcription_precision {
    return Intl.message(
      'Transcription precision',
      name: 'transcription_precision',
      desc: '',
      args: [],
    );
  }

  /// `Summary usefulness`
  String get summary_usefulness {
    return Intl.message(
      'Summary usefulness',
      name: 'summary_usefulness',
      desc: '',
      args: [],
    );
  }

  /// `Others`
  String get others {
    return Intl.message(
      'Others',
      name: 'others',
      desc: '',
      args: [],
    );
  }

  /// `What you need to be improved`
  String get what_improve {
    return Intl.message(
      'What you need to be improved',
      name: 'what_improve',
      desc: '',
      args: [],
    );
  }

  /// `No Speech Detected`
  String get no_speech_detected {
    return Intl.message(
      'No Speech Detected',
      name: 'no_speech_detected',
      desc: '',
      args: [],
    );
  }

  /// `Upload in progress. Keep screen open.\n Disable VPN for faster upload.`
  String get upload_in_progress {
    return Intl.message(
      'Upload in progress. Keep screen open.\n Disable VPN for faster upload.',
      name: 'upload_in_progress',
      desc: '',
      args: [],
    );
  }

  /// `Choose the main language in your audio for best results`
  String get language_tip {
    return Intl.message(
      'Choose the main language in your audio for best results',
      name: 'language_tip',
      desc: '',
      args: [],
    );
  }

  /// `Please select a language first so we can transcribe the audio accurately!`
  String get please_select_a_language {
    return Intl.message(
      'Please select a language first so we can transcribe the audio accurately!',
      name: 'please_select_a_language',
      desc: '',
      args: [],
    );
  }

  /// `Select a language to use throughout the recording process before saving.`
  String get select_a_language {
    return Intl.message(
      'Select a language to use throughout the recording process before saving.',
      name: 'select_a_language',
      desc: '',
      args: [],
    );
  }

  /// `Filter & Sort`
  String get filter_and_sort {
    return Intl.message(
      'Filter & Sort',
      name: 'filter_and_sort',
      desc: '',
      args: [],
    );
  }

  /// `Filter`
  String get filter {
    return Intl.message(
      'Filter',
      name: 'filter',
      desc: '',
      args: [],
    );
  }

  /// `All`
  String get all {
    return Intl.message(
      'All',
      name: 'all',
      desc: '',
      args: [],
    );
  }

  /// `Recording`
  String get record {
    return Intl.message(
      'Recording',
      name: 'record',
      desc: '',
      args: [],
    );
  }

  /// `YouTube`
  String get youtube {
    return Intl.message(
      'YouTube',
      name: 'youtube',
      desc: '',
      args: [],
    );
  }

  /// `Sort by`
  String get sort_by {
    return Intl.message(
      'Sort by',
      name: 'sort_by',
      desc: '',
      args: [],
    );
  }

  /// `Newest First`
  String get newest_first {
    return Intl.message(
      'Newest First',
      name: 'newest_first',
      desc: '',
      args: [],
    );
  }

  /// `Oldest First`
  String get oldest_first {
    return Intl.message(
      'Oldest First',
      name: 'oldest_first',
      desc: '',
      args: [],
    );
  }

  /// `A to Z`
  String get a_to_z {
    return Intl.message(
      'A to Z',
      name: 'a_to_z',
      desc: '',
      args: [],
    );
  }

  /// `Z to A`
  String get z_to_a {
    return Intl.message(
      'Z to A',
      name: 'z_to_a',
      desc: '',
      args: [],
    );
  }

  /// `Tap the`
  String get tap_the {
    return Intl.message(
      'Tap the',
      name: 'tap_the',
      desc: '',
      args: [],
    );
  }

  /// `or`
  String get or {
    return Intl.message(
      'or',
      name: 'or',
      desc: '',
      args: [],
    );
  }

  /// `Upload`
  String get upload {
    return Intl.message(
      'Upload',
      name: 'upload',
      desc: '',
      args: [],
    );
  }

  /// `to`
  String get to {
    return Intl.message(
      'to',
      name: 'to',
      desc: '',
      args: [],
    );
  }

  /// `Recording in progress`
  String get recording_in_progress {
    return Intl.message(
      'Recording in progress',
      name: 'recording_in_progress',
      desc: '',
      args: [],
    );
  }

  /// `Recording...`
  String get recording_in_progress_content {
    return Intl.message(
      'Recording...',
      name: 'recording_in_progress_content',
      desc: '',
      args: [],
    );
  }

  /// `Recording paused`
  String get recording_paused {
    return Intl.message(
      'Recording paused',
      name: 'recording_paused',
      desc: '',
      args: [],
    );
  }

  /// `Press to resume`
  String get recording_paused_content {
    return Intl.message(
      'Press to resume',
      name: 'recording_paused_content',
      desc: '',
      args: [],
    );
  }

  /// `Access your notes on any device`
  String get login_info_1 {
    return Intl.message(
      'Access your notes on any device',
      name: 'login_info_1',
      desc: '',
      args: [],
    );
  }

  /// `Enterprise-grade security powered by AWS`
  String get login_info_2 {
    return Intl.message(
      'Enterprise-grade security powered by AWS',
      name: 'login_info_2',
      desc: '',
      args: [],
    );
  }

  /// `Your data stays private`
  String get login_info_3 {
    return Intl.message(
      'Your data stays private',
      name: 'login_info_3',
      desc: '',
      args: [],
    );
  }

  /// `Access on Web at notexapp.com`
  String get login_info_4 {
    return Intl.message(
      'Access on Web at notexapp.com',
      name: 'login_info_4',
      desc: '',
      args: [],
    );
  }

  /// `Maximize Productivity, Everywhere!`
  String get login_title {
    return Intl.message(
      'Maximize Productivity, Everywhere!',
      name: 'login_title',
      desc: '',
      args: [],
    );
  }

  /// `Signing in...`
  String get signing_in {
    return Intl.message(
      'Signing in...',
      name: 'signing_in',
      desc: '',
      args: [],
    );
  }

  /// `Verifying your credentials`
  String get verifying_your_credentials {
    return Intl.message(
      'Verifying your credentials',
      name: 'verifying_your_credentials',
      desc: '',
      args: [],
    );
  }

  /// `Migrating your notes...`
  String get migrating_your_notes {
    return Intl.message(
      'Migrating your notes...',
      name: 'migrating_your_notes',
      desc: '',
      args: [],
    );
  }

  /// `Please wait`
  String get please_wait {
    return Intl.message(
      'Please wait',
      name: 'please_wait',
      desc: '',
      args: [],
    );
  }

  /// `Migration complete!`
  String get migration_complete {
    return Intl.message(
      'Migration complete!',
      name: 'migration_complete',
      desc: '',
      args: [],
    );
  }

  /// `Your notes are now ready.`
  String get your_note_are_ready {
    return Intl.message(
      'Your notes are now ready.',
      name: 'your_note_are_ready',
      desc: '',
      args: [],
    );
  }

  /// `Connection Fail!`
  String get connection_fail {
    return Intl.message(
      'Connection Fail!',
      name: 'connection_fail',
      desc: '',
      args: [],
    );
  }

  /// `Unsynced Notes`
  String get unsynced_notes {
    return Intl.message(
      'Unsynced Notes',
      name: 'unsynced_notes',
      desc: '',
      args: [],
    );
  }

  /// `Recording Voice Note`
  String get recording_voice_note {
    return Intl.message(
      'Recording Voice Note',
      name: 'recording_voice_note',
      desc: '',
      args: [],
    );
  }

  /// `Audio Upload`
  String get audio_upload_note {
    return Intl.message(
      'Audio Upload',
      name: 'audio_upload_note',
      desc: '',
      args: [],
    );
  }

  /// `YouTube Video`
  String get youtube_video_note {
    return Intl.message(
      'YouTube Video',
      name: 'youtube_video_note',
      desc: '',
      args: [],
    );
  }

  /// `Introducing NoteX 2.0`
  String get login_title_2 {
    return Intl.message(
      'Introducing NoteX 2.0',
      name: 'login_title_2',
      desc: '',
      args: [],
    );
  }

  /// `Welcome back 👋`
  String get hello_welcome {
    return Intl.message(
      'Welcome back 👋',
      name: 'hello_welcome',
      desc: '',
      args: [],
    );
  }

  /// `Free`
  String get free {
    return Intl.message(
      'Free',
      name: 'free',
      desc: '',
      args: [],
    );
  }

  /// `Log out?`
  String get logout_question_mark {
    return Intl.message(
      'Log out?',
      name: 'logout_question_mark',
      desc: '',
      args: [],
    );
  }

  /// `This action cannot be undone. Deleting your account will permanently remove: All your notes and recordings`
  String get delete_account_detail {
    return Intl.message(
      'This action cannot be undone. Deleting your account will permanently remove: All your notes and recordings',
      name: 'delete_account_detail',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to log out?`
  String get logout_detail {
    return Intl.message(
      'Are you sure you want to log out?',
      name: 'logout_detail',
      desc: '',
      args: [],
    );
  }

  /// `Document`
  String get document_tab {
    return Intl.message(
      'Document',
      name: 'document_tab',
      desc: '',
      args: [],
    );
  }

  /// `Supported file types: .pdf, .doc, .docx, .txt, .md`
  String get document_type {
    return Intl.message(
      'Supported file types: .pdf, .doc, .docx, .txt, .md',
      name: 'document_type',
      desc: '',
      args: [],
    );
  }

  /// `Pick a specific language in the audio for better transcribing accuracy`
  String get pick_specific_language {
    return Intl.message(
      'Pick a specific language in the audio for better transcribing accuracy',
      name: 'pick_specific_language',
      desc: '',
      args: [],
    );
  }

  /// `Start For Free`
  String get start_for_free {
    return Intl.message(
      'Start For Free',
      name: 'start_for_free',
      desc: '',
      args: [],
    );
  }

  /// `AI Notes Creation`
  String get ai_note_create {
    return Intl.message(
      'AI Notes Creation',
      name: 'ai_note_create',
      desc: '',
      args: [],
    );
  }

  /// `Smart Recording`
  String get local_recording {
    return Intl.message(
      'Smart Recording',
      name: 'local_recording',
      desc: '',
      args: [],
    );
  }

  /// `Audio to AI Notes`
  String get audio_to_ai_note {
    return Intl.message(
      'Audio to AI Notes',
      name: 'audio_to_ai_note',
      desc: '',
      args: [],
    );
  }

  /// `Document to AI Notes`
  String get document_to_ai_note {
    return Intl.message(
      'Document to AI Notes',
      name: 'document_to_ai_note',
      desc: '',
      args: [],
    );
  }

  /// `YouTube Import`
  String get youtube_import {
    return Intl.message(
      'YouTube Import',
      name: 'youtube_import',
      desc: '',
      args: [],
    );
  }

  /// `AI Study Tools`
  String get ai_study_tools {
    return Intl.message(
      'AI Study Tools',
      name: 'ai_study_tools',
      desc: '',
      args: [],
    );
  }

  /// `Nova AI`
  String get ai_chat {
    return Intl.message(
      'Nova AI',
      name: 'ai_chat',
      desc: '',
      args: [],
    );
  }

  /// `AI Study Practice`
  String get ai_study_practice {
    return Intl.message(
      'AI Study Practice',
      name: 'ai_study_practice',
      desc: '',
      args: [],
    );
  }

  /// `Share & Sync`
  String get share_sync {
    return Intl.message(
      'Share & Sync',
      name: 'share_sync',
      desc: '',
      args: [],
    );
  }

  /// `Share Notes`
  String get share_note {
    return Intl.message(
      'Share Notes',
      name: 'share_note',
      desc: '',
      args: [],
    );
  }

  /// `1 per day`
  String get one_per_day {
    return Intl.message(
      '1 per day',
      name: 'one_per_day',
      desc: '',
      args: [],
    );
  }

  /// `30 min per \n week`
  String get thirty_min_per {
    return Intl.message(
      '30 min per \n week',
      name: 'thirty_min_per',
      desc: '',
      args: [],
    );
  }

  /// `PDF Export`
  String get pdf_export {
    return Intl.message(
      'PDF Export',
      name: 'pdf_export',
      desc: '',
      args: [],
    );
  }

  /// `Pro`
  String get pro_01 {
    return Intl.message(
      'Pro',
      name: 'pro_01',
      desc: '',
      args: [],
    );
  }

  /// `Go back`
  String get go_back {
    return Intl.message(
      'Go back',
      name: 'go_back',
      desc: '',
      args: [],
    );
  }

  /// `Create New Folder`
  String get create_new_folder {
    return Intl.message(
      'Create New Folder',
      name: 'create_new_folder',
      desc: '',
      args: [],
    );
  }

  /// `Name`
  String get name {
    return Intl.message(
      'Name',
      name: 'name',
      desc: '',
      args: [],
    );
  }

  /// `Ex: Folder_name A`
  String get required {
    return Intl.message(
      'Ex: Folder_name A',
      name: 'required',
      desc: '',
      args: [],
    );
  }

  /// `The text contains only 50 characters`
  String get text_must_not_exceed_50_chars {
    return Intl.message(
      'The text contains only 50 characters',
      name: 'text_must_not_exceed_50_chars',
      desc: '',
      args: [],
    );
  }

  /// `Card`
  String get card {
    return Intl.message(
      'Card',
      name: 'card',
      desc: '',
      args: [],
    );
  }

  /// `Reset`
  String get reset {
    return Intl.message(
      'Reset',
      name: 'reset',
      desc: '',
      args: [],
    );
  }

  /// `Select the main speech language before saving this recording`
  String get tool_tip_language {
    return Intl.message(
      'Select the main speech language before saving this recording',
      name: 'tool_tip_language',
      desc: '',
      args: [],
    );
  }

  /// `Document Upload Limit`
  String get document_limit {
    return Intl.message(
      'Document Upload Limit',
      name: 'document_limit',
      desc: '',
      args: [],
    );
  }

  /// `Free users can summarize 1 Document per day.`
  String get document_limit_message {
    return Intl.message(
      'Free users can summarize 1 Document per day.',
      name: 'document_limit_message',
      desc: '',
      args: [],
    );
  }

  /// `Document upload`
  String get document_upload_note {
    return Intl.message(
      'Document upload',
      name: 'document_upload_note',
      desc: '',
      args: [],
    );
  }

  /// `Unable to read the document. We couldn't extract any text from this document. This usually happens with scanned documents or image-only PDFs.`
  String get cannot_extract_text_from_pdf {
    return Intl.message(
      'Unable to read the document. We couldn\'t extract any text from this document. This usually happens with scanned documents or image-only PDFs.',
      name: 'cannot_extract_text_from_pdf',
      desc: '',
      args: [],
    );
  }

  /// `No notes found under this filter.\nPlease reset the filter selection`
  String get no_notes_found {
    return Intl.message(
      'No notes found under this filter.\nPlease reset the filter selection',
      name: 'no_notes_found',
      desc: '',
      args: [],
    );
  }

  /// `Audio`
  String get audio {
    return Intl.message(
      'Audio',
      name: 'audio',
      desc: '',
      args: [],
    );
  }

  /// `Quiz score`
  String get quiz_score {
    return Intl.message(
      'Quiz score',
      name: 'quiz_score',
      desc: '',
      args: [],
    );
  }

  /// `We're Here to Help:`
  String get report_issue2 {
    return Intl.message(
      'We\'re Here to Help:',
      name: 'report_issue2',
      desc: '',
      args: [],
    );
  }

  /// `We understand how frustrating it can be when you're experiencing issues, especially if they involve your important notes or recordings. Our support team is ready to help resolve any problems you're facing, usually within 12 hours.`
  String get introduce_guidance2 {
    return Intl.message(
      'We understand how frustrating it can be when you\'re experiencing issues, especially if they involve your important notes or recordings. Our support team is ready to help resolve any problems you\'re facing, usually within 12 hours.',
      name: 'introduce_guidance2',
      desc: '',
      args: [],
    );
  }

  /// `To help us assist you faster:`
  String get assist_faster {
    return Intl.message(
      'To help us assist you faster:',
      name: 'assist_faster',
      desc: '',
      args: [],
    );
  }

  /// `Sign in with your Google or Apple account if you haven't already`
  String get idea1 {
    return Intl.message(
      'Sign in with your Google or Apple account if you haven\'t already',
      name: 'idea1',
      desc: '',
      args: [],
    );
  }

  /// `Provide a brief description of what happened`
  String get idea2 {
    return Intl.message(
      'Provide a brief description of what happened',
      name: 'idea2',
      desc: '',
      args: [],
    );
  }

  /// `Include relevant details (device, OS version)`
  String get idea3 {
    return Intl.message(
      'Include relevant details (device, OS version)',
      name: 'idea3',
      desc: '',
      args: [],
    );
  }

  /// `Mention when the issue started occurring`
  String get idea4 {
    return Intl.message(
      'Mention when the issue started occurring',
      name: 'idea4',
      desc: '',
      args: [],
    );
  }

  /// `Email us directly at`
  String get idea5 {
    return Intl.message(
      'Email us directly at',
      name: 'idea5',
      desc: '',
      args: [],
    );
  }

  /// `This helps us investigate and resolve your issue more effectively.`
  String get appreciate_cooperation2 {
    return Intl.message(
      'This helps us investigate and resolve your issue more effectively.',
      name: 'appreciate_cooperation2',
      desc: '',
      args: [],
    );
  }

  /// `Thank you for using and trusting NoteX AI!`
  String get appreciate_cooperation3 {
    return Intl.message(
      'Thank you for using and trusting NoteX AI!',
      name: 'appreciate_cooperation3',
      desc: '',
      args: [],
    );
  }

  /// `Processing your document...`
  String get process_your_document {
    return Intl.message(
      'Processing your document...',
      name: 'process_your_document',
      desc: '',
      args: [],
    );
  }

  /// `There was an issue processing your document. Please go to the app and try again.`
  String get body_error_document_upload {
    return Intl.message(
      'There was an issue processing your document. Please go to the app and try again.',
      name: 'body_error_document_upload',
      desc: '',
      args: [],
    );
  }

  /// `Mind Map`
  String get mind_map_iap {
    return Intl.message(
      'Mind Map',
      name: 'mind_map_iap',
      desc: '',
      args: [],
    );
  }

  /// `Flashcard Set`
  String get flash_card_iap {
    return Intl.message(
      'Flashcard Set',
      name: 'flash_card_iap',
      desc: '',
      args: [],
    );
  }

  /// `Quiz Set`
  String get quiz_iap {
    return Intl.message(
      'Quiz Set',
      name: 'quiz_iap',
      desc: '',
      args: [],
    );
  }

  /// `Loading document content...`
  String get document_webview_loading_message {
    return Intl.message(
      'Loading document content...',
      name: 'document_webview_loading_message',
      desc: '',
      args: [],
    );
  }

  /// `Fail to load document!`
  String get fail_to_load_document {
    return Intl.message(
      'Fail to load document!',
      name: 'fail_to_load_document',
      desc: '',
      args: [],
    );
  }

  /// `Import Shared Notes`
  String get import_notes {
    return Intl.message(
      'Import Shared Notes',
      name: 'import_notes',
      desc: '',
      args: [],
    );
  }

  /// `Easily import shared note links from friends`
  String get connect_friends {
    return Intl.message(
      'Easily import shared note links from friends',
      name: 'connect_friends',
      desc: '',
      args: [],
    );
  }

  /// `Coming soon`
  String get coming_soon {
    return Intl.message(
      'Coming soon',
      name: 'coming_soon',
      desc: '',
      args: [],
    );
  }

  /// `Brief service disruption. Please retry shortly. Get live status updates on Discord!`
  String get brief_service_disruption {
    return Intl.message(
      'Brief service disruption. Please retry shortly. Get live status updates on Discord!',
      name: 'brief_service_disruption',
      desc: '',
      args: [],
    );
  }

  /// `Boost comprehension`
  String get boost_comprehension2 {
    return Intl.message(
      'Boost comprehension',
      name: 'boost_comprehension2',
      desc: '',
      args: [],
    );
  }

  /// `and retention`
  String get retention {
    return Intl.message(
      'and retention',
      name: 'retention',
      desc: '',
      args: [],
    );
  }

  /// `Extracting text from document`
  String get extracting_text_from_document {
    return Intl.message(
      'Extracting text from document',
      name: 'extracting_text_from_document',
      desc: '',
      args: [],
    );
  }

  /// `AI Transcription`
  String get ai_transcription {
    return Intl.message(
      'AI Transcription',
      name: 'ai_transcription',
      desc: '',
      args: [],
    );
  }

  /// `File Import`
  String get file_import {
    return Intl.message(
      'File Import',
      name: 'file_import',
      desc: '',
      args: [],
    );
  }

  /// `Sharing & Export`
  String get sharing_export {
    return Intl.message(
      'Sharing & Export',
      name: 'sharing_export',
      desc: '',
      args: [],
    );
  }

  /// `Web Sync`
  String get web_sync {
    return Intl.message(
      'Web Sync',
      name: 'web_sync',
      desc: '',
      args: [],
    );
  }

  /// `Note Sharing`
  String get note_sharing {
    return Intl.message(
      'Note Sharing',
      name: 'note_sharing',
      desc: '',
      args: [],
    );
  }

  /// `Max AI Transcription:`
  String get max_ai {
    return Intl.message(
      'Max AI Transcription:',
      name: 'max_ai',
      desc: '',
      args: [],
    );
  }

  /// `Free: 30 minutes per file`
  String get free_30_minutes {
    return Intl.message(
      'Free: 30 minutes per file',
      name: 'free_30_minutes',
      desc: '',
      args: [],
    );
  }

  /// `Pro: 6 hours per file`
  String get pro_6_hours {
    return Intl.message(
      'Pro: 6 hours per file',
      name: 'pro_6_hours',
      desc: '',
      args: [],
    );
  }

  /// `Loved by `
  String get loved_by {
    return Intl.message(
      'Loved by ',
      name: 'loved_by',
      desc: '',
      args: [],
    );
  }

  /// ` of Users`
  String get of_user {
    return Intl.message(
      ' of Users',
      name: 'of_user',
      desc: '',
      args: [],
    );
  }

  /// `Searching all notes`
  String get searching_all_notes {
    return Intl.message(
      'Searching all notes',
      name: 'searching_all_notes',
      desc: '',
      args: [],
    );
  }

  /// `No results found for`
  String get no_results_found {
    return Intl.message(
      'No results found for',
      name: 'no_results_found',
      desc: '',
      args: [],
    );
  }

  /// `Search in Files`
  String get search_in_files {
    return Intl.message(
      'Search in Files',
      name: 'search_in_files',
      desc: '',
      args: [],
    );
  }

  /// `Suggested`
  String get suggested {
    return Intl.message(
      'Suggested',
      name: 'suggested',
      desc: '',
      args: [],
    );
  }

  /// `There are no notes in this folder.`
  String get no_notes_in_folder {
    return Intl.message(
      'There are no notes in this folder.',
      name: 'no_notes_in_folder',
      desc: '',
      args: [],
    );
  }

  /// `MindMap generated success`
  String get mind_map_gen_success {
    return Intl.message(
      'MindMap generated success',
      name: 'mind_map_gen_success',
      desc: '',
      args: [],
    );
  }

  /// `FlashCard generated success`
  String get flash_card_gen_success {
    return Intl.message(
      'FlashCard generated success',
      name: 'flash_card_gen_success',
      desc: '',
      args: [],
    );
  }

  /// `Quiz generated success`
  String get quiz_gen_success {
    return Intl.message(
      'Quiz generated success',
      name: 'quiz_gen_success',
      desc: '',
      args: [],
    );
  }

  /// `Document Note`
  String get document_note {
    return Intl.message(
      'Document Note',
      name: 'document_note',
      desc: '',
      args: [],
    );
  }

  /// `Access NoteX web`
  String get access_notex_web {
    return Intl.message(
      'Access NoteX web',
      name: 'access_notex_web',
      desc: '',
      args: [],
    );
  }

  /// `Sync notes in computer browser`
  String get sync_notes {
    return Intl.message(
      'Sync notes in computer browser',
      name: 'sync_notes',
      desc: '',
      args: [],
    );
  }

  /// `Help & Legal`
  String get help_legal {
    return Intl.message(
      'Help & Legal',
      name: 'help_legal',
      desc: '',
      args: [],
    );
  }

  /// `UPGRADE`
  String get upgrade {
    return Intl.message(
      'UPGRADE',
      name: 'upgrade',
      desc: '',
      args: [],
    );
  }

  /// `YOUR PRODUCTIVITY`
  String get your_product {
    return Intl.message(
      'YOUR PRODUCTIVITY',
      name: 'your_product',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited AI Notes from Recordings, File Uploads, YouTube Links.`
  String get content_quarter_01 {
    return Intl.message(
      'Unlimited AI Notes from Recordings, File Uploads, YouTube Links.',
      name: 'content_quarter_01',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited AI Chat, Mind Map, Flashcards, Quiz, Note Sharing.`
  String get content_quarter_02 {
    return Intl.message(
      'Unlimited AI Chat, Mind Map, Flashcards, Quiz, Note Sharing.',
      name: 'content_quarter_02',
      desc: '',
      args: [],
    );
  }

  /// `7 days free, then`
  String get seven_day_free {
    return Intl.message(
      '7 days free, then',
      name: 'seven_day_free',
      desc: '',
      args: [],
    );
  }

  /// `Merry Christmas Sale!`
  String get black_friday_sale {
    return Intl.message(
      'Merry Christmas Sale!',
      name: 'black_friday_sale',
      desc: '',
      args: [],
    );
  }

  /// `22 - 30 Nov`
  String get time_black_friday {
    return Intl.message(
      '22 - 30 Nov',
      name: 'time_black_friday',
      desc: '',
      args: [],
    );
  }

  /// `22 - 30 November`
  String get time_black_friday_2 {
    return Intl.message(
      '22 - 30 November',
      name: 'time_black_friday_2',
      desc: '',
      args: [],
    );
  }

  /// `Once-in-a-lifetime offer`
  String get once_in_a_lifetime_offer {
    return Intl.message(
      'Once-in-a-lifetime offer',
      name: 'once_in_a_lifetime_offer',
      desc: '',
      args: [],
    );
  }

  /// `Limited Time`
  String get limited_time {
    return Intl.message(
      'Limited Time',
      name: 'limited_time',
      desc: '',
      args: [],
    );
  }

  /// `NoteX Pro Lifetime`
  String get noteX_pro_lifetime {
    return Intl.message(
      'NoteX Pro Lifetime',
      name: 'noteX_pro_lifetime',
      desc: '',
      args: [],
    );
  }

  /// `one-time payment`
  String get one_time_payment {
    return Intl.message(
      'one-time payment',
      name: 'one_time_payment',
      desc: '',
      args: [],
    );
  }

  /// `Free lifetime updates & enhancements`
  String get free_updates {
    return Intl.message(
      'Free lifetime updates & enhancements',
      name: 'free_updates',
      desc: '',
      args: [],
    );
  }

  /// `Unlock Lifetime Access`
  String get unlock_lifetime_access {
    return Intl.message(
      'Unlock Lifetime Access',
      name: 'unlock_lifetime_access',
      desc: '',
      args: [],
    );
  }

  /// `Lifetime spots remaining`
  String get lifetime_spots_remaining {
    return Intl.message(
      'Lifetime spots remaining',
      name: 'lifetime_spots_remaining',
      desc: '',
      args: [],
    );
  }

  /// `Only`
  String get only {
    return Intl.message(
      'Only',
      name: 'only',
      desc: '',
      args: [],
    );
  }

  /// `Basic Plan`
  String get basic {
    return Intl.message(
      'Basic Plan',
      name: 'basic',
      desc: '',
      args: [],
    );
  }

  /// `Try Pro Free for 7 Days`
  String get try_pro_free_7_day {
    return Intl.message(
      'Try Pro Free for 7 Days',
      name: 'try_pro_free_7_day',
      desc: '',
      args: [],
    );
  }

  /// `Start my 7-Day Trial`
  String get start_my_7_day_trial {
    return Intl.message(
      'Start my 7-Day Trial',
      name: 'start_my_7_day_trial',
      desc: '',
      args: [],
    );
  }

  /// `✓ No payment now`
  String get no_payment_now {
    return Intl.message(
      '✓ No payment now',
      name: 'no_payment_now',
      desc: '',
      args: [],
    );
  }

  /// `Auto-renews after trial • Cancel anytime`
  String get auto_renew_after_trial {
    return Intl.message(
      'Auto-renews after trial • Cancel anytime',
      name: 'auto_renew_after_trial',
      desc: '',
      args: [],
    );
  }

  /// `New version available`
  String get check_update {
    return Intl.message(
      'New version available',
      name: 'check_update',
      desc: '',
      args: [],
    );
  }

  /// `A new update is available! Please update to the latest version to enjoy the best experience.`
  String get update_available {
    return Intl.message(
      'A new update is available! Please update to the latest version to enjoy the best experience.',
      name: 'update_available',
      desc: '',
      args: [],
    );
  }

  /// `Update Now!`
  String get update_now {
    return Intl.message(
      'Update Now!',
      name: 'update_now',
      desc: '',
      args: [],
    );
  }

  /// `Later`
  String get update_later {
    return Intl.message(
      'Later',
      name: 'update_later',
      desc: '',
      args: [],
    );
  }

  /// `Unable to open store`
  String get unable_to_open_store {
    return Intl.message(
      'Unable to open store',
      name: 'unable_to_open_store',
      desc: '',
      args: [],
    );
  }

  /// `Ask anything ...`
  String get ask_anything {
    return Intl.message(
      'Ask anything ...',
      name: 'ask_anything',
      desc: '',
      args: [],
    );
  }

  /// `Copy`
  String get copy {
    return Intl.message(
      'Copy',
      name: 'copy',
      desc: '',
      args: [],
    );
  }

  /// `I'm Nova AI from NoteX.`
  String get ai_learning_companion {
    return Intl.message(
      'I\'m Nova AI from NoteX.',
      name: 'ai_learning_companion',
      desc: '',
      args: [],
    );
  }

  /// `Temporary session, use "Save Chat" to keep`
  String get chat_topic_temporary_stored {
    return Intl.message(
      'Temporary session, use "Save Chat" to keep',
      name: 'chat_topic_temporary_stored',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited AI notes from YouTube & Document`
  String get unlimited_ai_notes_from_youtube_and_document {
    return Intl.message(
      'Unlimited AI notes from YouTube & Document',
      name: 'unlimited_ai_notes_from_youtube_and_document',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited AI Chat, AI Mind Map, Flashcard, Quiz`
  String get unlimited_ai_chat_ai_mind_map_flashcard_quiz {
    return Intl.message(
      'Unlimited AI Chat, AI Mind Map, Flashcard, Quiz',
      name: 'unlimited_ai_chat_ai_mind_map_flashcard_quiz',
      desc: '',
      args: [],
    );
  }

  /// `3 AI Audio Transcription per day *`
  String get ai_audio_transcription_per_day {
    return Intl.message(
      '3 AI Audio Transcription per day *',
      name: 'ai_audio_transcription_per_day',
      desc: '',
      args: [],
    );
  }

  /// `* max 60 min per file`
  String get max_60_min_per_file {
    return Intl.message(
      '* max 60 min per file',
      name: 'max_60_min_per_file',
      desc: '',
      args: [],
    );
  }

  /// `Only Today`
  String get only_today {
    return Intl.message(
      'Only Today',
      name: 'only_today',
      desc: '',
      args: [],
    );
  }

  /// `NoteX Essential Lifetime`
  String get noteX_lifetime_essential {
    return Intl.message(
      'NoteX Essential Lifetime',
      name: 'noteX_lifetime_essential',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited AI Chat, AI Mind Map, Flashcard, Quiz`
  String get unlimited_ai_chat {
    return Intl.message(
      'Unlimited AI Chat, AI Mind Map, Flashcard, Quiz',
      name: 'unlimited_ai_chat',
      desc: '',
      args: [],
    );
  }

  /// `Login Success!`
  String get login_success {
    return Intl.message(
      'Login Success!',
      name: 'login_success',
      desc: '',
      args: [],
    );
  }

  /// `Move to Folder`
  String get add_folder {
    return Intl.message(
      'Move to Folder',
      name: 'add_folder',
      desc: '',
      args: [],
    );
  }

  /// `Unlock Essential Lifetime`
  String get unlock_essential_life_time {
    return Intl.message(
      'Unlock Essential Lifetime',
      name: 'unlock_essential_life_time',
      desc: '',
      args: [],
    );
  }

  /// `Translate Note`
  String get translate_note {
    return Intl.message(
      'Translate Note',
      name: 'translate_note',
      desc: '',
      args: [],
    );
  }

  /// `Output Language`
  String get output_language {
    return Intl.message(
      'Output Language',
      name: 'output_language',
      desc: '',
      args: [],
    );
  }

  /// `Translating Note...`
  String get translating_note {
    return Intl.message(
      'Translating Note...',
      name: 'translating_note',
      desc: '',
      args: [],
    );
  }

  /// `Translation Completed`
  String get translation_completed {
    return Intl.message(
      'Translation Completed',
      name: 'translation_completed',
      desc: '',
      args: [],
    );
  }

  /// `Translation Failed`
  String get translation_failed {
    return Intl.message(
      'Translation Failed',
      name: 'translation_failed',
      desc: '',
      args: [],
    );
  }

  /// `Loading content...`
  String get loading_content {
    return Intl.message(
      'Loading content...',
      name: 'loading_content',
      desc: '',
      args: [],
    );
  }

  /// `of`
  String get of_index {
    return Intl.message(
      'of',
      name: 'of_index',
      desc: '',
      args: [],
    );
  }

  /// `The file exceeds 20MB. Please select a smaller file.`
  String get document_exceed_limit {
    return Intl.message(
      'The file exceeds 20MB. Please select a smaller file.',
      name: 'document_exceed_limit',
      desc: '',
      args: [],
    );
  }

  /// `Image (.png)`
  String get image_png {
    return Intl.message(
      'Image (.png)',
      name: 'image_png',
      desc: '',
      args: [],
    );
  }

  /// `PDF (.pdf)`
  String get pdf_pdf {
    return Intl.message(
      'PDF (.pdf)',
      name: 'pdf_pdf',
      desc: '',
      args: [],
    );
  }

  /// `SubRip (.srt)`
  String get sub_rip {
    return Intl.message(
      'SubRip (.srt)',
      name: 'sub_rip',
      desc: '',
      args: [],
    );
  }

  /// `Markdown (.md)`
  String get markdown_md {
    return Intl.message(
      'Markdown (.md)',
      name: 'markdown_md',
      desc: '',
      args: [],
    );
  }

  /// `Export Mindmap as`
  String get export_mind_map {
    return Intl.message(
      'Export Mindmap as',
      name: 'export_mind_map',
      desc: '',
      args: [],
    );
  }

  /// `lifetime deals left at this price`
  String get deals_left_at_this_price {
    return Intl.message(
      'lifetime deals left at this price',
      name: 'deals_left_at_this_price',
      desc: '',
      args: [],
    );
  }

  /// `Paste URL here`
  String get paste_url_here {
    return Intl.message(
      'Paste URL here',
      name: 'paste_url_here',
      desc: '',
      args: [],
    );
  }

  /// `Add to Note`
  String get add_to_notes {
    return Intl.message(
      'Add to Note',
      name: 'add_to_notes',
      desc: '',
      args: [],
    );
  }

  /// `Unable to extract content from web URL`
  String get unable_to_extract_web_url {
    return Intl.message(
      'Unable to extract content from web URL',
      name: 'unable_to_extract_web_url',
      desc: '',
      args: [],
    );
  }

  /// `Web Link`
  String get web_link {
    return Intl.message(
      'Web Link',
      name: 'web_link',
      desc: '',
      args: [],
    );
  }

  /// `Processing web link`
  String get processing_web_link {
    return Intl.message(
      'Processing web link',
      name: 'processing_web_link',
      desc: '',
      args: [],
    );
  }

  /// `Academic Use`
  String get student {
    return Intl.message(
      'Academic Use',
      name: 'student',
      desc: '',
      args: [],
    );
  }

  /// `Business Use`
  String get professional {
    return Intl.message(
      'Business Use',
      name: 'professional',
      desc: '',
      args: [],
    );
  }

  /// `Work Notes & Projects`
  String get work_notes_projects {
    return Intl.message(
      'Work Notes & Projects',
      name: 'work_notes_projects',
      desc: '',
      args: [],
    );
  }

  /// `How will you use NoteX?`
  String get how_will_you_use_notex {
    return Intl.message(
      'How will you use NoteX?',
      name: 'how_will_you_use_notex',
      desc: '',
      args: [],
    );
  }

  /// `Select your primary use case`
  String get select_your_primary_use_case {
    return Intl.message(
      'Select your primary use case',
      name: 'select_your_primary_use_case',
      desc: '',
      args: [],
    );
  }

  /// `Lecture Notes & Study Materials`
  String get lecture_notes_study_materials {
    return Intl.message(
      'Lecture Notes & Study Materials',
      name: 'lecture_notes_study_materials',
      desc: '',
      args: [],
    );
  }

  /// `AI Chat With Notes`
  String get ai_chat_with_notes {
    return Intl.message(
      'AI Chat With Notes',
      name: 'ai_chat_with_notes',
      desc: '',
      args: [],
    );
  }

  /// `Your personal study`
  String get your_personal_study {
    return Intl.message(
      'Your personal study',
      name: 'your_personal_study',
      desc: '',
      args: [],
    );
  }

  /// `Your personal study assistant`
  String get your_personal_study_assistant {
    return Intl.message(
      'Your personal study assistant',
      name: 'your_personal_study_assistant',
      desc: '',
      args: [],
    );
  }

  /// `Interactive Flashcards & Quiz`
  String get interactive_flashcards_quiz {
    return Intl.message(
      'Interactive Flashcards & Quiz',
      name: 'interactive_flashcards_quiz',
      desc: '',
      args: [],
    );
  }

  /// `Learn faster through active recall`
  String get learn_faster_through_active_recall {
    return Intl.message(
      'Learn faster through active recall',
      name: 'learn_faster_through_active_recall',
      desc: '',
      args: [],
    );
  }

  /// `Multiply knowledge with friends`
  String get multiply_knowledge_with_friends {
    return Intl.message(
      'Multiply knowledge with friends',
      name: 'multiply_knowledge_with_friends',
      desc: '',
      args: [],
    );
  }

  /// `Transform meetings into actionable intelligence`
  String get transform_meetings_into_actionable_intelligence {
    return Intl.message(
      'Transform meetings into actionable intelligence',
      name: 'transform_meetings_into_actionable_intelligence',
      desc: '',
      args: [],
    );
  }

  /// `AI Chat Assistant`
  String get ai_chat_assistant {
    return Intl.message(
      'AI Chat Assistant',
      name: 'ai_chat_assistant',
      desc: '',
      args: [],
    );
  }

  /// `Instant answers from your meeting data`
  String get instant_answers_from_your_meeting_data {
    return Intl.message(
      'Instant answers from your meeting data',
      name: 'instant_answers_from_your_meeting_data',
      desc: '',
      args: [],
    );
  }

  /// `Organize & Assign Action Items`
  String get organize_assign_action_items {
    return Intl.message(
      'Organize & Assign Action Items',
      name: 'organize_assign_action_items',
      desc: '',
      args: [],
    );
  }

  /// `Get more done, stay on track`
  String get get_more_done_stay_on_track {
    return Intl.message(
      'Get more done, stay on track',
      name: 'get_more_done_stay_on_track',
      desc: '',
      args: [],
    );
  }

  /// `Visualize strategies and uncover opportunities`
  String get visualize_strategies_uncover_opportunities {
    return Intl.message(
      'Visualize strategies and uncover opportunities',
      name: 'visualize_strategies_uncover_opportunities',
      desc: '',
      args: [],
    );
  }

  /// `assistant`
  String get assistant {
    return Intl.message(
      'assistant',
      name: 'assistant',
      desc: '',
      args: [],
    );
  }

  /// `Learn faster through`
  String get learn_faster_through {
    return Intl.message(
      'Learn faster through',
      name: 'learn_faster_through',
      desc: '',
      args: [],
    );
  }

  /// `active recall`
  String get active_recall {
    return Intl.message(
      'active recall',
      name: 'active_recall',
      desc: '',
      args: [],
    );
  }

  /// `Transform meetings into`
  String get transform_meetings {
    return Intl.message(
      'Transform meetings into',
      name: 'transform_meetings',
      desc: '',
      args: [],
    );
  }

  /// `actionable intelligence`
  String get actionable_intelligence {
    return Intl.message(
      'actionable intelligence',
      name: 'actionable_intelligence',
      desc: '',
      args: [],
    );
  }

  /// `Instant answers from your meeting`
  String get instant_answers_meeting {
    return Intl.message(
      'Instant answers from your meeting',
      name: 'instant_answers_meeting',
      desc: '',
      args: [],
    );
  }

  /// `meeting data`
  String get meeting_data {
    return Intl.message(
      'meeting data',
      name: 'meeting_data',
      desc: '',
      args: [],
    );
  }

  /// `Organize & Assign Action Items`
  String get organize_assign_items {
    return Intl.message(
      'Organize & Assign Action Items',
      name: 'organize_assign_items',
      desc: '',
      args: [],
    );
  }

  /// `Get more done, stay`
  String get get_more_done {
    return Intl.message(
      'Get more done, stay',
      name: 'get_more_done',
      desc: '',
      args: [],
    );
  }

  /// `on track`
  String get on_track {
    return Intl.message(
      'on track',
      name: 'on_track',
      desc: '',
      args: [],
    );
  }

  /// `Visualize strategies and`
  String get visualize_strategies {
    return Intl.message(
      'Visualize strategies and',
      name: 'visualize_strategies',
      desc: '',
      args: [],
    );
  }

  /// `uncover opportunities`
  String get uncover_opportunities {
    return Intl.message(
      'uncover opportunities',
      name: 'uncover_opportunities',
      desc: '',
      args: [],
    );
  }

  /// `Visualize strategies and uncover opportunities`
  String get visualize_strategies_opportunities {
    return Intl.message(
      'Visualize strategies and uncover opportunities',
      name: 'visualize_strategies_opportunities',
      desc: '',
      args: [],
    );
  }

  /// `Instant answers from your`
  String get instant_answers_from_your {
    return Intl.message(
      'Instant answers from your',
      name: 'instant_answers_from_your',
      desc: '',
      args: [],
    );
  }

  /// `data`
  String get data {
    return Intl.message(
      'data',
      name: 'data',
      desc: '',
      args: [],
    );
  }

  /// `Visualize strategies and uncover`
  String get visualize_strategies_uncover {
    return Intl.message(
      'Visualize strategies and uncover',
      name: 'visualize_strategies_uncover',
      desc: '',
      args: [],
    );
  }

  /// `opportunities`
  String get opportunities {
    return Intl.message(
      'opportunities',
      name: 'opportunities',
      desc: '',
      args: [],
    );
  }

  /// `AI Notes Creation`
  String get ai_note_creation {
    return Intl.message(
      'AI Notes Creation',
      name: 'ai_note_creation',
      desc: '',
      args: [],
    );
  }

  /// `AI Insight`
  String get ai_insight {
    return Intl.message(
      'AI Insight',
      name: 'ai_insight',
      desc: '',
      args: [],
    );
  }

  /// `AI Learning`
  String get ai_learning {
    return Intl.message(
      'AI Learning',
      name: 'ai_learning',
      desc: '',
      args: [],
    );
  }

  /// `AI Workflow`
  String get ai_workflow {
    return Intl.message(
      'AI Workflow',
      name: 'ai_workflow',
      desc: '',
      args: [],
    );
  }

  /// `Website Import`
  String get website_import {
    return Intl.message(
      'Website Import',
      name: 'website_import',
      desc: '',
      args: [],
    );
  }

  /// `Action Items`
  String get action_items {
    return Intl.message(
      'Action Items',
      name: 'action_items',
      desc: '',
      args: [],
    );
  }

  /// `Note Reminders`
  String get note_reminders {
    return Intl.message(
      'Note Reminders',
      name: 'note_reminders',
      desc: '',
      args: [],
    );
  }

  /// `Mind Blowing!`
  String get rating_sub_context_1 {
    return Intl.message(
      'Mind Blowing!',
      name: 'rating_sub_context_1',
      desc: '',
      args: [],
    );
  }

  /// `Meeting Pro`
  String get rating_sub_context_2 {
    return Intl.message(
      'Meeting Pro',
      name: 'rating_sub_context_2',
      desc: '',
      args: [],
    );
  }

  /// `Time Saver`
  String get rating_sub_context_3 {
    return Intl.message(
      'Time Saver',
      name: 'rating_sub_context_3',
      desc: '',
      args: [],
    );
  }

  /// `AI Note Taking It Is Best`
  String get rating_sub_context_4 {
    return Intl.message(
      'AI Note Taking It Is Best',
      name: 'rating_sub_context_4',
      desc: '',
      args: [],
    );
  }

  /// `Like This App So Much`
  String get rating_sub_context_5 {
    return Intl.message(
      'Like This App So Much',
      name: 'rating_sub_context_5',
      desc: '',
      args: [],
    );
  }

  /// `AI Note Taking It Is Best`
  String get rating_sub_context_6 {
    return Intl.message(
      'AI Note Taking It Is Best',
      name: 'rating_sub_context_6',
      desc: '',
      args: [],
    );
  }

  /// `Best Note Taking I've Used`
  String get rating_sub_context_7 {
    return Intl.message(
      'Best Note Taking I\'ve Used',
      name: 'rating_sub_context_7',
      desc: '',
      args: [],
    );
  }

  /// `So Far The Best`
  String get rating_sub_context_8 {
    return Intl.message(
      'So Far The Best',
      name: 'rating_sub_context_8',
      desc: '',
      args: [],
    );
  }

  /// `Supports YouTube, Web, TikTok, Instagram, Facebook & more`
  String get support_youtube_and_more {
    return Intl.message(
      'Supports YouTube, Web, TikTok, Instagram, Facebook & more',
      name: 'support_youtube_and_more',
      desc: '',
      args: [],
    );
  }

  /// `This app is truly amazing, and it's only getting better. Thanks to the developers for their hard work and dedication. I recommend it over any other AI note-taking app on the App Store.`
  String get rating_cmt1 {
    return Intl.message(
      'This app is truly amazing, and it\'s only getting better. Thanks to the developers for their hard work and dedication. I recommend it over any other AI note-taking app on the App Store.',
      name: 'rating_cmt1',
      desc: '',
      args: [],
    );
  }

  /// `Absolutely love this app! The perfect companion for all my meetings.`
  String get rating_cmt2 {
    return Intl.message(
      'Absolutely love this app! The perfect companion for all my meetings.',
      name: 'rating_cmt2',
      desc: '',
      args: [],
    );
  }

  /// `Cut my study time in half. More time for coffee breaks!`
  String get rating_cmt3 {
    return Intl.message(
      'Cut my study time in half. More time for coffee breaks!',
      name: 'rating_cmt3',
      desc: '',
      args: [],
    );
  }

  /// `This app is absolutely mind-blowing! Not only does it nail transcription, but it takes things to another level with incredible summaries, outlines, and action items. Pure genius!`
  String get rating_cmt4 {
    return Intl.message(
      'This app is absolutely mind-blowing! Not only does it nail transcription, but it takes things to another level with incredible summaries, outlines, and action items. Pure genius!',
      name: 'rating_cmt4',
      desc: '',
      args: [],
    );
  }

  /// `Wonderful, powerful! Everything you want and more.Everything you want and more.`
  String get rating_cmt5 {
    return Intl.message(
      'Wonderful, powerful! Everything you want and more.Everything you want and more.',
      name: 'rating_cmt5',
      desc: '',
      args: [],
    );
  }

  /// `I tried this for the first time today by entering a YouTube presentation. Within seconds it gave me a complete transcription, a well-designed brain chart, and a series of flash cards on the subject. This was far beyond anything I had anticipated. It is efficient, sophisticated, and wonderfully useful. This is the app I will be using daily for notes and learning.`
  String get rating_cmt6 {
    return Intl.message(
      'I tried this for the first time today by entering a YouTube presentation. Within seconds it gave me a complete transcription, a well-designed brain chart, and a series of flash cards on the subject. This was far beyond anything I had anticipated. It is efficient, sophisticated, and wonderfully useful. This is the app I will be using daily for notes and learning.',
      name: 'rating_cmt6',
      desc: '',
      args: [],
    );
  }

  /// `So far this is the best note app I've found. It has lots of useful features.`
  String get rating_cmt7 {
    return Intl.message(
      'So far this is the best note app I\'ve found. It has lots of useful features.',
      name: 'rating_cmt7',
      desc: '',
      args: [],
    );
  }

  /// `Captures every detail from marathon biology lectures. Summary feature is a lifesaver during exam prep.`
  String get rating_cmt8 {
    return Intl.message(
      'Captures every detail from marathon biology lectures. Summary feature is a lifesaver during exam prep.',
      name: 'rating_cmt8',
      desc: '',
      args: [],
    );
  }

  /// `Processing content...`
  String get processing_content {
    return Intl.message(
      'Processing content...',
      name: 'processing_content',
      desc: '',
      args: [],
    );
  }

  /// `Transcript Language`
  String get transcript_language {
    return Intl.message(
      'Transcript Language',
      name: 'transcript_language',
      desc: '',
      args: [],
    );
  }

  /// `Select YouTube transcript language. This language will be used to generate your AI notes`
  String get youtube_transcript_language_guidance {
    return Intl.message(
      'Select YouTube transcript language. This language will be used to generate your AI notes',
      name: 'youtube_transcript_language_guidance',
      desc: '',
      args: [],
    );
  }

  /// `Multi-language`
  String get multi_language {
    return Intl.message(
      'Multi-language',
      name: 'multi_language',
      desc: '',
      args: [],
    );
  }

  /// `Auto`
  String get auto {
    return Intl.message(
      'Auto',
      name: 'auto',
      desc: '',
      args: [],
    );
  }

  /// `Referral code not found.`
  String get referral_not_found {
    return Intl.message(
      'Referral code not found.',
      name: 'referral_not_found',
      desc: '',
      args: [],
    );
  }

  /// `You cannot use your own referral code.`
  String get referral_self_use {
    return Intl.message(
      'You cannot use your own referral code.',
      name: 'referral_self_use',
      desc: '',
      args: [],
    );
  }

  /// `User information not found.`
  String get user_not_found {
    return Intl.message(
      'User information not found.',
      name: 'user_not_found',
      desc: '',
      args: [],
    );
  }

  /// `Referral code has already been used.`
  String get referral_already_used {
    return Intl.message(
      'Referral code has already been used.',
      name: 'referral_already_used',
      desc: '',
      args: [],
    );
  }

  /// `Referral code has expired after 24 hours.`
  String get referral_time_expired {
    return Intl.message(
      'Referral code has expired after 24 hours.',
      name: 'referral_time_expired',
      desc: '',
      args: [],
    );
  }

  /// `An unknown server error occurred.`
  String get server_err {
    return Intl.message(
      'An unknown server error occurred.',
      name: 'server_err',
      desc: '',
      args: [],
    );
  }

  /// `Referral validation error.`
  String get referral_validation_err {
    return Intl.message(
      'Referral validation error.',
      name: 'referral_validation_err',
      desc: '',
      args: [],
    );
  }

  /// `Enter Referral Code`
  String get enter_referral_code {
    return Intl.message(
      'Enter Referral Code',
      name: 'enter_referral_code',
      desc: '',
      args: [],
    );
  }

  /// `Referral`
  String get referral {
    return Intl.message(
      'Referral',
      name: 'referral',
      desc: '',
      args: [],
    );
  }

  /// `Referral code`
  String get referral_code {
    return Intl.message(
      'Referral code',
      name: 'referral_code',
      desc: '',
      args: [],
    );
  }

  /// `Credit`
  String get credit {
    return Intl.message(
      'Credit',
      name: 'credit',
      desc: '',
      args: [],
    );
  }

  /// `Credits`
  String get credits {
    return Intl.message(
      'Credits',
      name: 'credits',
      desc: '',
      args: [],
    );
  }

  /// `Pro Lite`
  String get proLite {
    return Intl.message(
      'Pro Lite',
      name: 'proLite',
      desc: '',
      args: [],
    );
  }

  /// `Unlock Together`
  String get unlock_together {
    return Intl.message(
      'Unlock Together',
      name: 'unlock_together',
      desc: '',
      args: [],
    );
  }

  /// `Double the Benefits!`
  String get double_the_benefits {
    return Intl.message(
      'Double the Benefits!',
      name: 'double_the_benefits',
      desc: '',
      args: [],
    );
  }

  /// `Invite friends – both get`
  String get invite_friends {
    return Intl.message(
      'Invite friends – both get',
      name: 'invite_friends',
      desc: '',
      args: [],
    );
  }

  /// `credits & premium!`
  String get credits_premium_features {
    return Intl.message(
      'credits & premium!',
      name: 'credits_premium_features',
      desc: '',
      args: [],
    );
  }

  /// `Chance to win NoteX Lifetime Pro! 🚀`
  String get you_will_get_one_entry_to_win_noteX {
    return Intl.message(
      'Chance to win NoteX Lifetime Pro! 🚀',
      name: 'you_will_get_one_entry_to_win_noteX',
      desc: '',
      args: [],
    );
  }

  /// `Level up together ✨`
  String get lifetime_pro_access_level_up_together {
    return Intl.message(
      'Level up together ✨',
      name: 'lifetime_pro_access_level_up_together',
      desc: '',
      args: [],
    );
  }

  /// `Refer Now`
  String get refer_now {
    return Intl.message(
      'Refer Now',
      name: 'refer_now',
      desc: '',
      args: [],
    );
  }

  /// `Follow these steps to get rewarded`
  String get follow_steps_to_get_rewarded {
    return Intl.message(
      'Follow these steps to get rewarded',
      name: 'follow_steps_to_get_rewarded',
      desc: '',
      args: [],
    );
  }

  /// `Copy your referral code.`
  String get copy_your_referral_code {
    return Intl.message(
      'Copy your referral code.',
      name: 'copy_your_referral_code',
      desc: '',
      args: [],
    );
  }

  /// `Share the code with friends via email, social media, or messages.`
  String get share_code_friends {
    return Intl.message(
      'Share the code with friends via email, social media, or messages.',
      name: 'share_code_friends',
      desc: '',
      args: [],
    );
  }

  /// `Both you and your friends will receive usage credits.`
  String get both_you_friends_receive_usage_credits {
    return Intl.message(
      'Both you and your friends will receive usage credits.',
      name: 'both_you_friends_receive_usage_credits',
      desc: '',
      args: [],
    );
  }

  /// `Your Referrals`
  String get your_referrals {
    return Intl.message(
      'Your Referrals',
      name: 'your_referrals',
      desc: '',
      args: [],
    );
  }

  /// `Share your referral code to start earning credits!`
  String get share_referral_code_start_earning_credits {
    return Intl.message(
      'Share your referral code to start earning credits!',
      name: 'share_referral_code_start_earning_credits',
      desc: '',
      args: [],
    );
  }

  /// `Redeem`
  String get redeem_credits {
    return Intl.message(
      'Redeem',
      name: 'redeem_credits',
      desc: '',
      args: [],
    );
  }

  /// `Refer & Rewards`
  String get refer_rewards {
    return Intl.message(
      'Refer & Rewards',
      name: 'refer_rewards',
      desc: '',
      args: [],
    );
  }

  /// `Warning: This AI note-taking app may cause excessive productivity! 🚀 Use my code and we'll both get extra usage. Code: `
  String get warning_this_ai_note_taking_app_may_cause_excessive_productivity {
    return Intl.message(
      'Warning: This AI note-taking app may cause excessive productivity! 🚀 Use my code and we\'ll both get extra usage. Code: ',
      name: 'warning_this_ai_note_taking_app_may_cause_excessive_productivity',
      desc: '',
      args: [],
    );
  }

  /// `Join NoteX AI and let's level up together!`
  String get join_noteX_ai_lets_level_up_together {
    return Intl.message(
      'Join NoteX AI and let\'s level up together!',
      name: 'join_noteX_ai_lets_level_up_together',
      desc: '',
      args: [],
    );
  }

  /// `You have received`
  String get you_have_received {
    return Intl.message(
      'You have received',
      name: 'you_have_received',
      desc: '',
      args: [],
    );
  }

  /// `You'll get one entry to win NoteX Lifetime Pro Access! 3 lucky winners chosen on the 30th every month 🎁`
  String get you_have_received2 {
    return Intl.message(
      'You\'ll get one entry to win NoteX Lifetime Pro Access! 3 lucky winners chosen on the 30th every month 🎁',
      name: 'you_have_received2',
      desc: '',
      args: [],
    );
  }

  /// `Credits can be used to create notes and access the features within them. If your subscription expires, you can use credits to continue performing actions.`
  String
      get credits_can_be_used_to_create_notes_and_access_the_features_within_them {
    return Intl.message(
      'Credits can be used to create notes and access the features within them. If your subscription expires, you can use credits to continue performing actions.',
      name:
          'credits_can_be_used_to_create_notes_and_access_the_features_within_them',
      desc: '',
      args: [],
    );
  }

  /// `Essential`
  String get essential {
    return Intl.message(
      'Essential',
      name: 'essential',
      desc: '',
      args: [],
    );
  }

  /// `Bonus credits for new referred friends only`
  String get bonus_credits_for_new_referred_friends_only {
    return Intl.message(
      'Bonus credits for new referred friends only',
      name: 'bonus_credits_for_new_referred_friends_only',
      desc: '',
      args: [],
    );
  }

  /// `Invalid code. Try again.`
  String get invalid_code {
    return Intl.message(
      'Invalid code. Try again.',
      name: 'invalid_code',
      desc: '',
      args: [],
    );
  }

  /// `Available Credits`
  String get available_credits {
    return Intl.message(
      'Available Credits',
      name: 'available_credits',
      desc: '',
      args: [],
    );
  }

  /// `Credits Earned`
  String get credits_earned {
    return Intl.message(
      'Credits Earned',
      name: 'credits_earned',
      desc: '',
      args: [],
    );
  }

  /// `Each referral earns`
  String get each_referral_earns {
    return Intl.message(
      'Each referral earns',
      name: 'each_referral_earns',
      desc: '',
      args: [],
    );
  }

  /// `Credits Used`
  String get credits_used {
    return Intl.message(
      'Credits Used',
      name: 'credits_used',
      desc: '',
      args: [],
    );
  }

  /// `Each AI note generation uses 1 credit`
  String get each_ai_note_generation_uses_1_credit {
    return Intl.message(
      'Each AI note generation uses 1 credit',
      name: 'each_ai_note_generation_uses_1_credit',
      desc: '',
      args: [],
    );
  }

  /// `credits`
  String get tolower_credits {
    return Intl.message(
      'credits',
      name: 'tolower_credits',
      desc: '',
      args: [],
    );
  }

  /// `Login unsuccessful. Try again or use another login method.`
  String get login_unsuccessful {
    return Intl.message(
      'Login unsuccessful. Try again or use another login method.',
      name: 'login_unsuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Oops! We couldn't start your purchase. Please try again.`
  String get purchase_init_fail {
    return Intl.message(
      'Oops! We couldn\'t start your purchase. Please try again.',
      name: 'purchase_init_fail',
      desc: '',
      args: [],
    );
  }

  /// `There was an issue processing your web link. Please go to the app and try again.`
  String get body_error_note_web {
    return Intl.message(
      'There was an issue processing your web link. Please go to the app and try again.',
      name: 'body_error_note_web',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade to Pro Experience`
  String get update_pro {
    return Intl.message(
      'Upgrade to Pro Experience',
      name: 'update_pro',
      desc: '',
      args: [],
    );
  }

  /// `Experience unlimited AI notes, priority service, and premium features`
  String get unlimited_everything {
    return Intl.message(
      'Experience unlimited AI notes, priority service, and premium features',
      name: 'unlimited_everything',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited AI note generation from all sources (YouTube, Documents, Recording, Audio)`
  String get unlimited_ai_note {
    return Intl.message(
      'Unlimited AI note generation from all sources (YouTube, Documents, Recording, Audio)',
      name: 'unlimited_ai_note',
      desc: '',
      args: [],
    );
  }

  /// `5 AI Short Videos generation per day (beta)`
  String get ai_short_3 {
    return Intl.message(
      '5 AI Short Videos generation per day (beta)',
      name: 'ai_short_3',
      desc: '',
      args: [],
    );
  }

  /// `Latest AI models`
  String get latest_ai_models {
    return Intl.message(
      'Latest AI models',
      name: 'latest_ai_models',
      desc: '',
      args: [],
    );
  }

  /// `Priority processing`
  String get priority_processing {
    return Intl.message(
      'Priority processing',
      name: 'priority_processing',
      desc: '',
      args: [],
    );
  }

  /// `PRO Access`
  String get you_are_pro {
    return Intl.message(
      'PRO Access',
      name: 'you_are_pro',
      desc: '',
      args: [],
    );
  }

  /// `Essential Lifetime Access`
  String get essential_lifetime_access {
    return Intl.message(
      'Essential Lifetime Access',
      name: 'essential_lifetime_access',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited AI notes from YouTube & Documents`
  String get ai_notes_10 {
    return Intl.message(
      'Unlimited AI notes from YouTube & Documents',
      name: 'ai_notes_10',
      desc: '',
      args: [],
    );
  }

  /// `3 AI notes per day from Recording & Audio upload (up to 60min per file)`
  String get ai_notes_3 {
    return Intl.message(
      '3 AI notes per day from Recording & Audio upload (up to 60min per file)',
      name: 'ai_notes_3',
      desc: '',
      args: [],
    );
  }

  /// `3 AI Short Videos generation per day`
  String get ai_short_1 {
    return Intl.message(
      '3 AI Short Videos generation per day',
      name: 'ai_short_1',
      desc: '',
      args: [],
    );
  }

  /// `Basic AI features`
  String get basic_features {
    return Intl.message(
      'Basic AI features',
      name: 'basic_features',
      desc: '',
      args: [],
    );
  }

  /// `Limited notes per day`
  String get limited_notes {
    return Intl.message(
      'Limited notes per day',
      name: 'limited_notes',
      desc: '',
      args: [],
    );
  }

  /// `Try premium features to see the difference`
  String get premium_features {
    return Intl.message(
      'Try premium features to see the difference',
      name: 'premium_features',
      desc: '',
      args: [],
    );
  }

  /// `Notifications`
  String get notifications {
    return Intl.message(
      'Notifications',
      name: 'notifications',
      desc: '',
      args: [],
    );
  }

  /// `Notify when notes are ready`
  String get notifications_note_ready {
    return Intl.message(
      'Notify when notes are ready',
      name: 'notifications_note_ready',
      desc: '',
      args: [],
    );
  }

  /// `Your notes are successfully created`
  String get notifications_note_created {
    return Intl.message(
      'Your notes are successfully created',
      name: 'notifications_note_created',
      desc: '',
      args: [],
    );
  }

  /// `Set up weekly audio recording times`
  String get reminders_record_audio {
    return Intl.message(
      'Set up weekly audio recording times',
      name: 'reminders_record_audio',
      desc: '',
      args: [],
    );
  }

  /// `Recording Schedule`
  String get recording_schedule {
    return Intl.message(
      'Recording Schedule',
      name: 'recording_schedule',
      desc: '',
      args: [],
    );
  }

  /// `Delete reminder?`
  String get delete_reminder {
    return Intl.message(
      'Delete reminder?',
      name: 'delete_reminder',
      desc: '',
      args: [],
    );
  }

  /// `Are you certain you want to remove this reminder?`
  String get content_delete_reminder {
    return Intl.message(
      'Are you certain you want to remove this reminder?',
      name: 'content_delete_reminder',
      desc: '',
      args: [],
    );
  }

  /// `Edit Reminder`
  String get edit_reminder {
    return Intl.message(
      'Edit Reminder',
      name: 'edit_reminder',
      desc: '',
      args: [],
    );
  }

  /// `Title`
  String get title {
    return Intl.message(
      'Title',
      name: 'title',
      desc: '',
      args: [],
    );
  }

  /// `Time`
  String get time {
    return Intl.message(
      'Time',
      name: 'time',
      desc: '',
      args: [],
    );
  }

  /// `Create Reminder`
  String get create_reminder {
    return Intl.message(
      'Create Reminder',
      name: 'create_reminder',
      desc: '',
      args: [],
    );
  }

  /// `Change`
  String get change {
    return Intl.message(
      'Change',
      name: 'change',
      desc: '',
      args: [],
    );
  }

  /// `Allow`
  String get allow {
    return Intl.message(
      'Allow',
      name: 'allow',
      desc: '',
      args: [],
    );
  }

  /// `Don't Allow`
  String get donotallow {
    return Intl.message(
      'Don\'t Allow',
      name: 'donotallow',
      desc: '',
      args: [],
    );
  }

  /// `Notifications may include alerts, sounds, and icon badges. These can be configured in Settings.`
  String get noti_req_description {
    return Intl.message(
      'Notifications may include alerts, sounds, and icon badges. These can be configured in Settings.',
      name: 'noti_req_description',
      desc: '',
      args: [],
    );
  }

  /// `‘NoteX’ Would Like to Send You Notifications`
  String get noti_req_title {
    return Intl.message(
      '‘NoteX’ Would Like to Send You Notifications',
      name: 'noti_req_title',
      desc: '',
      args: [],
    );
  }

  /// `It's time to record`
  String get noti_default_title {
    return Intl.message(
      'It\'s time to record',
      name: 'noti_default_title',
      desc: '',
      args: [],
    );
  }

  /// `Get ready and start recording! 🚀`
  String get noti_default_description {
    return Intl.message(
      'Get ready and start recording! 🚀',
      name: 'noti_default_description',
      desc: '',
      args: [],
    );
  }

  /// `Create Shorts`
  String get create_shorts {
    return Intl.message(
      'Create Shorts',
      name: 'create_shorts',
      desc: '',
      args: [],
    );
  }

  /// `Duration`
  String get duration {
    return Intl.message(
      'Duration',
      name: 'duration',
      desc: '',
      args: [],
    );
  }

  /// `Voice`
  String get voice {
    return Intl.message(
      'Voice',
      name: 'voice',
      desc: '',
      args: [],
    );
  }

  /// `Video`
  String get video {
    return Intl.message(
      'Video',
      name: 'video',
      desc: '',
      args: [],
    );
  }

  /// `Generate Audio`
  String get generate_audio {
    return Intl.message(
      'Generate Audio',
      name: 'generate_audio',
      desc: '',
      args: [],
    );
  }

  /// `Generate Video`
  String get generate_video {
    return Intl.message(
      'Generate Video',
      name: 'generate_video',
      desc: '',
      args: [],
    );
  }

  /// `Video Captions`
  String get video_captions {
    return Intl.message(
      'Video Captions',
      name: 'video_captions',
      desc: '',
      args: [],
    );
  }

  /// `Background Style`
  String get background_style {
    return Intl.message(
      'Background Style',
      name: 'background_style',
      desc: '',
      args: [],
    );
  }

  /// `Word (.docx)`
  String get word_docx {
    return Intl.message(
      'Word (.docx)',
      name: 'word_docx',
      desc: '',
      args: [],
    );
  }

  /// `Image (.jpeg)`
  String get image_jpeg {
    return Intl.message(
      'Image (.jpeg)',
      name: 'image_jpeg',
      desc: '',
      args: [],
    );
  }

  /// `Click 'Create Short' to turn your note into engaging shorts `
  String get click_create_short {
    return Intl.message(
      'Click \'Create Short\' to turn your note into engaging shorts ',
      name: 'click_create_short',
      desc: '',
      args: [],
    );
  }

  /// `Shorts`
  String get shorts {
    return Intl.message(
      'Shorts',
      name: 'shorts',
      desc: '',
      args: [],
    );
  }

  /// `No voice available`
  String get no_voice_available {
    return Intl.message(
      'No voice available',
      name: 'no_voice_available',
      desc: '',
      args: [],
    );
  }

  /// `Daily Shorts Limit Reached (Beta - Early Access)`
  String get daily_shorts_limit_reached {
    return Intl.message(
      'Daily Shorts Limit Reached (Beta - Early Access)',
      name: 'daily_shorts_limit_reached',
      desc: '',
      args: [],
    );
  }

  /// `You've used up all Shorts generations for today. This beta feature has daily limits to ensure stable service. \nCome back tomorrow to create more AI short videos!`
  String get daily_shorts_limit_reached_detail {
    return Intl.message(
      'You\'ve used up all Shorts generations for today. This beta feature has daily limits to ensure stable service. \nCome back tomorrow to create more AI short videos!',
      name: 'daily_shorts_limit_reached_detail',
      desc: '',
      args: [],
    );
  }

  /// `*Video is temporary - Save before closing`
  String get video_is_temporary {
    return Intl.message(
      '*Video is temporary - Save before closing',
      name: 'video_is_temporary',
      desc: '',
      args: [],
    );
  }

  /// `Unlock the most powerful AI \nnote-taking assistant`
  String get unlock_the_most_powerful_ai_note_taking_assistant {
    return Intl.message(
      'Unlock the most powerful AI \nnote-taking assistant',
      name: 'unlock_the_most_powerful_ai_note_taking_assistant',
      desc: '',
      args: [],
    );
  }

  /// `Unlock the most powerful AI note-taking assistant`
  String get unlock_the_most_ipad {
    return Intl.message(
      'Unlock the most powerful AI note-taking assistant',
      name: 'unlock_the_most_ipad',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited Audio, YouTube , Document & Website to AI Notes`
  String get unlimited_audio_youtube_website_to_ai_notes {
    return Intl.message(
      'Unlimited Audio, YouTube , Document & Website to AI Notes',
      name: 'unlimited_audio_youtube_website_to_ai_notes',
      desc: '',
      args: [],
    );
  }

  /// `Nova AI Assistant & Mind Mapping`
  String get nova_ai_assistant_mind_mapping {
    return Intl.message(
      'Nova AI Assistant & Mind Mapping',
      name: 'nova_ai_assistant_mind_mapping',
      desc: '',
      args: [],
    );
  }

  /// `Generate Shorts & Study Guides`
  String get generate_shorts_study_guides {
    return Intl.message(
      'Generate Shorts & Study Guides',
      name: 'generate_shorts_study_guides',
      desc: '',
      args: [],
    );
  }

  /// `Export to PDF & Share Notes`
  String get export_to_pdf_share_notes {
    return Intl.message(
      'Export to PDF & Share Notes',
      name: 'export_to_pdf_share_notes',
      desc: '',
      args: [],
    );
  }

  /// `Beta`
  String get beta {
    return Intl.message(
      'Beta',
      name: 'beta',
      desc: '',
      args: [],
    );
  }

  /// `*Audio is temporary - Save before closing`
  String get audio_is_temporary {
    return Intl.message(
      '*Audio is temporary - Save before closing',
      name: 'audio_is_temporary',
      desc: '',
      args: [],
    );
  }

  /// `Crafting your story...`
  String get generate_shorts_step_1 {
    return Intl.message(
      'Crafting your story...',
      name: 'generate_shorts_step_1',
      desc: '',
      args: [],
    );
  }

  /// `Adding perfect voice...`
  String get generate_shorts_step_2 {
    return Intl.message(
      'Adding perfect voice...',
      name: 'generate_shorts_step_2',
      desc: '',
      args: [],
    );
  }

  /// `Making it look amazing! This video will be worth sharing #NoteXAI`
  String get generate_shorts_step_3 {
    return Intl.message(
      'Making it look amazing! This video will be worth sharing #NoteXAI',
      name: 'generate_shorts_step_3',
      desc: '',
      args: [],
    );
  }

  /// `Share`
  String get share_only {
    return Intl.message(
      'Share',
      name: 'share_only',
      desc: '',
      args: [],
    );
  }

  /// `Donwload success`
  String get download_sucess {
    return Intl.message(
      'Donwload success',
      name: 'download_sucess',
      desc: '',
      args: [],
    );
  }

  /// `Preparing video...`
  String get preparing_video {
    return Intl.message(
      'Preparing video...',
      name: 'preparing_video',
      desc: '',
      args: [],
    );
  }

  /// `Create Short`
  String get create_short {
    return Intl.message(
      'Create Short',
      name: 'create_short',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load video`
  String get fail_to_load_video {
    return Intl.message(
      'Failed to load video',
      name: 'fail_to_load_video',
      desc: '',
      args: [],
    );
  }

  /// `Unlimited YouTube & Document AI Notes`
  String get unlimited_youtube_document_ai_notes {
    return Intl.message(
      'Unlimited YouTube & Document AI Notes',
      name: 'unlimited_youtube_document_ai_notes',
      desc: '',
      args: [],
    );
  }

  /// `3 Audio & Recording AI Notes Daily*`
  String get audio_recording_ai_notes_daily {
    return Intl.message(
      '3 Audio & Recording AI Notes Daily*',
      name: 'audio_recording_ai_notes_daily',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade to Pro Tier at a Special Price`
  String get upgrade_to_pro_tier_at_a_special_price {
    return Intl.message(
      'Upgrade to Pro Tier at a Special Price',
      name: 'upgrade_to_pro_tier_at_a_special_price',
      desc: '',
      args: [],
    );
  }

  /// `Join the Discord`
  String get join_discord {
    return Intl.message(
      'Join the Discord',
      name: 'join_discord',
      desc: '',
      args: [],
    );
  }

  /// `Restore success`
  String get restore_success_title {
    return Intl.message(
      'Restore success',
      name: 'restore_success_title',
      desc: '',
      args: [],
    );
  }

  /// `No items available for restoration`
  String get restore_fail_title {
    return Intl.message(
      'No items available for restoration',
      name: 'restore_fail_title',
      desc: '',
      args: [],
    );
  }

  /// `For assistance, <NAME_EMAIL>`
  String get restore_fail_message {
    return Intl.message(
      'For assistance, <NAME_EMAIL>',
      name: 'restore_fail_message',
      desc: '',
      args: [],
    );
  }

  /// `Web`
  String get web {
    return Intl.message(
      'Web',
      name: 'web',
      desc: '',
      args: [],
    );
  }

  /// `Add Text`
  String get text {
    return Intl.message(
      'Add Text',
      name: 'text',
      desc: '',
      args: [],
    );
  }

  /// `Type or paste any text here. AI will transform it into a clear, structured summary with key highlights.`
  String get type_or_paste_any_text_here {
    return Intl.message(
      'Type or paste any text here. AI will transform it into a clear, structured summary with key highlights.',
      name: 'type_or_paste_any_text_here',
      desc: '',
      args: [],
    );
  }

  /// `characters`
  String get characters {
    return Intl.message(
      'characters',
      name: 'characters',
      desc: '',
      args: [],
    );
  }

  /// `Buy Once. Unlock Max Productivity Forever.`
  String get buy_one_forever {
    return Intl.message(
      'Buy Once. Unlock Max Productivity Forever.',
      name: 'buy_one_forever',
      desc: '',
      args: [],
    );
  }

  /// `Essential Lifetime`
  String get essential_lifetime {
    return Intl.message(
      'Essential Lifetime',
      name: 'essential_lifetime',
      desc: '',
      args: [],
    );
  }

  /// `PRO Lifetime`
  String get pro_lifetime {
    return Intl.message(
      'PRO Lifetime',
      name: 'pro_lifetime',
      desc: '',
      args: [],
    );
  }

  /// `Smart starter pack`
  String get smart_start {
    return Intl.message(
      'Smart starter pack',
      name: 'smart_start',
      desc: '',
      args: [],
    );
  }

  /// `Unlock unlimited AI experience`
  String get unlock_unlimited_ai {
    return Intl.message(
      'Unlock unlimited AI experience',
      name: 'unlock_unlimited_ai',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade Plan`
  String get upgrade_plan {
    return Intl.message(
      'Upgrade Plan',
      name: 'upgrade_plan',
      desc: '',
      args: [],
    );
  }

  /// `Future features could have usage allowances`
  String get future_features {
    return Intl.message(
      'Future features could have usage allowances',
      name: 'future_features',
      desc: '',
      args: [],
    );
  }

  /// `AI Notes from Audio`
  String get ai_note_from {
    return Intl.message(
      'AI Notes from Audio',
      name: 'ai_note_from',
      desc: '',
      args: [],
    );
  }

  /// `3 daily`
  String get daily_3 {
    return Intl.message(
      '3 daily',
      name: 'daily_3',
      desc: '',
      args: [],
    );
  }

  /// `5 daily`
  String get daily_5 {
    return Intl.message(
      '5 daily',
      name: 'daily_5',
      desc: '',
      args: [],
    );
  }

  /// `10 daily`
  String get daily_10 {
    return Intl.message(
      '10 daily',
      name: 'daily_10',
      desc: '',
      args: [],
    );
  }

  /// `AI Short Videos`
  String get ai_short_video {
    return Intl.message(
      'AI Short Videos',
      name: 'ai_short_video',
      desc: '',
      args: [],
    );
  }

  /// `Nova AI Chat`
  String get nova_ai_chat {
    return Intl.message(
      'Nova AI Chat',
      name: 'nova_ai_chat',
      desc: '',
      args: [],
    );
  }

  /// `Mind Map, \nStudy Guides`
  String get mind_map_study {
    return Intl.message(
      'Mind Map, \nStudy Guides',
      name: 'mind_map_study',
      desc: '',
      args: [],
    );
  }

  /// `Early Access to \nFuture Features`
  String get early_access {
    return Intl.message(
      'Early Access to \nFuture Features',
      name: 'early_access',
      desc: '',
      args: [],
    );
  }

  /// `AI Notes from \nYouTube, Web, Docs`
  String get ai_notes_from {
    return Intl.message(
      'AI Notes from \nYouTube, Web, Docs',
      name: 'ai_notes_from',
      desc: '',
      args: [],
    );
  }

  /// `Current Plan`
  String get current_plan {
    return Intl.message(
      'Current Plan',
      name: 'current_plan',
      desc: '',
      args: [],
    );
  }

  /// `Limited Offer`
  String get limited_offer {
    return Intl.message(
      'Limited Offer',
      name: 'limited_offer',
      desc: '',
      args: [],
    );
  }

  /// `Unlock PRO Lifetime`
  String get unlock_pro_lifetime {
    return Intl.message(
      'Unlock PRO Lifetime',
      name: 'unlock_pro_lifetime',
      desc: '',
      args: [],
    );
  }

  /// `Essential: 60 minutes per file`
  String get minute_60_per_file {
    return Intl.message(
      'Essential: 60 minutes per file',
      name: 'minute_60_per_file',
      desc: '',
      args: [],
    );
  }

  /// `Or`
  String get or_upper {
    return Intl.message(
      'Or',
      name: 'or_upper',
      desc: '',
      args: [],
    );
  }

  /// `Continue with Email`
  String get continue_with_email {
    return Intl.message(
      'Continue with Email',
      name: 'continue_with_email',
      desc: '',
      args: [],
    );
  }

  /// `Check your inbox`
  String get email_sent {
    return Intl.message(
      'Check your inbox',
      name: 'email_sent',
      desc: '',
      args: [],
    );
  }

  /// `We've sent you a magic link to sign in.\nClick the link in your email to continue.`
  String get email_sent_success {
    return Intl.message(
      'We\'ve sent you a magic link to sign in.\nClick the link in your email to continue.',
      name: 'email_sent_success',
      desc: '',
      args: [],
    );
  }

  /// `Enter your email address...`
  String get enter_email {
    return Intl.message(
      'Enter your email address...',
      name: 'enter_email',
      desc: '',
      args: [],
    );
  }

  /// `Please enter a valid email address`
  String get enter_valid_email {
    return Intl.message(
      'Please enter a valid email address',
      name: 'enter_valid_email',
      desc: '',
      args: [],
    );
  }

  /// `The link you used is invalid, may have expired, or has already been used. Please request a new link and try again.`
  String get link_invalid {
    return Intl.message(
      'The link you used is invalid, may have expired, or has already been used. Please request a new link and try again.',
      name: 'link_invalid',
      desc: '',
      args: [],
    );
  }

  /// `The email link has expired.`
  String get link_expired {
    return Intl.message(
      'The email link has expired.',
      name: 'link_expired',
      desc: '',
      args: [],
    );
  }

  /// `The email address is not valid.`
  String get email_invalid {
    return Intl.message(
      'The email address is not valid.',
      name: 'email_invalid',
      desc: '',
      args: [],
    );
  }

  /// `The user corresponding to this email has been disabled.`
  String get user_disabled {
    return Intl.message(
      'The user corresponding to this email has been disabled.',
      name: 'user_disabled',
      desc: '',
      args: [],
    );
  }

  /// `Network error. Please check your internet connection and try again.`
  String get network_error {
    return Intl.message(
      'Network error. Please check your internet connection and try again.',
      name: 'network_error',
      desc: '',
      args: [],
    );
  }

  /// `Login failed.`
  String get login_failed {
    return Intl.message(
      'Login failed.',
      name: 'login_failed',
      desc: '',
      args: [],
    );
  }

  /// `Referral Credits`
  String get referral_credits {
    return Intl.message(
      'Referral Credits',
      name: 'referral_credits',
      desc: '',
      args: [],
    );
  }

  /// `Make sure your phone are connected to the internet`
  String get unable_to_connect_to_server {
    return Intl.message(
      'Make sure your phone are connected to the internet',
      name: 'unable_to_connect_to_server',
      desc: '',
      args: [],
    );
  }

  /// `Connect to Internet`
  String get error_logging_in {
    return Intl.message(
      'Connect to Internet',
      name: 'error_logging_in',
      desc: '',
      args: [],
    );
  }

  /// `Timestamped transcript has been successfully edited`
  String get edit_transcript_json_success {
    return Intl.message(
      'Timestamped transcript has been successfully edited',
      name: 'edit_transcript_json_success',
      desc: '',
      args: [],
    );
  }

  /// `Timestamped transcript edit fail. Please try again.`
  String get edit_transcript_json_fail {
    return Intl.message(
      'Timestamped transcript edit fail. Please try again.',
      name: 'edit_transcript_json_fail',
      desc: '',
      args: [],
    );
  }

  /// `Transcript line cannot be empty`
  String get transcript_line_cannot_be_empty {
    return Intl.message(
      'Transcript line cannot be empty',
      name: 'transcript_line_cannot_be_empty',
      desc: '',
      args: [],
    );
  }

  /// `Click on the transcript item to edit`
  String get transcript_line_tool_tip {
    return Intl.message(
      'Click on the transcript item to edit',
      name: 'transcript_line_tool_tip',
      desc: '',
      args: [],
    );
  }

  /// `Error`
  String get error {
    return Intl.message(
      'Error',
      name: 'error',
      desc: '',
      args: [],
    );
  }

  /// `Export Transcript`
  String get export_transcript {
    return Intl.message(
      'Export Transcript',
      name: 'export_transcript',
      desc: '',
      args: [],
    );
  }

  /// `Export Quiz`
  String get export_quiz {
    return Intl.message(
      'Export Quiz',
      name: 'export_quiz',
      desc: '',
      args: [],
    );
  }

  /// `Export Flashcard`
  String get export_flashcard {
    return Intl.message(
      'Export Flashcard',
      name: 'export_flashcard',
      desc: '',
      args: [],
    );
  }

  /// `No quiz/flashcards generated. Tap to create now!`
  String get no_generated {
    return Intl.message(
      'No quiz/flashcards generated. Tap to create now!',
      name: 'no_generated',
      desc: '',
      args: [],
    );
  }

  /// `Choose your NoteX experience`
  String get choose_your_note_experience {
    return Intl.message(
      'Choose your NoteX experience',
      name: 'choose_your_note_experience',
      desc: '',
      args: [],
    );
  }

  /// `Choose your NoteX`
  String get choose_your_note {
    return Intl.message(
      'Choose your NoteX',
      name: 'choose_your_note',
      desc: '',
      args: [],
    );
  }

  /// `experience`
  String get experience {
    return Intl.message(
      'experience',
      name: 'experience',
      desc: '',
      args: [],
    );
  }

  /// `Smart Notes, Big Ideas`
  String get smart_note_big_ideas {
    return Intl.message(
      'Smart Notes, Big Ideas',
      name: 'smart_note_big_ideas',
      desc: '',
      args: [],
    );
  }

  /// `Let NoteX AI turn information`
  String get let_note_ai {
    return Intl.message(
      'Let NoteX AI turn information',
      name: 'let_note_ai',
      desc: '',
      args: [],
    );
  }

  /// `chaos into clarity`
  String get chaos_into_clarity {
    return Intl.message(
      'chaos into clarity',
      name: 'chaos_into_clarity',
      desc: '',
      args: [],
    );
  }

  /// `Focus on What Matters`
  String get focus_on {
    return Intl.message(
      'Focus on What Matters',
      name: 'focus_on',
      desc: '',
      args: [],
    );
  }

  /// `Let AI handle the details`
  String get let_ai_handle {
    return Intl.message(
      'Let AI handle the details',
      name: 'let_ai_handle',
      desc: '',
      args: [],
    );
  }

  /// `Let's create your\nfirst AI note`
  String get welcome_title {
    return Intl.message(
      'Let\'s create your\nfirst AI note',
      name: 'welcome_title',
      desc: '',
      args: [],
    );
  }

  /// `Start Speaking`
  String get start_speaking {
    return Intl.message(
      'Start Speaking',
      name: 'start_speaking',
      desc: '',
      args: [],
    );
  }

  /// `Tap to record your thoughts`
  String get tap_to_record {
    return Intl.message(
      'Tap to record your thoughts',
      name: 'tap_to_record',
      desc: '',
      args: [],
    );
  }

  /// `Or quickly import from`
  String get quick_import {
    return Intl.message(
      'Or quickly import from',
      name: 'quick_import',
      desc: '',
      args: [],
    );
  }

  /// `SPECIAL\nOFFER`
  String get special_offer {
    return Intl.message(
      'SPECIAL\nOFFER',
      name: 'special_offer',
      desc: '',
      args: [],
    );
  }

  /// `This action will discard all changes, and they cannot be undone.`
  String get content_discard_changes_note {
    return Intl.message(
      'This action will discard all changes, and they cannot be undone.',
      name: 'content_discard_changes_note',
      desc: '',
      args: [],
    );
  }

  /// `Find and Replace`
  String get find_and_replace {
    return Intl.message(
      'Find and Replace',
      name: 'find_and_replace',
      desc: '',
      args: [],
    );
  }

  /// `Replace`
  String get replace {
    return Intl.message(
      'Replace',
      name: 'replace',
      desc: '',
      args: [],
    );
  }

  /// `Replace All`
  String get replace_all {
    return Intl.message(
      'Replace All',
      name: 'replace_all',
      desc: '',
      args: [],
    );
  }

  /// `Search`
  String get search {
    return Intl.message(
      'Search',
      name: 'search',
      desc: '',
      args: [],
    );
  }

  /// `Hi`
  String get hi {
    return Intl.message(
      'Hi',
      name: 'hi',
      desc: '',
      args: [],
    );
  }

  /// `Processing your text...`
  String get process_your_text {
    return Intl.message(
      'Processing your text...',
      name: 'process_your_text',
      desc: '',
      args: [],
    );
  }

  /// `Good Morning!`
  String get good_morning {
    return Intl.message(
      'Good Morning!',
      name: 'good_morning',
      desc: '',
      args: [],
    );
  }

  /// `Good Afternoon!`
  String get good_afternoon {
    return Intl.message(
      'Good Afternoon!',
      name: 'good_afternoon',
      desc: '',
      args: [],
    );
  }

  /// `Good Evening!`
  String get good_evening {
    return Intl.message(
      'Good Evening!',
      name: 'good_evening',
      desc: '',
      args: [],
    );
  }

  /// `Link`
  String get link {
    return Intl.message(
      'Link',
      name: 'link',
      desc: '',
      args: [],
    );
  }

  /// `Doc`
  String get doc {
    return Intl.message(
      'Doc',
      name: 'doc',
      desc: '',
      args: [],
    );
  }

  /// `Capture today's brilliance`
  String get morning_content {
    return Intl.message(
      'Capture today\'s brilliance',
      name: 'morning_content',
      desc: '',
      args: [],
    );
  }

  /// `Small notes, big impact`
  String get afternoon_content {
    return Intl.message(
      'Small notes, big impact',
      name: 'afternoon_content',
      desc: '',
      args: [],
    );
  }

  /// `Reflect, capture, grow`
  String get evening_content {
    return Intl.message(
      'Reflect, capture, grow',
      name: 'evening_content',
      desc: '',
      args: [],
    );
  }

  /// `ACHIEVE MORE`
  String get achieve_more {
    return Intl.message(
      'ACHIEVE MORE',
      name: 'achieve_more',
      desc: '',
      args: [],
    );
  }

  /// `PRO ACCESS LIFETIME`
  String get pro_access_life_time {
    return Intl.message(
      'PRO ACCESS LIFETIME',
      name: 'pro_access_life_time',
      desc: '',
      args: [],
    );
  }

  /// `UNLOCK TOGETHER`
  String get unlock_toge {
    return Intl.message(
      'UNLOCK TOGETHER',
      name: 'unlock_toge',
      desc: '',
      args: [],
    );
  }

  /// `UPGRADE PRO`
  String get upgrade_pro_2 {
    return Intl.message(
      'UPGRADE PRO',
      name: 'upgrade_pro_2',
      desc: '',
      args: [],
    );
  }

  /// `REFERRAL`
  String get referral_02 {
    return Intl.message(
      'REFERRAL',
      name: 'referral_02',
      desc: '',
      args: [],
    );
  }

  /// `LIMITED TIME`
  String get limited_time_02 {
    return Intl.message(
      'LIMITED TIME',
      name: 'limited_time_02',
      desc: '',
      args: [],
    );
  }

  /// `Add password`
  String get add_password {
    return Intl.message(
      'Add password',
      name: 'add_password',
      desc: '',
      args: [],
    );
  }

  /// `Anyone with the link can view`
  String get anyone_with_link {
    return Intl.message(
      'Anyone with the link can view',
      name: 'anyone_with_link',
      desc: '',
      args: [],
    );
  }

  /// `Private`
  String get private {
    return Intl.message(
      'Private',
      name: 'private',
      desc: '',
      args: [],
    );
  }

  /// `Public`
  String get public {
    return Intl.message(
      'Public',
      name: 'public',
      desc: '',
      args: [],
    );
  }

  /// `Add a password to the public link`
  String get add_password_to_public {
    return Intl.message(
      'Add a password to the public link',
      name: 'add_password_to_public',
      desc: '',
      args: [],
    );
  }

  /// `Only you can view this note`
  String get only_you_can_view_this_note {
    return Intl.message(
      'Only you can view this note',
      name: 'only_you_can_view_this_note',
      desc: '',
      args: [],
    );
  }

  /// `Share with link:`
  String get share_with_link {
    return Intl.message(
      'Share with link:',
      name: 'share_with_link',
      desc: '',
      args: [],
    );
  }

  /// `button below or choose\na specific content input type to get started`
  String get button_below {
    return Intl.message(
      'button below or choose\na specific content input type to get started',
      name: 'button_below',
      desc: '',
      args: [],
    );
  }

  /// `Tap the Record`
  String get tap_the_record {
    return Intl.message(
      'Tap the Record',
      name: 'tap_the_record',
      desc: '',
      args: [],
    );
  }

  /// `Advanced`
  String get advanced {
    return Intl.message(
      'Advanced',
      name: 'advanced',
      desc: '',
      args: [],
    );
  }

  /// `Summary Style`
  String get summary_style {
    return Intl.message(
      'Summary Style',
      name: 'summary_style',
      desc: '',
      args: [],
    );
  }

  /// `Writing Style`
  String get writing_style {
    return Intl.message(
      'Writing Style',
      name: 'writing_style',
      desc: '',
      args: [],
    );
  }

  /// `Balanced`
  String get balanced {
    return Intl.message(
      'Balanced',
      name: 'balanced',
      desc: '',
      args: [],
    );
  }

  /// `Neutral`
  String get neutral {
    return Intl.message(
      'Neutral',
      name: 'neutral',
      desc: '',
      args: [],
    );
  }

  /// `Additional Instructions (opt.)`
  String get additional_ins {
    return Intl.message(
      'Additional Instructions (opt.)',
      name: 'additional_ins',
      desc: '',
      args: [],
    );
  }

  /// `for unlimited experiences.`
  String get for_unlimited_experiences {
    return Intl.message(
      'for unlimited experiences.',
      name: 'for_unlimited_experiences',
      desc: '',
      args: [],
    );
  }

  /// `Unlock All Features`
  String get unlock_all_features {
    return Intl.message(
      'Unlock All Features',
      name: 'unlock_all_features',
      desc: '',
      args: [],
    );
  }

  /// `Summary Language`
  String get summary_language {
    return Intl.message(
      'Summary Language',
      name: 'summary_language',
      desc: '',
      args: [],
    );
  }

  /// `Add specific focus areas or requirements...`
  String get add_focus {
    return Intl.message(
      'Add specific focus areas or requirements...',
      name: 'add_focus',
      desc: '',
      args: [],
    );
  }

  /// `Short`
  String get short {
    return Intl.message(
      'Short',
      name: 'short',
      desc: '',
      args: [],
    );
  }

  /// `Comprehensive`
  String get comprehensive {
    return Intl.message(
      'Comprehensive',
      name: 'comprehensive',
      desc: '',
      args: [],
    );
  }

  /// `Friendly`
  String get friendly {
    return Intl.message(
      'Friendly',
      name: 'friendly',
      desc: '',
      args: [],
    );
  }

  /// `Professional`
  String get professional_style {
    return Intl.message(
      'Professional',
      name: 'professional_style',
      desc: '',
      args: [],
    );
  }

  /// `Key points only`
  String get short_description {
    return Intl.message(
      'Key points only',
      name: 'short_description',
      desc: '',
      args: [],
    );
  }

  /// `Main ideas with context `
  String get balanced_description {
    return Intl.message(
      'Main ideas with context ',
      name: 'balanced_description',
      desc: '',
      args: [],
    );
  }

  /// `Detailed coverage with supporting points`
  String get comprehensive_description {
    return Intl.message(
      'Detailed coverage with supporting points',
      name: 'comprehensive_description',
      desc: '',
      args: [],
    );
  }

  /// `Conversational with emoji`
  String get friendly_description {
    return Intl.message(
      'Conversational with emoji',
      name: 'friendly_description',
      desc: '',
      args: [],
    );
  }

  /// `Straightforward, factual presentation`
  String get neutral_description {
    return Intl.message(
      'Straightforward, factual presentation',
      name: 'neutral_description',
      desc: '',
      args: [],
    );
  }

  /// `Formal language suitable for work context`
  String get professional_description {
    return Intl.message(
      'Formal language suitable for work context',
      name: 'professional_description',
      desc: '',
      args: [],
    );
  }

  /// `Supported file types: .mp3, .wav, .ogg, .m4a`
  String get support_audio {
    return Intl.message(
      'Supported file types: .mp3, .wav, .ogg, .m4a',
      name: 'support_audio',
      desc: '',
      args: [],
    );
  }

  /// `This is the language you will see in the summary output`
  String get this_is_the_language {
    return Intl.message(
      'This is the language you will see in the summary output',
      name: 'this_is_the_language',
      desc: '',
      args: [],
    );
  }

  /// `Let's create your first AI note!`
  String get lets_create_your_first_ai_note {
    return Intl.message(
      'Let\'s create your first AI note!',
      name: 'lets_create_your_first_ai_note',
      desc: '',
      args: [],
    );
  }

  /// `NoteX empty`
  String get notex_empty {
    return Intl.message(
      'NoteX empty',
      name: 'notex_empty',
      desc: '',
      args: [],
    );
  }

  /// `Please select a summary language. This is the language you will see in the summary output`
  String get please_select_a_youtube_language {
    return Intl.message(
      'Please select a summary language. This is the language you will see in the summary output',
      name: 'please_select_a_youtube_language',
      desc: '',
      args: [],
    );
  }

  /// `About Us`
  String get about_us {
    return Intl.message(
      'About Us',
      name: 'about_us',
      desc: '',
      args: [],
    );
  }

  /// `Lifetime Plan`
  String get lifetime {
    return Intl.message(
      'Lifetime Plan',
      name: 'lifetime',
      desc: '',
      args: [],
    );
  }

  /// `Plan`
  String get plan {
    return Intl.message(
      'Plan',
      name: 'plan',
      desc: '',
      args: [],
    );
  }

  /// `Clear mind, clear path`
  String get morning_content_2 {
    return Intl.message(
      'Clear mind, clear path',
      name: 'morning_content_2',
      desc: '',
      args: [],
    );
  }

  /// `Today's notes shape tomorrow`
  String get morning_content_3 {
    return Intl.message(
      'Today\'s notes shape tomorrow',
      name: 'morning_content_3',
      desc: '',
      args: [],
    );
  }

  /// `First thought, best thought`
  String get morning_content_4 {
    return Intl.message(
      'First thought, best thought',
      name: 'morning_content_4',
      desc: '',
      args: [],
    );
  }

  /// `Begin with clarity`
  String get morning_content_5 {
    return Intl.message(
      'Begin with clarity',
      name: 'morning_content_5',
      desc: '',
      args: [],
    );
  }

  /// `Ideas worth keeping`
  String get morning_content_6 {
    return Intl.message(
      'Ideas worth keeping',
      name: 'morning_content_6',
      desc: '',
      args: [],
    );
  }

  /// `Thoughts captured, mind free`
  String get afternoon_content_1 {
    return Intl.message(
      'Thoughts captured, mind free',
      name: 'afternoon_content_1',
      desc: '',
      args: [],
    );
  }

  /// `Order amid the chaos`
  String get afternoon_content_3 {
    return Intl.message(
      'Order amid the chaos',
      name: 'afternoon_content_3',
      desc: '',
      args: [],
    );
  }

  /// `Your ideas, organized`
  String get afternoon_content_4 {
    return Intl.message(
      'Your ideas, organized',
      name: 'afternoon_content_4',
      desc: '',
      args: [],
    );
  }

  /// `Clarity in progress`
  String get afternoon_content_5 {
    return Intl.message(
      'Clarity in progress',
      name: 'afternoon_content_5',
      desc: '',
      args: [],
    );
  }

  /// `Keep what matters`
  String get afternoon_content_6 {
    return Intl.message(
      'Keep what matters',
      name: 'afternoon_content_6',
      desc: '',
      args: [],
    );
  }

  /// `Today's insights preserved`
  String get evening_content_2 {
    return Intl.message(
      'Today\'s insights preserved',
      name: 'evening_content_2',
      desc: '',
      args: [],
    );
  }

  /// `Tomorrow begins with today's notes`
  String get evening_content_3 {
    return Intl.message(
      'Tomorrow begins with today\'s notes',
      name: 'evening_content_3',
      desc: '',
      args: [],
    );
  }

  /// `Thoughts organized, mind at ease`
  String get evening_content_4 {
    return Intl.message(
      'Thoughts organized, mind at ease',
      name: 'evening_content_4',
      desc: '',
      args: [],
    );
  }

  /// `Save now, thank yourself later`
  String get evening_content_5 {
    return Intl.message(
      'Save now, thank yourself later',
      name: 'evening_content_5',
      desc: '',
      args: [],
    );
  }

  /// `Progress preserved`
  String get evening_content_6 {
    return Intl.message(
      'Progress preserved',
      name: 'evening_content_6',
      desc: '',
      args: [],
    );
  }

  /// `notes`
  String get notes {
    return Intl.message(
      'notes',
      name: 'notes',
      desc: '',
      args: [],
    );
  }

  /// `Number of Quizzes`
  String get number_of_quiz {
    return Intl.message(
      'Number of Quizzes',
      name: 'number_of_quiz',
      desc: '',
      args: [],
    );
  }

  /// `Create Flashcards`
  String get number_of_flash {
    return Intl.message(
      'Create Flashcards',
      name: 'number_of_flash',
      desc: '',
      args: [],
    );
  }

  /// `You have a maximum of 30 questions.`
  String get max_30 {
    return Intl.message(
      'You have a maximum of 30 questions.',
      name: 'max_30',
      desc: '',
      args: [],
    );
  }

  /// `free messages`
  String get free_messages {
    return Intl.message(
      'free messages',
      name: 'free_messages',
      desc: '',
      args: [],
    );
  }

  /// `Update to PRO`
  String get update_to_pro {
    return Intl.message(
      'Update to PRO',
      name: 'update_to_pro',
      desc: '',
      args: [],
    );
  }

  /// `Quantity`
  String get quantity {
    return Intl.message(
      'Quantity',
      name: 'quantity',
      desc: '',
      args: [],
    );
  }

  /// `Easily import shared note links from friends`
  String get easily_import_shared_note_link {
    return Intl.message(
      'Easily import shared note links from friends',
      name: 'easily_import_shared_note_link',
      desc: '',
      args: [],
    );
  }

  /// `Import Note Links`
  String get import_note_links {
    return Intl.message(
      'Import Note Links',
      name: 'import_note_links',
      desc: '',
      args: [],
    );
  }

  /// `Sync from Watch`
  String get sync_from_watch {
    return Intl.message(
      'Sync from Watch',
      name: 'sync_from_watch',
      desc: '',
      args: [],
    );
  }

  /// `Delete Recording`
  String get delete_recording {
    return Intl.message(
      'Delete Recording',
      name: 'delete_recording',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete`
  String get delete_recording_confirmation {
    return Intl.message(
      'Are you sure you want to delete',
      name: 'delete_recording_confirmation',
      desc: '',
      args: [],
    );
  }

  /// `Failed to delete recording`
  String get failed_to_delete_recording {
    return Intl.message(
      'Failed to delete recording',
      name: 'failed_to_delete_recording',
      desc: '',
      args: [],
    );
  }

  /// `No recordings`
  String get no_recordings {
    return Intl.message(
      'No recordings',
      name: 'no_recordings',
      desc: '',
      args: [],
    );
  }

  /// `Recordings from your Apple Watch will appear here`
  String get watch_sync_empty_message {
    return Intl.message(
      'Recordings from your Apple Watch will appear here',
      name: 'watch_sync_empty_message',
      desc: '',
      args: [],
    );
  }

  /// `Note is not ready for export. Please wait for processing to complete.`
  String get note_not_ready {
    return Intl.message(
      'Note is not ready for export. Please wait for processing to complete.',
      name: 'note_not_ready',
      desc: '',
      args: [],
    );
  }

  /// `Export failed. Please try again later.`
  String get export_failed {
    return Intl.message(
      'Export failed. Please try again later.',
      name: 'export_failed',
      desc: '',
      args: [],
    );
  }

  /// `Connection timeout. Please check your internet connection and try again.`
  String get connection_timeout {
    return Intl.message(
      'Connection timeout. Please check your internet connection and try again.',
      name: 'connection_timeout',
      desc: '',
      args: [],
    );
  }

  /// `HTTP request failed. Please try again later.`
  String get http_failed {
    return Intl.message(
      'HTTP request failed. Please try again later.',
      name: 'http_failed',
      desc: '',
      args: [],
    );
  }

  /// `No export URL provided.`
  String get no_url_provided {
    return Intl.message(
      'No export URL provided.',
      name: 'no_url_provided',
      desc: '',
      args: [],
    );
  }

  /// `Delete Note`
  String get delete_note_item {
    return Intl.message(
      'Delete Note',
      name: 'delete_note_item',
      desc: '',
      args: [],
    );
  }

  /// `Get Offer Now`
  String get get_offer_now {
    return Intl.message(
      'Get Offer Now',
      name: 'get_offer_now',
      desc: '',
      args: [],
    );
  }

  /// `7-day free trial to access all features, then just `
  String get day_free_trial_access_all_features {
    return Intl.message(
      '7-day free trial to access all features, then just ',
      name: 'day_free_trial_access_all_features',
      desc: '',
      args: [],
    );
  }

  /// `Days`
  String get days {
    return Intl.message(
      'Days',
      name: 'days',
      desc: '',
      args: [],
    );
  }

  /// `Early supporters exclusive offer`
  String get early_supporters_exclusive_offer {
    return Intl.message(
      'Early supporters exclusive offer',
      name: 'early_supporters_exclusive_offer',
      desc: '',
      args: [],
    );
  }

  /// `Hours`
  String get hours {
    return Intl.message(
      'Hours',
      name: 'hours',
      desc: '',
      args: [],
    );
  }

  /// `Minutes`
  String get minutes {
    return Intl.message(
      'Minutes',
      name: 'minutes',
      desc: '',
      args: [],
    );
  }

  /// `Offer expires `
  String get offer_expires {
    return Intl.message(
      'Offer expires ',
      name: 'offer_expires',
      desc: '',
      args: [],
    );
  }

  /// `Open Now`
  String get open_now {
    return Intl.message(
      'Open Now',
      name: 'open_now',
      desc: '',
      args: [],
    );
  }

  /// ` per year`
  String get per_year {
    return Intl.message(
      ' per year',
      name: 'per_year',
      desc: '',
      args: [],
    );
  }

  /// `Redeem 7 days for 0`
  String get redeem_7_days_for_0 {
    return Intl.message(
      'Redeem 7 days for 0',
      name: 'redeem_7_days_for_0',
      desc: '',
      args: [],
    );
  }

  /// `Seconds`
  String get seconds {
    return Intl.message(
      'Seconds',
      name: 'seconds',
      desc: '',
      args: [],
    );
  }

  /// `Special Gift`
  String get special_gift {
    return Intl.message(
      'Special Gift',
      name: 'special_gift',
      desc: '',
      args: [],
    );
  }

  /// `You are given a special gift today 🎁`
  String get you_are_given_a_special_gift_today {
    return Intl.message(
      'You are given a special gift today 🎁',
      name: 'you_are_given_a_special_gift_today',
      desc: '',
      args: [],
    );
  }

  /// `lifetime`
  String get lifetime_setting {
    return Intl.message(
      'lifetime',
      name: 'lifetime_setting',
      desc: '',
      args: [],
    );
  }

  /// `GET NOW`
  String get get_now {
    return Intl.message(
      'GET NOW',
      name: 'get_now',
      desc: '',
      args: [],
    );
  }

  /// `Flashcard Sets`
  String get flashcard_sets {
    return Intl.message(
      'Flashcard Sets',
      name: 'flashcard_sets',
      desc: '',
      args: [],
    );
  }

  /// `Maximum 3 Flashcard Sets`
  String get max_3_flashcard_sets {
    return Intl.message(
      'Maximum 3 Flashcard Sets',
      name: 'max_3_flashcard_sets',
      desc: '',
      args: [],
    );
  }

  /// `Enter new name`
  String get enter_new_name {
    return Intl.message(
      'Enter new name',
      name: 'enter_new_name',
      desc: '',
      args: [],
    );
  }

  /// `Delete this item?`
  String get delete_this_item {
    return Intl.message(
      'Delete this item?',
      name: 'delete_this_item',
      desc: '',
      args: [],
    );
  }

  /// `You will not be able to recover them afterwards`
  String get you_will_not_be {
    return Intl.message(
      'You will not be able to recover them afterwards',
      name: 'you_will_not_be',
      desc: '',
      args: [],
    );
  }

  /// `Advanced Mode`
  String get advance_mode {
    return Intl.message(
      'Advanced Mode',
      name: 'advance_mode',
      desc: '',
      args: [],
    );
  }

  /// `Card Count`
  String get card_count {
    return Intl.message(
      'Card Count',
      name: 'card_count',
      desc: '',
      args: [],
    );
  }

  /// `Maximum of 30 Cards per Set`
  String get max_30_cards_per_set {
    return Intl.message(
      'Maximum of 30 Cards per Set',
      name: 'max_30_cards_per_set',
      desc: '',
      args: [],
    );
  }

  /// `Card Difficulty`
  String get card_difficulty {
    return Intl.message(
      'Card Difficulty',
      name: 'card_difficulty',
      desc: '',
      args: [],
    );
  }

  /// `Topic (optional)`
  String get topic_option {
    return Intl.message(
      'Topic (optional)',
      name: 'topic_option',
      desc: '',
      args: [],
    );
  }

  /// `Maximum 3 Quiz Sets`
  String get max_3_quiz_sets {
    return Intl.message(
      'Maximum 3 Quiz Sets',
      name: 'max_3_quiz_sets',
      desc: '',
      args: [],
    );
  }

  /// `Quiz Sets`
  String get quiz_set {
    return Intl.message(
      'Quiz Sets',
      name: 'quiz_set',
      desc: '',
      args: [],
    );
  }

  /// `Mixed`
  String get mixed {
    return Intl.message(
      'Mixed',
      name: 'mixed',
      desc: '',
      args: [],
    );
  }

  /// `Easy`
  String get easy {
    return Intl.message(
      'Easy',
      name: 'easy',
      desc: '',
      args: [],
    );
  }

  /// `Medium`
  String get medium {
    return Intl.message(
      'Medium',
      name: 'medium',
      desc: '',
      args: [],
    );
  }

  /// `Hard`
  String get hard {
    return Intl.message(
      'Hard',
      name: 'hard',
      desc: '',
      args: [],
    );
  }

  /// `Quiz Count`
  String get quiz_count {
    return Intl.message(
      'Quiz Count',
      name: 'quiz_count',
      desc: '',
      args: [],
    );
  }

  /// `Quiz Difficulty`
  String get quiz_diff {
    return Intl.message(
      'Quiz Difficulty',
      name: 'quiz_diff',
      desc: '',
      args: [],
    );
  }

  /// `Generating AI ...`
  String get gen_ai {
    return Intl.message(
      'Generating AI ...',
      name: 'gen_ai',
      desc: '',
      args: [],
    );
  }

  /// `Limit AI experience`
  String get account_content_basic {
    return Intl.message(
      'Limit AI experience',
      name: 'account_content_basic',
      desc: '',
      args: [],
    );
  }

  /// `Unlock unlimited AI experience`
  String get account_content_pro {
    return Intl.message(
      'Unlock unlimited AI experience',
      name: 'account_content_pro',
      desc: '',
      args: [],
    );
  }

  /// `Basic`
  String get account_basic {
    return Intl.message(
      'Basic',
      name: 'account_basic',
      desc: '',
      args: [],
    );
  }

  /// `Your next bill is for {price} on {date}.`
  String next_bill_date(Object price, Object date) {
    return Intl.message(
      'Your next bill is for $price on $date.',
      name: 'next_bill_date',
      desc: '',
      args: [price, date],
    );
  }

  /// `Your Plan`
  String get your_plan {
    return Intl.message(
      'Your Plan',
      name: 'your_plan',
      desc: '',
      args: [],
    );
  }

  /// `Lifetime`
  String get account_lifetime {
    return Intl.message(
      'Lifetime',
      name: 'account_lifetime',
      desc: '',
      args: [],
    );
  }

  /// `Change Plan`
  String get change_plan {
    return Intl.message(
      'Change Plan',
      name: 'change_plan',
      desc: '',
      args: [],
    );
  }

  /// `Quarterly`
  String get quarterly {
    return Intl.message(
      'Quarterly',
      name: 'quarterly',
      desc: '',
      args: [],
    );
  }

  /// `Your trial will expire in {date} days.`
  String content_account_trial(Object date) {
    return Intl.message(
      'Your trial will expire in $date days.',
      name: 'content_account_trial',
      desc: '',
      args: [date],
    );
  }

  /// `Enter card count`
  String get enter_card_count {
    return Intl.message(
      'Enter card count',
      name: 'enter_card_count',
      desc: '',
      args: [],
    );
  }

  /// `Enter quiz count`
  String get enter_quiz_count {
    return Intl.message(
      'Enter quiz count',
      name: 'enter_quiz_count',
      desc: '',
      args: [],
    );
  }

  /// `Quizzes`
  String get quizzes {
    return Intl.message(
      'Quizzes',
      name: 'quizzes',
      desc: '',
      args: [],
    );
  }

  /// `Maximum of 30 Questions per Set`
  String get max_30_quiz_sets {
    return Intl.message(
      'Maximum of 30 Questions per Set',
      name: 'max_30_quiz_sets',
      desc: '',
      args: [],
    );
  }

  /// `Select a language`
  String get create_select_a_language {
    return Intl.message(
      'Select a language',
      name: 'create_select_a_language',
      desc: '',
      args: [],
    );
  }

  /// `Discard`
  String get discard {
    return Intl.message(
      'Discard',
      name: 'discard',
      desc: '',
      args: [],
    );
  }

  /// `The note has been successfully deleted.`
  String get delete_success {
    return Intl.message(
      'The note has been successfully deleted.',
      name: 'delete_success',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to remove this note?`
  String get content_delete_note_detail {
    return Intl.message(
      'Are you sure you want to remove this note?',
      name: 'content_delete_note_detail',
      desc: '',
      args: [],
    );
  }

  /// `Leaving will close the reminder notification and discard all changes.`
  String get content_discard_changes_reminder {
    return Intl.message(
      'Leaving will close the reminder notification and discard all changes.',
      name: 'content_discard_changes_reminder',
      desc: '',
      args: [],
    );
  }

  /// `Chat empty`
  String get chat_empty {
    return Intl.message(
      'Chat empty',
      name: 'chat_empty',
      desc: '',
      args: [],
    );
  }

  /// `Save Chat`
  String get save_chat {
    return Intl.message(
      'Save Chat',
      name: 'save_chat',
      desc: '',
      args: [],
    );
  }

  /// `Saved Chat`
  String get saved_chat {
    return Intl.message(
      'Saved Chat',
      name: 'saved_chat',
      desc: '',
      args: [],
    );
  }

  /// `Today`
  String get to_day {
    return Intl.message(
      'Today',
      name: 'to_day',
      desc: '',
      args: [],
    );
  }

  /// `Yesterday`
  String get yesterday {
    return Intl.message(
      'Yesterday',
      name: 'yesterday',
      desc: '',
      args: [],
    );
  }

  /// `Nova Chat`
  String get nova_chat {
    return Intl.message(
      'Nova Chat',
      name: 'nova_chat',
      desc: '',
      args: [],
    );
  }

  /// `Export`
  String get export {
    return Intl.message(
      'Export',
      name: 'export',
      desc: '',
      args: [],
    );
  }

  /// `Quiz`
  String get quiz {
    return Intl.message(
      'Quiz',
      name: 'quiz',
      desc: '',
      args: [],
    );
  }

  /// `Podcast`
  String get podcast {
    return Intl.message(
      'Podcast',
      name: 'podcast',
      desc: '',
      args: [],
    );
  }

  /// `Create Podcast`
  String get create_podcast {
    return Intl.message(
      'Create Podcast',
      name: 'create_podcast',
      desc: '',
      args: [],
    );
  }

  /// `Click 'Create Podcast' to turn your note into engaging audio`
  String get click_create_podcast {
    return Intl.message(
      'Click \'Create Podcast\' to turn your note into engaging audio',
      name: 'click_create_podcast',
      desc: '',
      args: [],
    );
  }

  /// `Podcast Name`
  String get podcast_name {
    return Intl.message(
      'Podcast Name',
      name: 'podcast_name',
      desc: '',
      args: [],
    );
  }

  /// `Export Video`
  String get export_video {
    return Intl.message(
      'Export Video',
      name: 'export_video',
      desc: '',
      args: [],
    );
  }

  /// `Saved Successfully`
  String get saved_successfully {
    return Intl.message(
      'Saved Successfully',
      name: 'saved_successfully',
      desc: '',
      args: [],
    );
  }

  /// `The audio/video will not be saved in the app. If you exit now, all your changes will be lost.`
  String get audio_video_not_save {
    return Intl.message(
      'The audio/video will not be saved in the app. If you exit now, all your changes will be lost.',
      name: 'audio_video_not_save',
      desc: '',
      args: [],
    );
  }

  /// `SALE OFF`
  String get sale_off {
    return Intl.message(
      'SALE OFF',
      name: 'sale_off',
      desc: '',
      args: [],
    );
  }

  /// `SPECIAL GIFT`
  String get special_gift_title {
    return Intl.message(
      'SPECIAL GIFT',
      name: 'special_gift_title',
      desc: '',
      args: [],
    );
  }

  /// `Supported image types: .png, .jpg, .heif, .heic`
  String get support_image {
    return Intl.message(
      'Supported image types: .png, .jpg, .heif, .heic',
      name: 'support_image',
      desc: '',
      args: [],
    );
  }

  /// `Upload Image`
  String get upload_image {
    return Intl.message(
      'Upload Image',
      name: 'upload_image',
      desc: '',
      args: [],
    );
  }

  /// `Camera`
  String get camera {
    return Intl.message(
      'Camera',
      name: 'camera',
      desc: '',
      args: [],
    );
  }

  /// `{images} photos have been uploaded`
  String images_have_been_uploaded(Object images) {
    return Intl.message(
      '$images photos have been uploaded',
      name: 'images_have_been_uploaded',
      desc: '',
      args: [images],
    );
  }

  /// `Image`
  String get image {
    return Intl.message(
      'Image',
      name: 'image',
      desc: '',
      args: [],
    );
  }

  /// `Closing will discard the photos you've captured`
  String get content_discard_changes_image {
    return Intl.message(
      'Closing will discard the photos you\'ve captured',
      name: 'content_discard_changes_image',
      desc: '',
      args: [],
    );
  }

  /// `Processing image...`
  String get processing_image {
    return Intl.message(
      'Processing image...',
      name: 'processing_image',
      desc: '',
      args: [],
    );
  }

  /// `Support for up to 10 images`
  String get support_for_up_to_10_images {
    return Intl.message(
      'Support for up to 10 images',
      name: 'support_for_up_to_10_images',
      desc: '',
      args: [],
    );
  }

  /// `Cannot create PDF file from image`
  String get cannot_create_pdf_file_from_image {
    return Intl.message(
      'Cannot create PDF file from image',
      name: 'cannot_create_pdf_file_from_image',
      desc: '',
      args: [],
    );
  }

  /// `Camera Access Required`
  String get camera_permission {
    return Intl.message(
      'Camera Access Required',
      name: 'camera_permission',
      desc: '',
      args: [],
    );
  }

  /// `The app needs camera access to take photos. Please grant permission in Settings.`
  String get camera_permission_denied_details {
    return Intl.message(
      'The app needs camera access to take photos. Please grant permission in Settings.',
      name: 'camera_permission_denied_details',
      desc: '',
      args: [],
    );
  }

  /// `Quiz set not found`
  String get quiz_set_not_found {
    return Intl.message(
      'Quiz set not found',
      name: 'quiz_set_not_found',
      desc: '',
      args: [],
    );
  }

  /// `Flashcard set not found`
  String get flashcard_set_not_found {
    return Intl.message(
      'Flashcard set not found',
      name: 'flashcard_set_not_found',
      desc: '',
      args: [],
    );
  }

  /// `File saved successfully`
  String get file_save_success {
    return Intl.message(
      'File saved successfully',
      name: 'file_save_success',
      desc: '',
      args: [],
    );
  }

  /// `Failed to save file`
  String get failed_to_save_file {
    return Intl.message(
      'Failed to save file',
      name: 'failed_to_save_file',
      desc: '',
      args: [],
    );
  }

  /// `Oops!\nSomething went wrong`
  String get oops_something_went_wrong {
    return Intl.message(
      'Oops!\nSomething went wrong',
      name: 'oops_something_went_wrong',
      desc: '',
      args: [],
    );
  }

  /// `We're having some trouble connecting to the server. Please try again in a moment.`
  String get trouble_connecting_to_server {
    return Intl.message(
      'We\'re having some trouble connecting to the server. Please try again in a moment.',
      name: 'trouble_connecting_to_server',
      desc: '',
      args: [],
    );
  }

  /// `Unlock unlimited access to all AI features`
  String get unlock_unlimited_access_to_all_ai_features {
    return Intl.message(
      'Unlock unlimited access to all AI features',
      name: 'unlock_unlimited_access_to_all_ai_features',
      desc: '',
      args: [],
    );
  }

  /// `Upgrade to Full Pro Access`
  String get upgrade_to_full_pro_access {
    return Intl.message(
      'Upgrade to Full Pro Access',
      name: 'upgrade_to_full_pro_access',
      desc: '',
      args: [],
    );
  }

  /// `Make the interface feel more like you — with theme, font, and layout preferences at your fingertips.`
  String get make_the_interface_feel_more_like_you {
    return Intl.message(
      'Make the interface feel more like you — with theme, font, and layout preferences at your fingertips.',
      name: 'make_the_interface_feel_more_like_you',
      desc: '',
      args: [],
    );
  }

  /// `Restart now`
  String get restart_now {
    return Intl.message(
      'Restart now',
      name: 'restart_now',
      desc: '',
      args: [],
    );
  }

  /// `Switch Mode`
  String get switch_mode {
    return Intl.message(
      'Switch Mode',
      name: 'switch_mode',
      desc: '',
      args: [],
    );
  }

  /// `Export Audio File`
  String get export_audio {
    return Intl.message(
      'Export Audio File',
      name: 'export_audio',
      desc: '',
      args: [],
    );
  }

  /// `Share Audio File`
  String get share_file {
    return Intl.message(
      'Share Audio File',
      name: 'share_file',
      desc: '',
      args: [],
    );
  }

  /// `The audio file will be permanently deleted from your device. This action cannot be undone.`
  String get delete_recording_setting_confirmation {
    return Intl.message(
      'The audio file will be permanently deleted from your device. This action cannot be undone.',
      name: 'delete_recording_setting_confirmation',
      desc: '',
      args: [],
    );
  }

  /// `Regenerate`
  String get Regenerate {
    return Intl.message(
      'Regenerate',
      name: 'Regenerate',
      desc: '',
      args: [],
    );
  }

  /// `Manage Recordings`
  String get manage_recordings {
    return Intl.message(
      'Manage Recordings',
      name: 'manage_recordings',
      desc: '',
      args: [],
    );
  }

  /// `Slideshow`
  String get slide_show {
    return Intl.message(
      'Slideshow',
      name: 'slide_show',
      desc: '',
      args: [],
    );
  }

  /// `Click 'Create Slideshow' to visualize your note as a presentation`
  String get click_create_slide {
    return Intl.message(
      'Click \'Create Slideshow\' to visualize your note as a presentation',
      name: 'click_create_slide',
      desc: '',
      args: [],
    );
  }

  /// `Create Slideshow`
  String get create_slide {
    return Intl.message(
      'Create Slideshow',
      name: 'create_slide',
      desc: '',
      args: [],
    );
  }

  /// `Slide Count`
  String get slide_count {
    return Intl.message(
      'Slide Count',
      name: 'slide_count',
      desc: '',
      args: [],
    );
  }

  /// `Maximum of 12 Slides per Template`
  String get slide_count_tooltip {
    return Intl.message(
      'Maximum of 12 Slides per Template',
      name: 'slide_count_tooltip',
      desc: '',
      args: [],
    );
  }

  /// `Templates`
  String get templates {
    return Intl.message(
      'Templates',
      name: 'templates',
      desc: '',
      args: [],
    );
  }

  /// `Enter Slide Count`
  String get enter_slide_count {
    return Intl.message(
      'Enter Slide Count',
      name: 'enter_slide_count',
      desc: '',
      args: [],
    );
  }

  /// `Slide Range`
  String get slide_range {
    return Intl.message(
      'Slide Range',
      name: 'slide_range',
      desc: '',
      args: [],
    );
  }

  /// `Daily Slideshow Limit Reached`
  String get daily_slideshow_limit_reached {
    return Intl.message(
      'Daily Slideshow Limit Reached',
      name: 'daily_slideshow_limit_reached',
      desc: '',
      args: [],
    );
  }

  /// `You've used up all Slideshow generations for today. This beta feature has daily limits to ensure stable performance. Come back tomorrow to create more AI-powered slideshows!`
  String get daily_slideshow_limit_reached_detail {
    return Intl.message(
      'You\'ve used up all Slideshow generations for today. This beta feature has daily limits to ensure stable performance. Come back tomorrow to create more AI-powered slideshows!',
      name: 'daily_slideshow_limit_reached_detail',
      desc: '',
      args: [],
    );
  }

  /// `Initializing camera...`
  String get initializing_camera {
    return Intl.message(
      'Initializing camera...',
      name: 'initializing_camera',
      desc: '',
      args: [],
    );
  }

  /// `Enables image reordering by selection and swapping`
  String get enables_swap {
    return Intl.message(
      'Enables image reordering by selection and swapping',
      name: 'enables_swap',
      desc: '',
      args: [],
    );
  }

  /// `Export as`
  String get export_as {
    return Intl.message(
      'Export as',
      name: 'export_as',
      desc: '',
      args: [],
    );
  }

  /// `"NoteX" Would like to Access the Camera`
  String get camera_access {
    return Intl.message(
      '"NoteX" Would like to Access the Camera',
      name: 'camera_access',
      desc: '',
      args: [],
    );
  }

  /// `NoteX needs access to your camera to capture, recognize, and digitize texts from images`
  String get content_camera_access {
    return Intl.message(
      'NoteX needs access to your camera to capture, recognize, and digitize texts from images',
      name: 'content_camera_access',
      desc: '',
      args: [],
    );
  }

  /// `Photos`
  String get photos {
    return Intl.message(
      'Photos',
      name: 'photos',
      desc: '',
      args: [],
    );
  }

  /// `Failed to load the slideshow from the system. Retry for a better experience.`
  String get failed_to_load_slideshow {
    return Intl.message(
      'Failed to load the slideshow from the system. Retry for a better experience.',
      name: 'failed_to_load_slideshow',
      desc: '',
      args: [],
    );
  }

  /// `Style`
  String get style {
    return Intl.message(
      'Style',
      name: 'style',
      desc: '',
      args: [],
    );
  }

  /// `NEW`
  String get new_new {
    return Intl.message(
      'NEW',
      name: 'new_new',
      desc: '',
      args: [],
    );
  }

  /// `Creating quiz questions`
  String get creating_quiz {
    return Intl.message(
      'Creating quiz questions',
      name: 'creating_quiz',
      desc: '',
      args: [],
    );
  }

  /// `Generating quiz background`
  String get gen_quiz_bgr {
    return Intl.message(
      'Generating quiz background',
      name: 'gen_quiz_bgr',
      desc: '',
      args: [],
    );
  }

  /// `Generating AI voice`
  String get gen_ai_voice {
    return Intl.message(
      'Generating AI voice',
      name: 'gen_ai_voice',
      desc: '',
      args: [],
    );
  }

  /// `Mapping all together`
  String get map_all_together {
    return Intl.message(
      'Mapping all together',
      name: 'map_all_together',
      desc: '',
      args: [],
    );
  }

  /// `Making it look amazing! This quiz video will be worth sharing #NoteXAI`
  String get making_amazing {
    return Intl.message(
      'Making it look amazing! This quiz video will be worth sharing #NoteXAI',
      name: 'making_amazing',
      desc: '',
      args: [],
    );
  }

  /// `Success`
  String get success {
    return Intl.message(
      'Success',
      name: 'success',
      desc: '',
      args: [],
    );
  }

  /// `Fail`
  String get fail {
    return Intl.message(
      'Fail',
      name: 'fail',
      desc: '',
      args: [],
    );
  }

  /// `Preview only. Background will be AI-generated based on content`
  String get preview_only {
    return Intl.message(
      'Preview only. Background will be AI-generated based on content',
      name: 'preview_only',
      desc: '',
      args: [],
    );
  }

  /// `Successfully`
  String get successfully {
    return Intl.message(
      'Successfully',
      name: 'successfully',
      desc: '',
      args: [],
    );
  }

  /// `Document will be available after note is successfully created!`
  String get document_available {
    return Intl.message(
      'Document will be available after note is successfully created!',
      name: 'document_available',
      desc: '',
      args: [],
    );
  }

  /// `Customize Note Tabs`
  String get customize_note_tabs {
    return Intl.message(
      'Customize Note Tabs',
      name: 'customize_note_tabs',
      desc: '',
      args: [],
    );
  }

  /// `Note Tabs`
  String get note_tabs {
    return Intl.message(
      'Note Tabs',
      name: 'note_tabs',
      desc: '',
      args: [],
    );
  }

  /// `All Tabs`
  String get all_tabs {
    return Intl.message(
      'All Tabs',
      name: 'all_tabs',
      desc: '',
      args: [],
    );
  }

  /// `Select and reorder your note modules. A minimum of 4 tabs is required to continue.`
  String get select_and_reorder {
    return Intl.message(
      'Select and reorder your note modules. A minimum of 4 tabs is required to continue.',
      name: 'select_and_reorder',
      desc: '',
      args: [],
    );
  }

  /// `Deselect`
  String get deselect {
    return Intl.message(
      'Deselect',
      name: 'deselect',
      desc: '',
      args: [],
    );
  }

  /// `Select All`
  String get select_all {
    return Intl.message(
      'Select All',
      name: 'select_all',
      desc: '',
      args: [],
    );
  }

  /// `Customize Your Note View`
  String get customize_your_note_view {
    return Intl.message(
      'Customize Your Note View',
      name: 'customize_your_note_view',
      desc: '',
      args: [],
    );
  }

  /// `Select your note modules.`
  String get select_your_note {
    return Intl.message(
      'Select your note modules.',
      name: 'select_your_note',
      desc: '',
      args: [],
    );
  }

  /// `You can update anytime in Settings.`
  String get you_can_update_setting {
    return Intl.message(
      'You can update anytime in Settings.',
      name: 'you_can_update_setting',
      desc: '',
      args: [],
    );
  }

  /// `Dark`
  String get dark {
    return Intl.message(
      'Dark',
      name: 'dark',
      desc: '',
      args: [],
    );
  }

  /// `Light`
  String get light {
    return Intl.message(
      'Light',
      name: 'light',
      desc: '',
      args: [],
    );
  }

  /// `System`
  String get system {
    return Intl.message(
      'System',
      name: 'system',
      desc: '',
      args: [],
    );
  }

  /// `Appearance`
  String get appearance {
    return Intl.message(
      'Appearance',
      name: 'appearance',
      desc: '',
      args: [],
    );
  }

  /// `Transform your notes`
  String get craft_visual_stories {
    return Intl.message(
      'Transform your notes',
      name: 'craft_visual_stories',
      desc: '',
      args: [],
    );
  }

  /// `into slides`
  String get every_note_you_take {
    return Intl.message(
      'into slides',
      name: 'every_note_you_take',
      desc: '',
      args: [],
    );
  }

  /// `Generate engaging slides instantly`
  String get auto_generate_slides {
    return Intl.message(
      'Generate engaging slides instantly',
      name: 'auto_generate_slides',
      desc: '',
      args: [],
    );
  }

  /// `Transform your notes into slides`
  String get craft_visual_from_every_note {
    return Intl.message(
      'Transform your notes into slides',
      name: 'craft_visual_from_every_note',
      desc: '',
      args: [],
    );
  }

  /// `Thinking...`
  String get thinking {
    return Intl.message(
      'Thinking...',
      name: 'thinking',
      desc: '',
      args: [],
    );
  }

  /// `If you subscribed via web, manage at notexapp.com/setting`
  String get subscribe_via_web {
    return Intl.message(
      'If you subscribed via web, manage at notexapp.com/setting',
      name: 'subscribe_via_web',
      desc: '',
      args: [],
    );
  }

  /// `Customize Note Tabs`
  String get custom_note_tabs {
    return Intl.message(
      'Customize Note Tabs',
      name: 'custom_note_tabs',
      desc: '',
      args: [],
    );
  }

  /// `Transcript Context`
  String get transcript_context {
    return Intl.message(
      'Transcript Context',
      name: 'transcript_context',
      desc: '',
      args: [],
    );
  }

  /// `Select`
  String get select {
    return Intl.message(
      'Select',
      name: 'select',
      desc: '',
      args: [],
    );
  }

  /// `Items`
  String get items {
    return Intl.message(
      'Items',
      name: 'items',
      desc: '',
      args: [],
    );
  }

  /// `Move`
  String get move {
    return Intl.message(
      'Move',
      name: 'move',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
      Locale.fromSubtags(languageCode: 'de'),
      Locale.fromSubtags(languageCode: 'es'),
      Locale.fromSubtags(languageCode: 'fr'),
      Locale.fromSubtags(languageCode: 'he'),
      Locale.fromSubtags(languageCode: 'it'),
      Locale.fromSubtags(languageCode: 'ja'),
      Locale.fromSubtags(languageCode: 'pt'),
      Locale.fromSubtags(languageCode: 'vi'),
      Locale.fromSubtags(languageCode: 'zh', countryCode: 'CN'),
      Locale.fromSubtags(
          languageCode: 'zh', scriptCode: 'Hant', countryCode: 'HK'),
      Locale.fromSubtags(
          languageCode: 'zh', scriptCode: 'Hant', countryCode: 'TW'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
