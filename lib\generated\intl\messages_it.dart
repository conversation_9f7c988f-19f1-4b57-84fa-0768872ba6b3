// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a it locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'it';

  static String m0(date) => "La tua prova scadrà tra ${date} giorni.";

  static String m1(images) => "${images} foto sono state caricate";

  static String m2(price, date) =>
      "La prossima fattura di ${price} sarà il ${date}.";

  static String m3(uid) => "uid ${uid} copiato negli appunti!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Regenerate": MessageLookupByLibrary.simpleMessage("Rergenerare"),
        "a_to_z": MessageLookupByLibrary.simpleMessage("A-Z"),
        "about_us": MessageLookupByLibrary.simpleMessage("Chi siamo"),
        "access_notex_web":
            MessageLookupByLibrary.simpleMessage("Accedi a NoteX web"),
        "account": MessageLookupByLibrary.simpleMessage("Account"),
        "account_basic": MessageLookupByLibrary.simpleMessage("Base"),
        "account_content_basic":
            MessageLookupByLibrary.simpleMessage("Esperienza IA limitata"),
        "account_content_pro": MessageLookupByLibrary.simpleMessage(
            "Sblocca esperienza IA illimitata"),
        "account_lifetime": MessageLookupByLibrary.simpleMessage("A vita"),
        "achieve_more": MessageLookupByLibrary.simpleMessage("OTTIENI DI PIÙ"),
        "action_items": MessageLookupByLibrary.simpleMessage("Elementi azione"),
        "actionable_intelligence":
            MessageLookupByLibrary.simpleMessage("dati utili"),
        "active_description": MessageLookupByLibrary.simpleMessage(
            "Descrizione attiva non trovata."),
        "active_recall":
            MessageLookupByLibrary.simpleMessage("richiamo attivo"),
        "add_folder":
            MessageLookupByLibrary.simpleMessage("Sposta in cartella"),
        "add_note": MessageLookupByLibrary.simpleMessage("Aggiungi nota"),
        "add_password":
            MessageLookupByLibrary.simpleMessage("Aggiungi password"),
        "add_password_to_public": MessageLookupByLibrary.simpleMessage(
            "Aggiungi password al link pubblico"),
        "add_to": MessageLookupByLibrary.simpleMessage("Sposta in"),
        "add_to_notes":
            MessageLookupByLibrary.simpleMessage("Aggiungi alle note"),
        "additional_ins": MessageLookupByLibrary.simpleMessage(
            "Istruzioni Aggiuntive (opz.)"),
        "advance_mode":
            MessageLookupByLibrary.simpleMessage("Modalità avanzata"),
        "advanced": MessageLookupByLibrary.simpleMessage("Avanzato"),
        "afternoon_content": MessageLookupByLibrary.simpleMessage(
            "Piccole note, grande impatto"),
        "afternoon_content_1": MessageLookupByLibrary.simpleMessage(
            "Pensieri catturati, mente libera"),
        "afternoon_content_3":
            MessageLookupByLibrary.simpleMessage("Ordine nel caos"),
        "afternoon_content_4":
            MessageLookupByLibrary.simpleMessage("Le tue idee, organizzate"),
        "afternoon_content_5":
            MessageLookupByLibrary.simpleMessage("Chiarezza in progresso"),
        "afternoon_content_6":
            MessageLookupByLibrary.simpleMessage("Conserva ciò che conta"),
        "ai_audio_transcription_per_day": MessageLookupByLibrary.simpleMessage(
            "3 trascrizioni AI audio al giorno *"),
        "ai_chat": MessageLookupByLibrary.simpleMessage("Nova IA"),
        "ai_chat_assistant":
            MessageLookupByLibrary.simpleMessage("Assistente chat AI"),
        "ai_chat_with_notes":
            MessageLookupByLibrary.simpleMessage("Chat AI con note"),
        "ai_insight": MessageLookupByLibrary.simpleMessage("Intuizioni AI"),
        "ai_learning": MessageLookupByLibrary.simpleMessage("Apprendimento AI"),
        "ai_learning_companion":
            MessageLookupByLibrary.simpleMessage("Sono Nova AI di NoteX"),
        "ai_note_create":
            MessageLookupByLibrary.simpleMessage("Creazione note AI"),
        "ai_note_creation":
            MessageLookupByLibrary.simpleMessage("Creazione note AI"),
        "ai_note_from":
            MessageLookupByLibrary.simpleMessage("Note AI da audio"),
        "ai_notes_10": MessageLookupByLibrary.simpleMessage(
            "Note AI illimitate da YouTube e documenti"),
        "ai_notes_3": MessageLookupByLibrary.simpleMessage(
            "3 note IA al giorno da registrazioni e caricamenti audio (fino a 60 min per file)"),
        "ai_notes_from": MessageLookupByLibrary.simpleMessage(
            "Note AI da\nYouTube, Web, Docs"),
        "ai_short_1": MessageLookupByLibrary.simpleMessage(
            "3 generazioni di video brevi IA al giorno"),
        "ai_short_3": MessageLookupByLibrary.simpleMessage(
            "5 video brevi AI al giorno (beta)"),
        "ai_short_video":
            MessageLookupByLibrary.simpleMessage("Video brevi AI"),
        "ai_study_practice":
            MessageLookupByLibrary.simpleMessage("Pratica studio AI"),
        "ai_study_tools":
            MessageLookupByLibrary.simpleMessage("Strumenti studio AI"),
        "ai_summarize": MessageLookupByLibrary.simpleMessage("Riassunto AI"),
        "ai_transcription":
            MessageLookupByLibrary.simpleMessage("Trascrizione AI"),
        "ai_workflow": MessageLookupByLibrary.simpleMessage("Flusso lavoro AI"),
        "all": MessageLookupByLibrary.simpleMessage("Tutto"),
        "all_note": MessageLookupByLibrary.simpleMessage("Tutte le note"),
        "all_note_in_folder": MessageLookupByLibrary.simpleMessage(
            "Sei sicuro di voler rimuovere questa cartella?"),
        "all_tabs": MessageLookupByLibrary.simpleMessage("Tutte le schede"),
        "allow": MessageLookupByLibrary.simpleMessage("Consenti"),
        "almost_done": MessageLookupByLibrary.simpleMessage("Quasi finito"),
        "and": MessageLookupByLibrary.simpleMessage("e"),
        "answer": MessageLookupByLibrary.simpleMessage("Risposta"),
        "anyone_with_link": MessageLookupByLibrary.simpleMessage(
            "Chiunque abbia il link può visualizzare"),
        "app_feedback":
            MessageLookupByLibrary.simpleMessage("Feedback per l’app NoteX"),
        "app_store":
            MessageLookupByLibrary.simpleMessage("recensione su App Store"),
        "appearance": MessageLookupByLibrary.simpleMessage("Aspetto"),
        "appreciate_cooperation": MessageLookupByLibrary.simpleMessage(
            "Queste informazioni aiutano il nostro team a identificare e risolvere il tuo problema. Grazie per la collaborazione!"),
        "appreciate_cooperation2": MessageLookupByLibrary.simpleMessage(
            "Questo ci aiuta a risolvere il tuo problema efficacemente."),
        "appreciate_cooperation3":
            MessageLookupByLibrary.simpleMessage("Grazie per usare NoteX AI!"),
        "are_you_sure": MessageLookupByLibrary.simpleMessage("Offerta unica"),
        "ask_anything":
            MessageLookupByLibrary.simpleMessage("Chiedi qualsiasi cosa..."),
        "assist_faster": MessageLookupByLibrary.simpleMessage(
            "Per aiutarti più velocemente:"),
        "assistant": MessageLookupByLibrary.simpleMessage("assistente"),
        "at_your_pace": MessageLookupByLibrary.simpleMessage("un A+"),
        "audio": MessageLookupByLibrary.simpleMessage("Audio"),
        "audio_file": MessageLookupByLibrary.simpleMessage("File audio"),
        "audio_is_temporary": MessageLookupByLibrary.simpleMessage(
            "*Audio temporaneo - Salva prima di chiudere"),
        "audio_length_err": MessageLookupByLibrary.simpleMessage(
            "File audio troppo lungo. Carica un file più corto."),
        "audio_length_limit":
            MessageLookupByLibrary.simpleMessage("Limite durata audio"),
        "audio_process_err": MessageLookupByLibrary.simpleMessage(
            "Impossibile elaborare il file audio. Riprova con un altro file."),
        "audio_recording_ai_notes_daily": MessageLookupByLibrary.simpleMessage(
            "3 note AI al giorno da audio e registrazioni*"),
        "audio_to_ai_note":
            MessageLookupByLibrary.simpleMessage("Audio in note AI"),
        "audio_upload_note":
            MessageLookupByLibrary.simpleMessage("Caricamento audio"),
        "auto": MessageLookupByLibrary.simpleMessage("Auto"),
        "auto_detect":
            MessageLookupByLibrary.simpleMessage("Rileva automaticamente"),
        "auto_generate_slides": MessageLookupByLibrary.simpleMessage(
            "Genera diapositive coinvolgenti all\'istante"),
        "auto_renew_after_trial": MessageLookupByLibrary.simpleMessage(
            "Rinnovo automatico post-prova • Annulla quando vuoi"),
        "auto_renewable_after_trial": MessageLookupByLibrary.simpleMessage(
            "Rinnovo automatico post-prova. Annulla quando vuoi"),
        "auto_renewal": MessageLookupByLibrary.simpleMessage(
            "Rinnovo automatico, annulla quando vuoi"),
        "available_credits":
            MessageLookupByLibrary.simpleMessage("Crediti disponibili"),
        "available_transcript": MessageLookupByLibrary.simpleMessage(
            "Trascrizione disponibile dopo la creazione della nota!"),
        "back_content": MessageLookupByLibrary.simpleMessage(" punti"),
        "background_style":
            MessageLookupByLibrary.simpleMessage("Stile sfondo"),
        "balanced": MessageLookupByLibrary.simpleMessage("Bilanciato"),
        "balanced_description": MessageLookupByLibrary.simpleMessage(
            "Idee principali con contesto"),
        "basic": MessageLookupByLibrary.simpleMessage("Piano Base"),
        "basic_features":
            MessageLookupByLibrary.simpleMessage("Funzioni AI base"),
        "beta": MessageLookupByLibrary.simpleMessage("Beta"),
        "between_concepts":
            MessageLookupByLibrary.simpleMessage("Collega i concetti tra loro"),
        "black_friday_sale":
            MessageLookupByLibrary.simpleMessage("Saldi di Natale!"),
        "blurred_output_image": MessageLookupByLibrary.simpleMessage(
            "Generazione stile fallita! Cambia stile o immagine!"),
        "body_error_document_upload": MessageLookupByLibrary.simpleMessage(
            "Problema con il documento caricato. Vai all’app e riprova."),
        "body_error_note_document": MessageLookupByLibrary.simpleMessage(
            "Problema con il documento. Vai all’app e riprova."),
        "body_error_note_recording": MessageLookupByLibrary.simpleMessage(
            "Problema con la registrazione. Vai all’app e riprova."),
        "body_error_note_upload": MessageLookupByLibrary.simpleMessage(
            "Problema con l’audio caricato. Vai all’app e riprova."),
        "body_error_note_web": MessageLookupByLibrary.simpleMessage(
            "Problema con il link web. Vai all’app e riprova."),
        "body_error_note_youtube": MessageLookupByLibrary.simpleMessage(
            "Problema con il link YouTube. Vai all’app e riprova."),
        "body_success_note": MessageLookupByLibrary.simpleMessage(
            "La tua nota AI è pronta per la revisione."),
        "bonus_credits_for_new_referred_friends_only":
            MessageLookupByLibrary.simpleMessage(
                "Crediti bonus solo per nuovi amici invitati"),
        "boost_comprehension": MessageLookupByLibrary.simpleMessage(
            "Migliora comprensione e memoria"),
        "boost_comprehension2":
            MessageLookupByLibrary.simpleMessage("Migliora comprensione"),
        "boost_flashcards_quizzes": MessageLookupByLibrary.simpleMessage(
            "Migliora con flashcard e quiz AI"),
        "boost_knowledge":
            MessageLookupByLibrary.simpleMessage("Collega i puntini"),
        "boost_knowledge_retention":
            MessageLookupByLibrary.simpleMessage("Collega i concetti"),
        "both_you_friends_receive_usage_credits":
            MessageLookupByLibrary.simpleMessage(
                "Tu e i tuoi amici riceverete crediti d’uso."),
        "brief_service_disruption": MessageLookupByLibrary.simpleMessage(
            "Interruzione breve. Riprova presto. Aggiornamenti su Discord!"),
        "business_uses": MessageLookupByLibrary.simpleMessage("Uso aziendale"),
        "button_below": MessageLookupByLibrary.simpleMessage(
            "Tocca il pulsante in basso o scegli un tipo di contenuto specifico per iniziare"),
        "buy_one_forever": MessageLookupByLibrary.simpleMessage(
            "Compra una volta. Produttività massima per sempre."),
        "by_subscribing":
            MessageLookupByLibrary.simpleMessage("Abbonandoti accetti i"),
        "by_taping_continue": MessageLookupByLibrary.simpleMessage(
            "Continuando, accetti i nostri"),
        "by_tapping_started": MessageLookupByLibrary.simpleMessage(
            "Toccando \'Inizia\', accetti i nostri"),
        "camera": MessageLookupByLibrary.simpleMessage("Fotocamera"),
        "camera_access": MessageLookupByLibrary.simpleMessage(
            "\"NoteX\" Vorrebbe Accedere alla Fotocamera"),
        "camera_permission": MessageLookupByLibrary.simpleMessage(
            "Accesso alla fotocamera necessario"),
        "camera_permission_denied_details": MessageLookupByLibrary.simpleMessage(
            "Accesso alla fotocamera necessario per selezionare immagini. Per favore, concedi il permesso nelle impostazioni."),
        "can_improve":
            MessageLookupByLibrary.simpleMessage("Cosa possiamo migliorare?"),
        "cancel": MessageLookupByLibrary.simpleMessage("Annulla"),
        "cannot_create_pdf_file_from_image":
            MessageLookupByLibrary.simpleMessage(
                "Impossibile creare file PDF dall\'immagine"),
        "cannot_extract_text_from_pdf": MessageLookupByLibrary.simpleMessage(
            "Impossibile leggere il documento. Nessun testo estratto (es. PDF scannerizzati)."),
        "card": MessageLookupByLibrary.simpleMessage("Carta"),
        "card_count": MessageLookupByLibrary.simpleMessage("Conteggio carte"),
        "card_difficulty":
            MessageLookupByLibrary.simpleMessage("Difficoltà della carta"),
        "change": MessageLookupByLibrary.simpleMessage("Cambia"),
        "change_plan": MessageLookupByLibrary.simpleMessage("Cambia piano"),
        "chaos_into_clarity":
            MessageLookupByLibrary.simpleMessage("caos in chiarezza"),
        "characters": MessageLookupByLibrary.simpleMessage("caratteri"),
        "chat_empty": MessageLookupByLibrary.simpleMessage("Chat vuota"),
        "chat_topic_temporary_stored": MessageLookupByLibrary.simpleMessage(
            "Sessione temporanea, usa \"Salva Chat\" per conservare"),
        "check_if_you": MessageLookupByLibrary.simpleMessage(
            "Verifica di essere loggato con l’account Google corretto"),
        "check_update":
            MessageLookupByLibrary.simpleMessage("Nuova versione disponibile"),
        "child_detected": MessageLookupByLibrary.simpleMessage(
            "Rilevato minore. Carica un’altra immagine."),
        "choose_your_note":
            MessageLookupByLibrary.simpleMessage("Scegli il tuo NoteX"),
        "choose_your_note_experience": MessageLookupByLibrary.simpleMessage(
            "Scegli la tua esperienza NoteX"),
        "click_create_podcast": MessageLookupByLibrary.simpleMessage(
            "Fai clic su \'Crea podcast\' per trasformare la tua nota in un audio coinvolgente"),
        "click_create_short": MessageLookupByLibrary.simpleMessage(
            "Clicca \'Crea Breve\' per trasformare la nota in brevi coinvolgenti"),
        "click_create_slide": MessageLookupByLibrary.simpleMessage(
            "Crea un diaporama per visualizzare la tua nota come una presentazione"),
        "click_start_flashcard": MessageLookupByLibrary.simpleMessage(
            "Clicca su \'Crea flashcard\' per generare set di flashcard basati sulla trascrizione. Puoi creare più set."),
        "click_start_mindmap": MessageLookupByLibrary.simpleMessage(
            "Clicca su \'Crea mappa mentale\' per generare una mappa mentale basata sulla trascrizione"),
        "click_start_quiz": MessageLookupByLibrary.simpleMessage(
            "Clicca su \'Crea quiz\' per generare set di domande basati sulla trascrizione. Puoi creare più set."),
        "click_to_flip":
            MessageLookupByLibrary.simpleMessage("Clicca per girare"),
        "coming_soon": MessageLookupByLibrary.simpleMessage("Prossimamente"),
        "community": MessageLookupByLibrary.simpleMessage("Comunità"),
        "community_feedback":
            MessageLookupByLibrary.simpleMessage("Comunità e feedback"),
        "comprehensive": MessageLookupByLibrary.simpleMessage("Completo"),
        "comprehensive_description": MessageLookupByLibrary.simpleMessage(
            "Copertura dettagliata con punti di supporto"),
        "congratulations":
            MessageLookupByLibrary.simpleMessage("Congratulazioni!"),
        "connect_friends": MessageLookupByLibrary.simpleMessage(
            "Importa facilmente i link delle note condivise dagli amici"),
        "connection_fail":
            MessageLookupByLibrary.simpleMessage("Connessione fallita!"),
        "connection_timeout": MessageLookupByLibrary.simpleMessage(
            "Timeout della connessione. Controlla la tua connessione internet e riprova."),
        "contact_support":
            MessageLookupByLibrary.simpleMessage("Contatta supporto"),
        "content_account_trial": m0,
        "content_button_flashcard":
            MessageLookupByLibrary.simpleMessage("Crea flashcard"),
        "content_button_mindmap":
            MessageLookupByLibrary.simpleMessage("Crea mappa mentale"),
        "content_button_quiz":
            MessageLookupByLibrary.simpleMessage("Crea quiz"),
        "content_button_summary":
            MessageLookupByLibrary.simpleMessage("Genera riassunto"),
        "content_camera_access": MessageLookupByLibrary.simpleMessage(
            "NoteX ha bisogno di accedere alla fotocamera per catturare, riconoscere e digitalizzare testi dalle immagini"),
        "content_delete_note": MessageLookupByLibrary.simpleMessage(
            "Non potrai più recuperarla dopo."),
        "content_delete_note_detail": MessageLookupByLibrary.simpleMessage(
            "Sei sicuro di voler rimuovere questa nota?"),
        "content_delete_reminder": MessageLookupByLibrary.simpleMessage(
            "Sicuro di rimuovere questo promemoria?"),
        "content_discard_changes": MessageLookupByLibrary.simpleMessage(
            "Uscire interrompe la registrazione e scarta tutto."),
        "content_discard_changes_image": MessageLookupByLibrary.simpleMessage(
            "Chiudendo si perderanno le foto che hai scattato"),
        "content_discard_changes_note": MessageLookupByLibrary.simpleMessage(
            "Questa azione scarta tutte le modifiche senza possibilità di recupero."),
        "content_discard_changes_reminder": MessageLookupByLibrary.simpleMessage(
            "Uscendo si chiuderà la notifica del promemoria e si perderanno tutte le modifiche."),
        "content_empty_flashcard": MessageLookupByLibrary.simpleMessage(
            "Il riassunto apparirà qui dopo la riunione."),
        "content_empty_quiz": MessageLookupByLibrary.simpleMessage(
            "Il riassunto apparirà qui dopo la riunione."),
        "content_hour":
            MessageLookupByLibrary.simpleMessage("Ore di contenuti in"),
        "content_hour_insight": MessageLookupByLibrary.simpleMessage(
            "Ore di contenuti in intuizioni"),
        "content_minute_left": MessageLookupByLibrary.simpleMessage(
            "Le registrazioni oltre il limite gratuito saranno salvate localmente senza trascrizione AI. Passa a Pro."),
        "content_payment_successfully": MessageLookupByLibrary.simpleMessage(
            "Grazie per l’acquisto. Transazione completata con successo."),
        "content_quarter_01": MessageLookupByLibrary.simpleMessage(
            "Note AI illimitate da registrazioni, caricamenti, link YouTube."),
        "content_quarter_02": MessageLookupByLibrary.simpleMessage(
            "Chat AI, mappe mentali, flashcard, quiz, condivisione note illimitati."),
        "content_save_changes": MessageLookupByLibrary.simpleMessage(
            "Questa azione salva tutte le modifiche definitivamente."),
        "continue_3_day":
            MessageLookupByLibrary.simpleMessage("Prova gratuita 3 giorni"),
        "continue_button": MessageLookupByLibrary.simpleMessage("Continua"),
        "continue_with_apple":
            MessageLookupByLibrary.simpleMessage("Continua con Apple"),
        "continue_with_email":
            MessageLookupByLibrary.simpleMessage("Continua con email"),
        "continue_with_google":
            MessageLookupByLibrary.simpleMessage("Continua con Google"),
        "copied_to_clipboard":
            MessageLookupByLibrary.simpleMessage("Copiato negli appunti"),
        "copy": MessageLookupByLibrary.simpleMessage("Copia"),
        "copy_your_referral_code": MessageLookupByLibrary.simpleMessage(
            "Copia il tuo codice referral."),
        "correct": MessageLookupByLibrary.simpleMessage("Corretto"),
        "craft_visual_from_every_note": MessageLookupByLibrary.simpleMessage(
            "Trasforma i tuoi appunti in diapositive"),
        "craft_visual_stories":
            MessageLookupByLibrary.simpleMessage("Trasforma i tuoi appunti"),
        "create": MessageLookupByLibrary.simpleMessage("Crea"),
        "create_folder": MessageLookupByLibrary.simpleMessage("Crea cartella"),
        "create_lecture": MessageLookupByLibrary.simpleMessage(
            "Crea riassunti concisi delle lezioni"),
        "create_new_folder":
            MessageLookupByLibrary.simpleMessage("Crea nuova cartella"),
        "create_note_successfully":
            MessageLookupByLibrary.simpleMessage("Nota creata con successo!"),
        "create_notes":
            MessageLookupByLibrary.simpleMessage("Creazione note AI..."),
        "create_podcast": MessageLookupByLibrary.simpleMessage("Crea podcast"),
        "create_reminder":
            MessageLookupByLibrary.simpleMessage("Crea promemoria"),
        "create_select_a_language":
            MessageLookupByLibrary.simpleMessage("Seleziona una lingua"),
        "create_short": MessageLookupByLibrary.simpleMessage("Crea breve"),
        "create_shorts": MessageLookupByLibrary.simpleMessage("Crea brevi"),
        "create_slide":
            MessageLookupByLibrary.simpleMessage("Crea un diaporama"),
        "creating_note":
            MessageLookupByLibrary.simpleMessage("Creazione nota..."),
        "creating_quiz": MessageLookupByLibrary.simpleMessage(
            "Creazione delle domande del quiz"),
        "credit": MessageLookupByLibrary.simpleMessage("Credito"),
        "credits": MessageLookupByLibrary.simpleMessage("Crediti"),
        "credits_can_be_used_to_create_notes_and_access_the_features_within_them":
            MessageLookupByLibrary.simpleMessage(
                "I crediti servono per creare note e accedere alle funzioni. Se l’abbonamento scade, usa i crediti per continuare."),
        "credits_earned":
            MessageLookupByLibrary.simpleMessage("Crediti guadagnati"),
        "credits_premium_features":
            MessageLookupByLibrary.simpleMessage("crediti e funzioni premium."),
        "credits_used": MessageLookupByLibrary.simpleMessage("Crediti usati"),
        "current_plan": MessageLookupByLibrary.simpleMessage("Piano attuale"),
        "custom_note_tabs": MessageLookupByLibrary.simpleMessage(
            "Personalizza le schede della nota"),
        "customize_note_tabs": MessageLookupByLibrary.simpleMessage(
            "Personalizza le schede della nota"),
        "customize_your_note_view": MessageLookupByLibrary.simpleMessage(
            "Personalizza la visualizzazione delle tue note"),
        "daily_10": MessageLookupByLibrary.simpleMessage("10 al giorno"),
        "daily_3": MessageLookupByLibrary.simpleMessage("3 al giorno"),
        "daily_5": MessageLookupByLibrary.simpleMessage("5 al giorno"),
        "daily_rewards_limit_reached": MessageLookupByLibrary.simpleMessage(
            "Limite premi giornalieri raggiunto. Riprova domani!"),
        "daily_shorts_limit_reached": MessageLookupByLibrary.simpleMessage(
            "Limite brevi giornalieri raggiunto (Beta)"),
        "daily_shorts_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
            "Hai usato tutti i brevi di oggi. Funzione beta con limiti giornalieri. Torna domani!"),
        "daily_slideshow_limit_reached": MessageLookupByLibrary.simpleMessage(
            "Limite diapositive giornaliero raggiunto"),
        "daily_slideshow_limit_reached_detail":
            MessageLookupByLibrary.simpleMessage(
                "Hai esaurito tutte le generazioni di presentazioni per oggi. Questa funzione beta ha limiti giornalieri per garantire prestazioni stabili. Torna domani per creare altre presentazioni basate sull\'IA!"),
        "dark": MessageLookupByLibrary.simpleMessage("Scuro"),
        "data": MessageLookupByLibrary.simpleMessage("dati"),
        "day_free_trial_access_all_features": MessageLookupByLibrary.simpleMessage(
            "7-giorno di prova gratuito per accedere a tutte le funzionalità, poi solo "),
        "days": MessageLookupByLibrary.simpleMessage("Giorni"),
        "db_err": MessageLookupByLibrary.simpleMessage(
            "Errore database. Riprova più tardi."),
        "deals_left_at_this_price": MessageLookupByLibrary.simpleMessage(
            "offerte a vita a questo prezzo"),
        "decline_free_trial":
            MessageLookupByLibrary.simpleMessage("Rifiuta prova gratuita"),
        "default_error": MessageLookupByLibrary.simpleMessage(
            "Qualcosa è andato storto! Riprova!"),
        "delete": MessageLookupByLibrary.simpleMessage("Elimina"),
        "delete_account":
            MessageLookupByLibrary.simpleMessage("Elimina account"),
        "delete_account_detail": MessageLookupByLibrary.simpleMessage(
            "Azione irreversibile. Eliminerai definitivamente: tutte le note e registrazioni"),
        "delete_all_note": MessageLookupByLibrary.simpleMessage(
            "Elimina tutte le note nella cartella"),
        "delete_folder":
            MessageLookupByLibrary.simpleMessage("Elimina cartella"),
        "delete_note":
            MessageLookupByLibrary.simpleMessage("Eliminare questa nota?"),
        "delete_note_item":
            MessageLookupByLibrary.simpleMessage("Elimina nota"),
        "delete_recording":
            MessageLookupByLibrary.simpleMessage("Elimina registrazione"),
        "delete_recording_confirmation": MessageLookupByLibrary.simpleMessage(
            "Vuoi davvero eliminare questo registro"),
        "delete_recording_setting_confirmation":
            MessageLookupByLibrary.simpleMessage(
                "Il file audio verrà eliminato definitivamente dal tuo dispositivo. Questa azione non può essere annullata."),
        "delete_reminder":
            MessageLookupByLibrary.simpleMessage("Elimina promemoria?"),
        "delete_success": MessageLookupByLibrary.simpleMessage(
            "La nota è stata eliminata con successo."),
        "delete_this_folder":
            MessageLookupByLibrary.simpleMessage("Eliminare questa cartella?"),
        "delete_this_item":
            MessageLookupByLibrary.simpleMessage("Eliminare questo elemento?"),
        "deselect": MessageLookupByLibrary.simpleMessage("Deseleziona"),
        "detail_unlimited_ai_summaries": MessageLookupByLibrary.simpleMessage(
            "Note AI illimitate da registrazioni, audio, documenti e video YouTube"),
        "developing_quizzes":
            MessageLookupByLibrary.simpleMessage("Sviluppo quiz..."),
        "discard": MessageLookupByLibrary.simpleMessage("Annulla"),
        "discard_changes":
            MessageLookupByLibrary.simpleMessage("Scartare modifiche?"),
        "dissatisfied": MessageLookupByLibrary.simpleMessage(
            "Grazie per il feedback. Ci aiuta a migliorare. Lavoreremo per una migliore esperienza!"),
        "doc": MessageLookupByLibrary.simpleMessage("Documento"),
        "document": MessageLookupByLibrary.simpleMessage(
            "Carica documento (prossimamente)"),
        "document_available": MessageLookupByLibrary.simpleMessage(
            "Il documento sarà disponibile dopo la creazione della nota con successo!"),
        "document_exceed_limit": MessageLookupByLibrary.simpleMessage(
            "File oltre 20MB. Scegli un file più piccolo."),
        "document_limit": MessageLookupByLibrary.simpleMessage(
            "Limite caricamento documento"),
        "document_limit_message": MessageLookupByLibrary.simpleMessage(
            "Utenti gratuiti: 1 documento al giorno."),
        "document_note": MessageLookupByLibrary.simpleMessage("Nota documento"),
        "document_tab": MessageLookupByLibrary.simpleMessage("Documento"),
        "document_to_ai_note":
            MessageLookupByLibrary.simpleMessage("Documento in note AI"),
        "document_type": MessageLookupByLibrary.simpleMessage(
            "Tipi supportati: .pdf, .doc, .docx, .txt, .md"),
        "document_upload_note":
            MessageLookupByLibrary.simpleMessage("Caricamento documento"),
        "document_webview_loading_message":
            MessageLookupByLibrary.simpleMessage(
                "Caricamento contenuto documento..."),
        "done_button_label": MessageLookupByLibrary.simpleMessage("Fatto"),
        "donotallow": MessageLookupByLibrary.simpleMessage("Non consentire"),
        "double_the_benefits":
            MessageLookupByLibrary.simpleMessage("Raddoppia i vantaggi!"),
        "download_audio_file":
            MessageLookupByLibrary.simpleMessage("Scarica file audio"),
        "download_sucess":
            MessageLookupByLibrary.simpleMessage("Download riuscito"),
        "duration": MessageLookupByLibrary.simpleMessage("Durata"),
        "each_ai_note_generation_uses_1_credit":
            MessageLookupByLibrary.simpleMessage(
                "Ogni generazione nota AI usa 1 credito"),
        "each_referral_earns":
            MessageLookupByLibrary.simpleMessage("Ogni referral guadagna"),
        "early_access": MessageLookupByLibrary.simpleMessage(
            "Accesso anticipato a\nnuove funzioni"),
        "early_supporters_exclusive_offer":
            MessageLookupByLibrary.simpleMessage(
                "Offerta esclusiva per i sostenitori iniziali"),
        "easily_import_shared_note_link": MessageLookupByLibrary.simpleMessage(
            "Importa facilmente link di note condivise dagli amici"),
        "easy": MessageLookupByLibrary.simpleMessage("Facile"),
        "edit": MessageLookupByLibrary.simpleMessage("Modifica"),
        "edit_folder":
            MessageLookupByLibrary.simpleMessage("Modifica cartella"),
        "edit_folder_name":
            MessageLookupByLibrary.simpleMessage("Inserisci nome cartella"),
        "edit_name": MessageLookupByLibrary.simpleMessage("Modifica nome"),
        "edit_note": MessageLookupByLibrary.simpleMessage("Modifica nota"),
        "edit_notes": MessageLookupByLibrary.simpleMessage("Modifica nota"),
        "edit_reminder":
            MessageLookupByLibrary.simpleMessage("Modifica promemoria"),
        "edit_transcript":
            MessageLookupByLibrary.simpleMessage("Modifica trascrizione"),
        "edit_transcript_json_fail": MessageLookupByLibrary.simpleMessage(
            "Errore modifica trascrizione. Riprova."),
        "edit_transcript_json_success": MessageLookupByLibrary.simpleMessage(
            "Trascrizione con timestamp modificata con successo"),
        "email_invalid":
            MessageLookupByLibrary.simpleMessage("Email non valida."),
        "email_sent":
            MessageLookupByLibrary.simpleMessage("Controlla la posta"),
        "email_sent_success": MessageLookupByLibrary.simpleMessage(
            "Ti abbiamo inviato un link magico per accedere. Clicca il link nella tua email per continuare."),
        "enable_free":
            MessageLookupByLibrary.simpleMessage("Attiva prova gratuita"),
        "enables_swap": MessageLookupByLibrary.simpleMessage(
            "Consente il riordinamento delle immagini tramite selezione e scambio"),
        "english": MessageLookupByLibrary.simpleMessage("Inglese"),
        "enter_card_count": MessageLookupByLibrary.simpleMessage(
            "Inserisci il numero di carte"),
        "enter_email": MessageLookupByLibrary.simpleMessage(
            "Inserisci il tuo indirizzo email..."),
        "enter_feedback":
            MessageLookupByLibrary.simpleMessage("Inserisci feedback"),
        "enter_folder_name":
            MessageLookupByLibrary.simpleMessage("Inserisci nome cartella"),
        "enter_new_name":
            MessageLookupByLibrary.simpleMessage("Inserisci un nuovo nome"),
        "enter_quiz_count":
            MessageLookupByLibrary.simpleMessage("Inserisci il numero di quiz"),
        "enter_referral_code":
            MessageLookupByLibrary.simpleMessage("Inserisci codice referral"),
        "enter_slide_count": MessageLookupByLibrary.simpleMessage(
            "Inserisci il numero di diapositive"),
        "enter_title": MessageLookupByLibrary.simpleMessage("Inserisci titolo"),
        "enter_valid_email":
            MessageLookupByLibrary.simpleMessage("Inserisci un’email valida"),
        "error": MessageLookupByLibrary.simpleMessage("Errore"),
        "error_connection": MessageLookupByLibrary.simpleMessage(
            "Errore di connessione.\nRiprova"),
        "error_convert_image":
            MessageLookupByLibrary.simpleMessage("Errore conversione immagine"),
        "error_logging_in":
            MessageLookupByLibrary.simpleMessage("Connettiti a internet"),
        "esc": MessageLookupByLibrary.simpleMessage("Esc"),
        "essential": MessageLookupByLibrary.simpleMessage("Essential"),
        "essential_lifetime":
            MessageLookupByLibrary.simpleMessage("Essential a vita"),
        "essential_lifetime_access":
            MessageLookupByLibrary.simpleMessage("Accesso Essential a vita"),
        "evening_content":
            MessageLookupByLibrary.simpleMessage("Rifletti, cattura, cresci"),
        "evening_content_2": MessageLookupByLibrary.simpleMessage(
            "Intuizioni di oggi preservate"),
        "evening_content_3": MessageLookupByLibrary.simpleMessage(
            "Il domani inizia con le note di oggi"),
        "evening_content_4": MessageLookupByLibrary.simpleMessage(
            "Pensieri organizzati, mente serena"),
        "evening_content_5":
            MessageLookupByLibrary.simpleMessage("Salva ora, ringraziati dopo"),
        "evening_content_6":
            MessageLookupByLibrary.simpleMessage("Progressi preservati"),
        "every_note_you_take":
            MessageLookupByLibrary.simpleMessage("in diapositive"),
        "experience": MessageLookupByLibrary.simpleMessage("esperienza"),
        "export": MessageLookupByLibrary.simpleMessage("Esporta"),
        "export_as": MessageLookupByLibrary.simpleMessage("Esporta come"),
        "export_audio":
            MessageLookupByLibrary.simpleMessage("Esporta File Audio"),
        "export_failed": MessageLookupByLibrary.simpleMessage(
            "Esportazione non riuscita. Riprova più tardi."),
        "export_flashcard":
            MessageLookupByLibrary.simpleMessage("Esporta flashcard"),
        "export_mind_map":
            MessageLookupByLibrary.simpleMessage("Esporta mappa mentale come"),
        "export_pdf": MessageLookupByLibrary.simpleMessage("Esporta riassunto"),
        "export_quiz": MessageLookupByLibrary.simpleMessage("Esporta quiz"),
        "export_to_pdf_share_notes": MessageLookupByLibrary.simpleMessage(
            "Esporta in PDF e condividi note"),
        "export_transcript":
            MessageLookupByLibrary.simpleMessage("Esporta trascrizione"),
        "extracting_text_from_document": MessageLookupByLibrary.simpleMessage(
            "Estrazione testo da documento"),
        "fail": MessageLookupByLibrary.simpleMessage("Fallimento"),
        "fail_create_pdf":
            MessageLookupByLibrary.simpleMessage("Errore creazione PDF"),
        "fail_to_load_document": MessageLookupByLibrary.simpleMessage(
            "Errore caricamento documento!"),
        "fail_to_load_video":
            MessageLookupByLibrary.simpleMessage("Errore caricamento video"),
        "failed_get_anonymous_user": MessageLookupByLibrary.simpleMessage(
            "Errore recupero utente anonimo"),
        "failed_to_delete_recording": MessageLookupByLibrary.simpleMessage(
            "Errore nell\'eliminazione dell\'inregistrazione"),
        "failed_to_load_slideshow": MessageLookupByLibrary.simpleMessage(
            "Impossibile caricare il set di diapositive dal sistema. Riprova per una migliore esperienza."),
        "failed_to_save_file":
            MessageLookupByLibrary.simpleMessage("Impossibile salvare il file"),
        "feedback": MessageLookupByLibrary.simpleMessage("Feedback"),
        "file_import":
            MessageLookupByLibrary.simpleMessage("Importazione file"),
        "file_save_success":
            MessageLookupByLibrary.simpleMessage("File salvato con successo"),
        "file_size_err": MessageLookupByLibrary.simpleMessage(
            "Dimensione file oltre il limite. Carica un file più piccolo."),
        "filter": MessageLookupByLibrary.simpleMessage("Filtro"),
        "filter_and_sort":
            MessageLookupByLibrary.simpleMessage("Filtra e ordina"),
        "finalizing": MessageLookupByLibrary.simpleMessage("Finalizzazione..."),
        "find_and_replace":
            MessageLookupByLibrary.simpleMessage("Trova e sostituisci"),
        "flash_card_gen_success":
            MessageLookupByLibrary.simpleMessage("Flashcard generate"),
        "flash_card_iap": MessageLookupByLibrary.simpleMessage("Set flashcard"),
        "flashcard": MessageLookupByLibrary.simpleMessage("Flashcard"),
        "flashcard_set_not_found": MessageLookupByLibrary.simpleMessage(
            "Set di flashcard non trovato"),
        "flashcard_sets":
            MessageLookupByLibrary.simpleMessage("Set di flashcard"),
        "flashcards": MessageLookupByLibrary.simpleMessage("Flashcard"),
        "flashcards_for": MessageLookupByLibrary.simpleMessage("Flashcard per"),
        "focus_on":
            MessageLookupByLibrary.simpleMessage("Focus sul necessario"),
        "folder": MessageLookupByLibrary.simpleMessage("Cartelle"),
        "follow_steps_to_get_rewarded": MessageLookupByLibrary.simpleMessage(
            "Segui i passi per essere premiato"),
        "for_unlimited_experiences":
            MessageLookupByLibrary.simpleMessage("per esperienze illimitate."),
        "free": MessageLookupByLibrary.simpleMessage("Gratis"),
        "free_30_minutes":
            MessageLookupByLibrary.simpleMessage("Gratis: 30 minuti per file"),
        "free_messages":
            MessageLookupByLibrary.simpleMessage("messaggi gratuiti"),
        "free_recording_limit": MessageLookupByLibrary.simpleMessage(
            "Limite registrazione gratuita"),
        "free_recording_limit_details": MessageLookupByLibrary.simpleMessage(
            "Ti restano %s minuti di trascrizioni gratuite questa settimana."),
        "free_trial": MessageLookupByLibrary.simpleMessage("Prova gratuita"),
        "free_updates": MessageLookupByLibrary.simpleMessage(
            "Aggiornamenti gratuiti a vita"),
        "free_usage":
            MessageLookupByLibrary.simpleMessage("Uso gratuito - Settimanale"),
        "free_user_audio": MessageLookupByLibrary.simpleMessage(
            "Utenti gratuiti: trascrizione e riassunto fino a 30 minuti"),
        "free_user_can": MessageLookupByLibrary.simpleMessage(
            "Utenti gratuiti: 1 video YouTube (max 30 min) al giorno."),
        "friendly": MessageLookupByLibrary.simpleMessage("Amichevole"),
        "friendly_description":
            MessageLookupByLibrary.simpleMessage("Conversazionale con emoji"),
        "front_content": MessageLookupByLibrary.simpleMessage("Hai ottenuto "),
        "future_features": MessageLookupByLibrary.simpleMessage(
            "Funzioni future potrebbero avere limiti"),
        "gen_ai": MessageLookupByLibrary.simpleMessage("Generazione IA..."),
        "gen_ai_voice":
            MessageLookupByLibrary.simpleMessage("Generazione della voce AI"),
        "gen_quiz_bgr": MessageLookupByLibrary.simpleMessage(
            "Generazione dello sfondo del quiz"),
        "generate_audio": MessageLookupByLibrary.simpleMessage("Genera audio"),
        "generate_content": MessageLookupByLibrary.simpleMessage(
            "Crea riassunti intelligenti di contenuti YouTube"),
        "generate_note_fail": MessageLookupByLibrary.simpleMessage(
            "Errore nella creazione delle note AI!"),
        "generate_shorts_step_1": MessageLookupByLibrary.simpleMessage(
            "Creazione della tua storia..."),
        "generate_shorts_step_2":
            MessageLookupByLibrary.simpleMessage("Aggiunta voce perfetta..."),
        "generate_shorts_step_3": MessageLookupByLibrary.simpleMessage(
            "Rendilo fantastico! Video da condividere #NoteXAI"),
        "generate_shorts_study_guides":
            MessageLookupByLibrary.simpleMessage("Genera brevi e guide studio"),
        "generate_transcript_notes": MessageLookupByLibrary.simpleMessage(
            "Generiamo trascrizione, note e guida studio"),
        "generate_video": MessageLookupByLibrary.simpleMessage("Genera video"),
        "generating_ai_note":
            MessageLookupByLibrary.simpleMessage("Generazione note AI"),
        "generating_summary":
            MessageLookupByLibrary.simpleMessage("Generazione riassunto AI..."),
        "get_fail": MessageLookupByLibrary.simpleMessage(
            "Errore recupero quiz/flashcard/mappa. Riprova!"),
        "get_more_done":
            MessageLookupByLibrary.simpleMessage("Fai di più, resta"),
        "get_more_done_stay_on_track":
            MessageLookupByLibrary.simpleMessage("Fai di più, resta in pista"),
        "get_now": MessageLookupByLibrary.simpleMessage("PRENDI ORA"),
        "get_offer_now":
            MessageLookupByLibrary.simpleMessage("Ottieni l\'offerta ora"),
        "get_start": MessageLookupByLibrary.simpleMessage("Inizia"),
        "go_back": MessageLookupByLibrary.simpleMessage("Torna indietro"),
        "go_email": MessageLookupByLibrary.simpleMessage("Vai a Email"),
        "go_pro": MessageLookupByLibrary.simpleMessage("Passa a PRO"),
        "go_unlimited":
            MessageLookupByLibrary.simpleMessage("Passa a illimitato!"),
        "good_afternoon":
            MessageLookupByLibrary.simpleMessage("Buon pomeriggio!"),
        "good_evening": MessageLookupByLibrary.simpleMessage("Buonasera!"),
        "good_morning": MessageLookupByLibrary.simpleMessage("Buongiorno!"),
        "got_it": MessageLookupByLibrary.simpleMessage("Capito!"),
        "hard": MessageLookupByLibrary.simpleMessage("Difficile"),
        "hello_welcome": MessageLookupByLibrary.simpleMessage("Bentornato 👋"),
        "help_legal": MessageLookupByLibrary.simpleMessage("Aiuto e legale"),
        "help_us_grow":
            MessageLookupByLibrary.simpleMessage("Aiutaci a crescere!"),
        "hi": MessageLookupByLibrary.simpleMessage("Ciao"),
        "hope_enjoy_app": MessageLookupByLibrary.simpleMessage(
            "Speriamo ti piaccia l’app, grazie per il supporto!"),
        "hours": MessageLookupByLibrary.simpleMessage("Ore"),
        "how_will_you_use_notex":
            MessageLookupByLibrary.simpleMessage("Come userai NoteX?"),
        "http_failed": MessageLookupByLibrary.simpleMessage(
            "Richiesta HTTP non riuscita. Riprova più tardi."),
        "idea1": MessageLookupByLibrary.simpleMessage(
            "Accedi con Google o Apple se non l’hai fatto"),
        "idea2": MessageLookupByLibrary.simpleMessage(
            "Descrivi brevemente il problema"),
        "idea3": MessageLookupByLibrary.simpleMessage(
            "Includi dettagli (dispositivo, versione OS)"),
        "idea4": MessageLookupByLibrary.simpleMessage(
            "Indica quando è iniziato il problema"),
        "idea5": MessageLookupByLibrary.simpleMessage("Scrivici a"),
        "image": MessageLookupByLibrary.simpleMessage("Immagine"),
        "image_jpeg": MessageLookupByLibrary.simpleMessage("Immagine (.jpeg)"),
        "image_png": MessageLookupByLibrary.simpleMessage("Immagine (.png)"),
        "image_quality_too_low": MessageLookupByLibrary.simpleMessage(
            "Qualità immagine bassa. Usa un’immagine migliore!"),
        "image_too_large": MessageLookupByLibrary.simpleMessage(
            "Immagine troppo grande. Carica file sotto 10MB."),
        "images_have_been_uploaded": m1,
        "import_note_links":
            MessageLookupByLibrary.simpleMessage("Importa link delle note"),
        "import_notes":
            MessageLookupByLibrary.simpleMessage("Importa Note Condivise"),
        "improve_responses": MessageLookupByLibrary.simpleMessage(
            "Le tue risposte ci aiutano a migliorare"),
        "initializing_camera": MessageLookupByLibrary.simpleMessage(
            "Inizializzazione della fotocamera..."),
        "insight_instantly": MessageLookupByLibrary.simpleMessage(
            "Intuizioni immediate da ore di contenuti"),
        "insights_instantly":
            MessageLookupByLibrary.simpleMessage("intuizioni immediate"),
        "instant_answers_from_your":
            MessageLookupByLibrary.simpleMessage("Risposte immediate dai tuoi"),
        "instant_answers_from_your_meeting_data":
            MessageLookupByLibrary.simpleMessage(
                "Risposte immediate dai dati delle riunioni"),
        "instant_answers_meeting": MessageLookupByLibrary.simpleMessage(
            "Risposte immediate dalle tue riunioni"),
        "instantly": MessageLookupByLibrary.simpleMessage("subito"),
        "interactive_ai_flashcards": MessageLookupByLibrary.simpleMessage(
            "Mappa mentale AI interattiva"),
        "interactive_flash":
            MessageLookupByLibrary.simpleMessage("Flashcard interattive"),
        "interactive_flashcards": MessageLookupByLibrary.simpleMessage(
            "Flashcard e mappe mentali illimitate"),
        "interactive_flashcards_quiz": MessageLookupByLibrary.simpleMessage(
            "Flashcard e quiz interattivi"),
        "introduce_guidance": MessageLookupByLibrary.simpleMessage(
            "Grazie per averci contattato. Per aiutarci a risolvere il problema, segui questi passi:"),
        "introduce_guidance2": MessageLookupByLibrary.simpleMessage(
            "Capiamo quanto sia frustrante avere problemi, specie con note importanti. Il nostro team ti aiuta entro 12 ore."),
        "inv_audio": MessageLookupByLibrary.simpleMessage(
            "File audio non valido. Carica un formato supportato."),
        "inv_yt_url": MessageLookupByLibrary.simpleMessage(
            "URL YouTube non valido. Fornisci un link valido."),
        "invalid_code":
            MessageLookupByLibrary.simpleMessage("Codice non valido. Riprova."),
        "invalid_file_type": MessageLookupByLibrary.simpleMessage(
            "Formato file errato. Ricarica."),
        "invalid_token":
            MessageLookupByLibrary.simpleMessage("Token non valido"),
        "invite_friends": MessageLookupByLibrary.simpleMessage(
            "Invita amici a unirsi - sbloccate entrambi extra"),
        "join_discord":
            MessageLookupByLibrary.simpleMessage("Unisciti a Discord"),
        "join_noteX_ai_lets_level_up_together":
            MessageLookupByLibrary.simpleMessage(
                "Unisciti a NoteX AI e cresciamo insieme!"),
        "language": MessageLookupByLibrary.simpleMessage("Lingua"),
        "language_tip": MessageLookupByLibrary.simpleMessage(
            "Scegli la lingua principale dell’audio per risultati migliori"),
        "language_tip_1": MessageLookupByLibrary.simpleMessage(
            "Seleziona la lingua principale per risultati ottimali"),
        "language_tip_2": MessageLookupByLibrary.simpleMessage(
            "Per conversazioni miste, scegli multilingua"),
        "language_tip_3": MessageLookupByLibrary.simpleMessage(
            "Le chiamate mettono in pausa la registrazione. Torna all’app per riprendere"),
        "latest_ai_models":
            MessageLookupByLibrary.simpleMessage("Ultimi modelli AI"),
        "learn_faster_through":
            MessageLookupByLibrary.simpleMessage("Impara più veloce con"),
        "learn_faster_through_active_recall":
            MessageLookupByLibrary.simpleMessage(
                "Impara più veloce con il richiamo attivo"),
        "learn_smart":
            MessageLookupByLibrary.simpleMessage("Impara in modo intelligente"),
        "learn_unlimited":
            MessageLookupByLibrary.simpleMessage("Impara senza limiti!"),
        "lecture_notes_study_materials": MessageLookupByLibrary.simpleMessage(
            "Note lezioni e materiali studio"),
        "let_ai_handle":
            MessageLookupByLibrary.simpleMessage("Lascia all’AI i dettagli"),
        "let_note_ai": MessageLookupByLibrary.simpleMessage(
            "Lascia che NoteX AI trasformi il"),
        "let_start": MessageLookupByLibrary.simpleMessage("Iniziamo"),
        "lets_create_your_first_ai_note": MessageLookupByLibrary.simpleMessage(
            "Creiamo la tua prima nota AI!"),
        "lifetime": MessageLookupByLibrary.simpleMessage("Piano a Vita"),
        "lifetime_pro_access_level_up_together":
            MessageLookupByLibrary.simpleMessage(
                "Accesso Pro a vita! Sali di livello insieme ✨"),
        "lifetime_setting": MessageLookupByLibrary.simpleMessage("a vita"),
        "lifetime_spots_remaining":
            MessageLookupByLibrary.simpleMessage("Posti a vita rimanenti"),
        "light": MessageLookupByLibrary.simpleMessage("Chiaro"),
        "limited_notes":
            MessageLookupByLibrary.simpleMessage("Note limitate al giorno"),
        "limited_offer":
            MessageLookupByLibrary.simpleMessage("Offerta limitata"),
        "limited_time": MessageLookupByLibrary.simpleMessage("Tempo limitato"),
        "link": MessageLookupByLibrary.simpleMessage("Link"),
        "link_error":
            MessageLookupByLibrary.simpleMessage("Errore con il link"),
        "link_expired":
            MessageLookupByLibrary.simpleMessage("Il link email è scaduto."),
        "link_invalid": MessageLookupByLibrary.simpleMessage(
            "Link non valido, scaduto o già usato. Richiedine uno nuovo."),
        "loading": MessageLookupByLibrary.simpleMessage("Caricamento"),
        "loading_content":
            MessageLookupByLibrary.simpleMessage("Caricamento contenuti..."),
        "local_recording":
            MessageLookupByLibrary.simpleMessage("Registrazione smart"),
        "login_failed":
            MessageLookupByLibrary.simpleMessage("Accesso fallito."),
        "login_info_1": MessageLookupByLibrary.simpleMessage(
            "Accedi alle note da qualsiasi dispositivo"),
        "login_info_2":
            MessageLookupByLibrary.simpleMessage("Sicurezza avanzata con AWS"),
        "login_info_3":
            MessageLookupByLibrary.simpleMessage("I tuoi dati restano privati"),
        "login_info_4": MessageLookupByLibrary.simpleMessage(
            "Accedi sul web su notexapp.com"),
        "login_success":
            MessageLookupByLibrary.simpleMessage("Accesso riuscito!"),
        "login_title": MessageLookupByLibrary.simpleMessage(
            "Massima produttività ovunque!"),
        "login_title_2": MessageLookupByLibrary.simpleMessage("Ecco NoteX 2.0"),
        "login_unsuccessful": MessageLookupByLibrary.simpleMessage(
            "Accesso fallito. Riprova o usa un altro metodo."),
        "logout": MessageLookupByLibrary.simpleMessage("Esci"),
        "logout_detail":
            MessageLookupByLibrary.simpleMessage("Sei sicuro di voler uscire?"),
        "logout_question_mark": MessageLookupByLibrary.simpleMessage("Uscire?"),
        "loved_by": MessageLookupByLibrary.simpleMessage("Amato da "),
        "make_the_interface_feel_more_like_you":
            MessageLookupByLibrary.simpleMessage(
                "Rendi l\'interfaccia più simile a te — con preferenze di tema, carattere e layout a portata di mano."),
        "making_amazing": MessageLookupByLibrary.simpleMessage(
            "Lo rendiamo fantastico! Questo video quiz varrà la pena di essere condiviso #NoteXAI"),
        "manage_recordings":
            MessageLookupByLibrary.simpleMessage("Gestire le registrazioni"),
        "map_all_together": MessageLookupByLibrary.simpleMessage(
            "Mappatura di tutti gli elementi insieme"),
        "markdown_md": MessageLookupByLibrary.simpleMessage("Markdown (.md)"),
        "max_30_cards_per_set":
            MessageLookupByLibrary.simpleMessage("Massimo 30 carte per set"),
        "max_30_quiz_sets": MessageLookupByLibrary.simpleMessage(
            "Massimo di 30 domande per set"),
        "max_3_flashcard_sets":
            MessageLookupByLibrary.simpleMessage("Massimo 3 set di flashcard"),
        "max_3_quiz_sets":
            MessageLookupByLibrary.simpleMessage("Massimo 3 set di quiz"),
        "max_60_min_per_file":
            MessageLookupByLibrary.simpleMessage("* max 60 min per file"),
        "max_ai": MessageLookupByLibrary.simpleMessage("Trascrizione AI max:"),
        "maybe_later": MessageLookupByLibrary.simpleMessage("Forse dopo"),
        "medium": MessageLookupByLibrary.simpleMessage("Medio"),
        "meeting_data": MessageLookupByLibrary.simpleMessage("dati riunioni"),
        "migrating_your_notes":
            MessageLookupByLibrary.simpleMessage("Migrazione note..."),
        "migration_complete":
            MessageLookupByLibrary.simpleMessage("Migrazione completata!"),
        "mind_map": MessageLookupByLibrary.simpleMessage("Mappa mentale"),
        "mind_map_gen_success":
            MessageLookupByLibrary.simpleMessage("Mappa mentale generata"),
        "mind_map_iap": MessageLookupByLibrary.simpleMessage("Mappa mentale"),
        "mind_map_study": MessageLookupByLibrary.simpleMessage(
            "Mappa mentale,\nGuide studio"),
        "minute_60_per_file": MessageLookupByLibrary.simpleMessage(
            "Essential: 60 minuti per file"),
        "minute_free": MessageLookupByLibrary.simpleMessage(
            "Hai usato i 30 minuti gratuiti di trascrizione questa settimana. Passa a Pro o attendi il reset."),
        "minutes": MessageLookupByLibrary.simpleMessage("Minuti"),
        "minutes_free_left":
            MessageLookupByLibrary.simpleMessage("Minuti gratuiti rimanenti"),
        "minutes_remaining":
            MessageLookupByLibrary.simpleMessage("minuti rimanenti"),
        "mixed": MessageLookupByLibrary.simpleMessage("Misto"),
        "month": MessageLookupByLibrary.simpleMessage("mese"),
        "monthly": MessageLookupByLibrary.simpleMessage("Mensile"),
        "more_summarize": MessageLookupByLibrary.simpleMessage(
            "Riassumi riunioni, podcast, tutorial e altro"),
        "morning_content": MessageLookupByLibrary.simpleMessage(
            "Cattura la brillantezza di oggi"),
        "morning_content_2": MessageLookupByLibrary.simpleMessage(
            "Mente chiara, percorso chiaro"),
        "morning_content_3": MessageLookupByLibrary.simpleMessage(
            "Le note di oggi plasmano il domani"),
        "morning_content_4": MessageLookupByLibrary.simpleMessage(
            "Primo pensiero, miglior pensiero"),
        "morning_content_5":
            MessageLookupByLibrary.simpleMessage("Inizia con chiarezza"),
        "morning_content_6": MessageLookupByLibrary.simpleMessage(
            "Idee che vale la pena conservare"),
        "most_popular": MessageLookupByLibrary.simpleMessage("Più popolare"),
        "multi_language": MessageLookupByLibrary.simpleMessage("Multilingua"),
        "multiple_people_detected": MessageLookupByLibrary.simpleMessage(
            "Più persone rilevate. Carica un’immagine con una sola persona!"),
        "multiply_knowledge_with_friends": MessageLookupByLibrary.simpleMessage(
            "Moltiplica la conoscenza con gli amici"),
        "my_notes": MessageLookupByLibrary.simpleMessage("Le mie note"),
        "name": MessageLookupByLibrary.simpleMessage("Nome"),
        "network_error": MessageLookupByLibrary.simpleMessage(
            "Errore rete. Controlla la connessione e riprova."),
        "neutral": MessageLookupByLibrary.simpleMessage("Neutro"),
        "neutral_description": MessageLookupByLibrary.simpleMessage(
            "Presentazione diretta e fattuale"),
        "new_new": MessageLookupByLibrary.simpleMessage("Nuovo"),
        "new_note": MessageLookupByLibrary.simpleMessage("Nuova nota"),
        "new_recording":
            MessageLookupByLibrary.simpleMessage("Nuova registrazione - "),
        "newest_first":
            MessageLookupByLibrary.simpleMessage("Prima i più recenti"),
        "next_bill_date": m2,
        "no": MessageLookupByLibrary.simpleMessage("No"),
        "no_generated": MessageLookupByLibrary.simpleMessage(
            "Nessun quiz/flashcard generato. Tocca per creare ora!"),
        "no_input": MessageLookupByLibrary.simpleMessage(
            "Nessun input fornito. Carica un file audio o inserisci un URL YouTube."),
        "no_internet": MessageLookupByLibrary.simpleMessage(
            "Nessuna connessione internet"),
        "no_internet_connection": MessageLookupByLibrary.simpleMessage(
            "Nessuna connessione internet!"),
        "no_notes_found": MessageLookupByLibrary.simpleMessage(
            "Nessuna nota con questo filtro.\nRipristina il filtro"),
        "no_notes_in_folder": MessageLookupByLibrary.simpleMessage(
            "Nessuna nota in questa cartella."),
        "no_payment_now":
            MessageLookupByLibrary.simpleMessage("✓ Nessun pagamento ora"),
        "no_person_detected": MessageLookupByLibrary.simpleMessage(
            "Nessuna persona rilevata. Carica un’immagine con una persona!"),
        "no_recording_credit": MessageLookupByLibrary.simpleMessage(
            "Crediti registrazione insufficienti. Aggiorna il piano."),
        "no_recordings":
            MessageLookupByLibrary.simpleMessage("Nessun registro"),
        "no_results_found":
            MessageLookupByLibrary.simpleMessage("Nessun risultato per"),
        "no_speech_detected":
            MessageLookupByLibrary.simpleMessage("Nessun discorso rilevato"),
        "no_summary": MessageLookupByLibrary.simpleMessage(
            "Nessun riassunto disponibile per questa nota."),
        "no_transcript": MessageLookupByLibrary.simpleMessage(
            "Nessuna trascrizione disponibile per questa nota."),
        "no_upload_credit": MessageLookupByLibrary.simpleMessage(
            "Crediti caricamento insufficienti. Aggiorna il piano."),
        "no_url_provided": MessageLookupByLibrary.simpleMessage(
            "Nessun URL di esportazione fornito."),
        "no_voice_available":
            MessageLookupByLibrary.simpleMessage("Nessuna voce disponibile"),
        "not_found_audio":
            MessageLookupByLibrary.simpleMessage("File audio non trovato"),
        "not_open_mail":
            MessageLookupByLibrary.simpleMessage("Impossibile aprire la mail!"),
        "not_open_web":
            MessageLookupByLibrary.simpleMessage("Impossibile aprire il web!"),
        "not_summarized_note": MessageLookupByLibrary.simpleMessage(
            "Riassunto mancante! Premi il pulsante per attivare l’AI 👇"),
        "note": MessageLookupByLibrary.simpleMessage("Nota"),
        "noteX": MessageLookupByLibrary.simpleMessage("NoteX"),
        "noteX_lifetime_essential":
            MessageLookupByLibrary.simpleMessage("NoteX Essential a vita"),
        "noteX_pro_lifetime":
            MessageLookupByLibrary.simpleMessage("NoteX Pro a vita"),
        "note_404": MessageLookupByLibrary.simpleMessage(
            "Nota non trovata. Controlla l’ID e riprova."),
        "note_not_ready": MessageLookupByLibrary.simpleMessage(
            "La nota non è pronta per l\'esportazione. Attendere il completamento dell\'elaborazione."),
        "note_reminders":
            MessageLookupByLibrary.simpleMessage("Promemoria note"),
        "note_sharing":
            MessageLookupByLibrary.simpleMessage("Condivisione note"),
        "note_tabs": MessageLookupByLibrary.simpleMessage("Schede della nota"),
        "note_taker":
            MessageLookupByLibrary.simpleMessage("#1 Assistente AI per note"),
        "notes": MessageLookupByLibrary.simpleMessage("note"),
        "notex_empty": MessageLookupByLibrary.simpleMessage("NoteX vuoto"),
        "notex_experience":
            MessageLookupByLibrary.simpleMessage("la tua esperienza con NoteX"),
        "nothing_restore":
            MessageLookupByLibrary.simpleMessage("Niente da ripristinare"),
        "noti_default_description": MessageLookupByLibrary.simpleMessage(
            "Preparati e inizia a registrare! 🚀"),
        "noti_default_title":
            MessageLookupByLibrary.simpleMessage("È ora di registrare"),
        "noti_req_description": MessageLookupByLibrary.simpleMessage(
            "Le notifiche possono includere avvisi, suoni e badge. Configurabili in Impostazioni."),
        "noti_req_title": MessageLookupByLibrary.simpleMessage(
            "‘NoteX’ vuole inviarti notifiche"),
        "notifications": MessageLookupByLibrary.simpleMessage("Notifiche"),
        "notifications_note_created":
            MessageLookupByLibrary.simpleMessage("Note create con successo"),
        "notifications_note_ready": MessageLookupByLibrary.simpleMessage(
            "Notifica quando le note sono pronte"),
        "nova_ai_assistant_mind_mapping": MessageLookupByLibrary.simpleMessage(
            "Assistente Nova AI e mappe mentali"),
        "nova_ai_chat": MessageLookupByLibrary.simpleMessage("Chat Nova AI"),
        "nova_chat": MessageLookupByLibrary.simpleMessage("Chat Nova"),
        "of_index": MessageLookupByLibrary.simpleMessage("di"),
        "of_user": MessageLookupByLibrary.simpleMessage(" utenti"),
        "offer_expires":
            MessageLookupByLibrary.simpleMessage("Offerta limitata nel tempo"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldest_first":
            MessageLookupByLibrary.simpleMessage("Prima i più vecchi"),
        "on_track": MessageLookupByLibrary.simpleMessage("in pista"),
        "on_your_android": MessageLookupByLibrary.simpleMessage(
            "Sul tuo dispositivo Android, apri Google Play Store"),
        "onboarding_generate_audio_video_content":
            MessageLookupByLibrary.simpleMessage("Trasforma le note in"),
        "onboarding_generate_audio_video_full_content":
            MessageLookupByLibrary.simpleMessage(
                "Trasforma le note in contenuti coinvolgenti"),
        "onboarding_generate_audio_video_sub_content":
            MessageLookupByLibrary.simpleMessage("contenuti coinvolgenti"),
        "onboarding_generate_audio_video_title":
            MessageLookupByLibrary.simpleMessage("Genera Audio e Video"),
        "once_in_a_lifetime_offer":
            MessageLookupByLibrary.simpleMessage("Offerta unica nella vita"),
        "one_per_day": MessageLookupByLibrary.simpleMessage("1 al giorno"),
        "one_time_payment":
            MessageLookupByLibrary.simpleMessage("Pagamento unico"),
        "only": MessageLookupByLibrary.simpleMessage("Solo"),
        "only_today": MessageLookupByLibrary.simpleMessage("Solo oggi"),
        "oops_something_went_wrong": MessageLookupByLibrary.simpleMessage(
            "Ops!\nQualcosa è andato storto"),
        "open_now": MessageLookupByLibrary.simpleMessage("Apri ora"),
        "open_youtube": MessageLookupByLibrary.simpleMessage("Apri YouTube"),
        "opportunities": MessageLookupByLibrary.simpleMessage("opportunità"),
        "or": MessageLookupByLibrary.simpleMessage("o"),
        "or_upper": MessageLookupByLibrary.simpleMessage("O"),
        "organize_assign_action_items": MessageLookupByLibrary.simpleMessage(
            "Organizza e assegna attività"),
        "organize_assign_items": MessageLookupByLibrary.simpleMessage(
            "Organizza e assegna attività"),
        "others": MessageLookupByLibrary.simpleMessage("Altro"),
        "output_language":
            MessageLookupByLibrary.simpleMessage("Lingua output"),
        "pace": MessageLookupByLibrary.simpleMessage("ritmo"),
        "paste": MessageLookupByLibrary.simpleMessage("Incolla"),
        "paste_url_here":
            MessageLookupByLibrary.simpleMessage("Incolla URL qui"),
        "paste_youtube_link":
            MessageLookupByLibrary.simpleMessage("Incolla link YouTube"),
        "payment_required": MessageLookupByLibrary.simpleMessage(
            "Hai esaurito i crediti gratuiti"),
        "payment_successfully":
            MessageLookupByLibrary.simpleMessage("Pagamento riuscito"),
        "pdf_export": MessageLookupByLibrary.simpleMessage("Esporta PDF"),
        "pdf_pdf": MessageLookupByLibrary.simpleMessage("PDF (.pdf)"),
        "per_year": MessageLookupByLibrary.simpleMessage(" per anno"),
        "period": MessageLookupByLibrary.simpleMessage(" di %s minuti"),
        "personalized_learning":
            MessageLookupByLibrary.simpleMessage("Esercitati per"),
        "personalized_learning_at":
            MessageLookupByLibrary.simpleMessage("Esercitati per un A+"),
        "photos": MessageLookupByLibrary.simpleMessage("Foto"),
        "pick_specific_language": MessageLookupByLibrary.simpleMessage(
            "Scegli una lingua specifica nell’audio per precisione"),
        "plan": MessageLookupByLibrary.simpleMessage("Piano"),
        "please_select_a_language": MessageLookupByLibrary.simpleMessage(
            "Seleziona una lingua per una trascrizione accurata!"),
        "please_select_a_youtube_language": MessageLookupByLibrary.simpleMessage(
            "Seleziona una lingua per il riepilogo. Questa è la lingua che vedrai nell\'output del riepilogo"),
        "please_try_again": MessageLookupByLibrary.simpleMessage("Riprova"),
        "please_wait": MessageLookupByLibrary.simpleMessage("Attendi"),
        "podcast": MessageLookupByLibrary.simpleMessage("Podcast"),
        "podcast_name":
            MessageLookupByLibrary.simpleMessage("Nome del podcast"),
        "policy": MessageLookupByLibrary.simpleMessage("Policy"),
        "premium_features": MessageLookupByLibrary.simpleMessage(
            "Prova le funzioni premium per la differenza"),
        "preparing_video":
            MessageLookupByLibrary.simpleMessage("Preparazione video..."),
        "press_back_again_to_exit": MessageLookupByLibrary.simpleMessage(
            "Premi di nuovo Indietro per uscire!"),
        "preview_only": MessageLookupByLibrary.simpleMessage(
            "Solo anteprima. Lo sfondo sarà generato dall\'IA in base al contenuto"),
        "priority_processing":
            MessageLookupByLibrary.simpleMessage("Elaborazione prioritaria"),
        "privacy_policy":
            MessageLookupByLibrary.simpleMessage("Informativa privacy"),
        "private": MessageLookupByLibrary.simpleMessage("Privato"),
        "pro": MessageLookupByLibrary.simpleMessage("Piano PRO"),
        "proLite": MessageLookupByLibrary.simpleMessage("Pro Lite"),
        "pro_01": MessageLookupByLibrary.simpleMessage("Pro"),
        "pro_6_hours":
            MessageLookupByLibrary.simpleMessage("Pro: 6 ore per file"),
        "pro_access_life_time":
            MessageLookupByLibrary.simpleMessage("ACCESSO PRO A VITA"),
        "pro_lifetime": MessageLookupByLibrary.simpleMessage("PRO a vita"),
        "process_your_document":
            MessageLookupByLibrary.simpleMessage("Elaborazione documento..."),
        "process_your_text":
            MessageLookupByLibrary.simpleMessage("Elaborazione testo..."),
        "processing_content":
            MessageLookupByLibrary.simpleMessage("Elaborazione contenuti..."),
        "processing_file":
            MessageLookupByLibrary.simpleMessage("Elaborazione file..."),
        "processing_image":
            MessageLookupByLibrary.simpleMessage("Elaborazione immagine..."),
        "processing_note_audio_file":
            MessageLookupByLibrary.simpleMessage("Elaborazione audio..."),
        "processing_note_recording": MessageLookupByLibrary.simpleMessage(
            "Elaborazione registrazione..."),
        "processing_note_youtube": MessageLookupByLibrary.simpleMessage(
            "Elaborazione video YouTube..."),
        "processing_web_link":
            MessageLookupByLibrary.simpleMessage("Elaborazione link web"),
        "producing_flashcards":
            MessageLookupByLibrary.simpleMessage("Produzione flashcard AI..."),
        "professional": MessageLookupByLibrary.simpleMessage("Uso aziendale"),
        "professional_description": MessageLookupByLibrary.simpleMessage(
            "Linguaggio formale adatto al contesto lavorativo"),
        "professional_style":
            MessageLookupByLibrary.simpleMessage("Professionale"),
        "public": MessageLookupByLibrary.simpleMessage("Pubblico"),
        "purchase_fail":
            MessageLookupByLibrary.simpleMessage("Acquisto fallito! Riprova!"),
        "purchase_init_fail": MessageLookupByLibrary.simpleMessage(
            "Ops! Impossibile avviare l’acquisto. Riprova."),
        "purpose_using":
            MessageLookupByLibrary.simpleMessage("scopo nell’usare "),
        "quarter": MessageLookupByLibrary.simpleMessage("trimestre"),
        "quarterly": MessageLookupByLibrary.simpleMessage("Trimestrale"),
        "question": MessageLookupByLibrary.simpleMessage("Domanda"),
        "quick_access": MessageLookupByLibrary.simpleMessage("Accesso rapido"),
        "quick_import":
            MessageLookupByLibrary.simpleMessage("O importa velocemente da"),
        "quickly": MessageLookupByLibrary.simpleMessage("concetti"),
        "quiz": MessageLookupByLibrary.simpleMessage("Quiz"),
        "quiz_count": MessageLookupByLibrary.simpleMessage("Conteggio quiz"),
        "quiz_diff":
            MessageLookupByLibrary.simpleMessage("Difficoltà del quiz"),
        "quiz_gen_success":
            MessageLookupByLibrary.simpleMessage("Quiz generato"),
        "quiz_iap": MessageLookupByLibrary.simpleMessage("Set quiz"),
        "quiz_master": MessageLookupByLibrary.simpleMessage("Maestro Quiz AI"),
        "quiz_score": MessageLookupByLibrary.simpleMessage("Punteggio quiz"),
        "quiz_set": MessageLookupByLibrary.simpleMessage("Set di quiz"),
        "quiz_set_not_found":
            MessageLookupByLibrary.simpleMessage("Set di quiz non trovato"),
        "quizz_for": MessageLookupByLibrary.simpleMessage("Quiz per"),
        "quizzes": MessageLookupByLibrary.simpleMessage("Quiz"),
        "rate": MessageLookupByLibrary.simpleMessage("Valuta"),
        "rate_five_stars":
            MessageLookupByLibrary.simpleMessage("Dacci 5 stelle"),
        "rate_us_on_store":
            MessageLookupByLibrary.simpleMessage("Valutaci sullo store"),
        "rating_cmt1": MessageLookupByLibrary.simpleMessage(
            "App incredibile, sempre meglio. Grazie agli sviluppatori per il loro impegno. La consiglio sopra ogni altra app AI per note."),
        "rating_cmt2": MessageLookupByLibrary.simpleMessage(
            "Adoro quest’app! Perfetta per le mie riunioni."),
        "rating_cmt3": MessageLookupByLibrary.simpleMessage(
            "Dimezzato il tempo di studio. Più pause caffè!"),
        "rating_cmt4": MessageLookupByLibrary.simpleMessage(
            "App strabiliante! Trascrizioni perfette, riassunti e attività geniali."),
        "rating_cmt5": MessageLookupByLibrary.simpleMessage(
            "Meravigliosa, potente! Tutto ciò che vuoi e oltre."),
        "rating_cmt6": MessageLookupByLibrary.simpleMessage(
            "Provata oggi con una presentazione YouTube. In secondi: trascrizione, mappa mentale e flashcard. Efficiente e sophisticated!"),
        "rating_cmt7": MessageLookupByLibrary.simpleMessage(
            "Finora la miglior app per note. Tante funzioni utili."),
        "rating_cmt8": MessageLookupByLibrary.simpleMessage(
            "Cattura ogni dettaglio dalle lezioni di biologia. Il riassunto salva gli esami!"),
        "rating_sub_context_1":
            MessageLookupByLibrary.simpleMessage("Sorprendente!"),
        "rating_sub_context_2":
            MessageLookupByLibrary.simpleMessage("Pro per riunioni"),
        "rating_sub_context_3":
            MessageLookupByLibrary.simpleMessage("Risparmio tempo"),
        "rating_sub_context_4":
            MessageLookupByLibrary.simpleMessage("AI per note al top"),
        "rating_sub_context_5":
            MessageLookupByLibrary.simpleMessage("Amo quest’app"),
        "rating_sub_context_6":
            MessageLookupByLibrary.simpleMessage("AI per note al top"),
        "rating_sub_context_7":
            MessageLookupByLibrary.simpleMessage("Miglior app per note"),
        "rating_sub_context_8":
            MessageLookupByLibrary.simpleMessage("Finora la migliore"),
        "record": MessageLookupByLibrary.simpleMessage("Registrazione"),
        "record_audio": MessageLookupByLibrary.simpleMessage("Registra audio"),
        "record_audio_coming_soon": MessageLookupByLibrary.simpleMessage(
            "Registra audio (prossimamente)"),
        "record_over_x_min": MessageLookupByLibrary.simpleMessage(
            "Registrazione oltre %s minuti"),
        "record_over_x_min_details": MessageLookupByLibrary.simpleMessage(
            "Le registrazioni saranno salvate localmente senza trascrizione AI. Rimuovi i limiti per elaborarle."),
        "record_summarize_lecture":
            MessageLookupByLibrary.simpleMessage("Registra e riassumi lezioni"),
        "recording": MessageLookupByLibrary.simpleMessage("Registrazione"),
        "recording_in_progress":
            MessageLookupByLibrary.simpleMessage("Registrazione in corso"),
        "recording_in_progress_content":
            MessageLookupByLibrary.simpleMessage("Registrazione..."),
        "recording_paused":
            MessageLookupByLibrary.simpleMessage("Registrazione in pausa"),
        "recording_paused_content":
            MessageLookupByLibrary.simpleMessage("Premi per riprendere"),
        "recording_permission_denied": MessageLookupByLibrary.simpleMessage(
            "Permesso registrazione negato!"),
        "recording_permission_denied_details":
            MessageLookupByLibrary.simpleMessage(
                "Vai in Impostazioni per consentire"),
        "recording_quality":
            MessageLookupByLibrary.simpleMessage("Qualità registrazione"),
        "recording_schedule": MessageLookupByLibrary.simpleMessage(
            "Pianificazione registrazione"),
        "recording_voice_note":
            MessageLookupByLibrary.simpleMessage("Registrazione nota vocale"),
        "redeem_7_days_for_0":
            MessageLookupByLibrary.simpleMessage("Riscambia 7 giorni per 0"),
        "redeem_credits": MessageLookupByLibrary.simpleMessage("Riscatta"),
        "refer_now": MessageLookupByLibrary.simpleMessage("Invita ora"),
        "refer_rewards":
            MessageLookupByLibrary.simpleMessage("Invita e guadagna"),
        "referral": MessageLookupByLibrary.simpleMessage("Referral"),
        "referral_already_used":
            MessageLookupByLibrary.simpleMessage("Codice referral già usato."),
        "referral_code":
            MessageLookupByLibrary.simpleMessage("Codice referral"),
        "referral_credits":
            MessageLookupByLibrary.simpleMessage("Crediti referral"),
        "referral_not_found": MessageLookupByLibrary.simpleMessage(
            "Codice referral non trovato."),
        "referral_self_use": MessageLookupByLibrary.simpleMessage(
            "Non puoi usare il tuo codice referral."),
        "referral_time_expired": MessageLookupByLibrary.simpleMessage(
            "Codice referral scaduto dopo 24 ore."),
        "referral_validation_err": MessageLookupByLibrary.simpleMessage(
            "Errore validazione referral."),
        "reload_tap": MessageLookupByLibrary.simpleMessage(
            "Errore, tocca per ricaricare"),
        "remain_recording_length": MessageLookupByLibrary.simpleMessage(
            "30s - 5min rimanenti in base alla durata..."),
        "reminders_record_audio": MessageLookupByLibrary.simpleMessage(
            "Imposta orari registrazione settimanali"),
        "remove_all_limits":
            MessageLookupByLibrary.simpleMessage("Rimuovi tutti i limiti"),
        "replace": MessageLookupByLibrary.simpleMessage("Sostituisci"),
        "replace_all":
            MessageLookupByLibrary.simpleMessage("Sostituisci tutto"),
        "report_issue":
            MessageLookupByLibrary.simpleMessage("Come segnalare un problema?"),
        "report_issue2":
            MessageLookupByLibrary.simpleMessage("Siamo qui per aiutarti:"),
        "required": MessageLookupByLibrary.simpleMessage("Es: Nome_cartella A"),
        "reset": MessageLookupByLibrary.simpleMessage("Ripristina"),
        "restart_now": MessageLookupByLibrary.simpleMessage("Riavvia ora"),
        "restore": MessageLookupByLibrary.simpleMessage("Ripristina"),
        "restore_fail_message": MessageLookupByLibrary.simpleMessage(
            "Per aiuto, contatta <EMAIL>"),
        "restore_fail_title": MessageLookupByLibrary.simpleMessage(
            "Nessun elemento da ripristinare"),
        "restore_purchase":
            MessageLookupByLibrary.simpleMessage("Ripristina acquisto"),
        "restore_success_title":
            MessageLookupByLibrary.simpleMessage("Ripristino riuscito"),
        "retention": MessageLookupByLibrary.simpleMessage("e memoria"),
        "retention_quickly":
            MessageLookupByLibrary.simpleMessage("tra i concetti"),
        "retry": MessageLookupByLibrary.simpleMessage("Riprova"),
        "sale_off": MessageLookupByLibrary.simpleMessage("SCONTO"),
        "satisfied":
            MessageLookupByLibrary.simpleMessage("Grazie per il feedback!"),
        "satisfied_quality": MessageLookupByLibrary.simpleMessage(
            "Questa nota è chiara e utile?"),
        "save": MessageLookupByLibrary.simpleMessage("Salva"),
        "save_50": MessageLookupByLibrary.simpleMessage("Risparmia 50%"),
        "save_changes":
            MessageLookupByLibrary.simpleMessage("Salvare modifiche?"),
        "save_chat": MessageLookupByLibrary.simpleMessage("Salva chat"),
        "save_file": MessageLookupByLibrary.simpleMessage("File salvato"),
        "saved_chat": MessageLookupByLibrary.simpleMessage("Chat salvata"),
        "saved_successfully":
            MessageLookupByLibrary.simpleMessage("Salvato con successo"),
        "saving_recording": MessageLookupByLibrary.simpleMessage(
            "Salvataggio registrazione su dispositivo"),
        "search": MessageLookupByLibrary.simpleMessage("Cerca"),
        "search_emoji": MessageLookupByLibrary.simpleMessage("Cerca emoji"),
        "search_in_files":
            MessageLookupByLibrary.simpleMessage("Cerca nei file"),
        "searching_all_notes":
            MessageLookupByLibrary.simpleMessage("Ricerca in tutte le note"),
        "seconds": MessageLookupByLibrary.simpleMessage("Secondi"),
        "select_a_language": MessageLookupByLibrary.simpleMessage(
            "Seleziona una lingua prima di salvare."),
        "select_all": MessageLookupByLibrary.simpleMessage("Seleziona tutto"),
        "select_and_reorder": MessageLookupByLibrary.simpleMessage(
            "Seleziona e riordina i moduli delle note. Sono necessari almeno 4 tab per continuare."),
        "select_language":
            MessageLookupByLibrary.simpleMessage("Seleziona lingua"),
        "select_your_note": MessageLookupByLibrary.simpleMessage(
            "Seleziona i tuoi moduli di nota."),
        "select_your_primary_use_case": MessageLookupByLibrary.simpleMessage(
            "Scegli il tuo uso principale"),
        "server_err":
            MessageLookupByLibrary.simpleMessage("Errore server sconosciuto."),
        "server_error":
            MessageLookupByLibrary.simpleMessage("Qualcosa è andato storto"),
        "setting": MessageLookupByLibrary.simpleMessage("Impostazioni"),
        "settings": MessageLookupByLibrary.simpleMessage("Impostazioni"),
        "seven_day_free":
            MessageLookupByLibrary.simpleMessage("7 giorni gratis, poi"),
        "share": MessageLookupByLibrary.simpleMessage("Invia ad amici"),
        "share_audio_file":
            MessageLookupByLibrary.simpleMessage("Condividi audio"),
        "share_code_friends": MessageLookupByLibrary.simpleMessage(
            "Condividi il codice con amici via email, social o messaggi."),
        "share_file":
            MessageLookupByLibrary.simpleMessage("Condividi File Audio"),
        "share_note": MessageLookupByLibrary.simpleMessage("Condividi note"),
        "share_note_link":
            MessageLookupByLibrary.simpleMessage("Condividi nota"),
        "share_only": MessageLookupByLibrary.simpleMessage("Condividi"),
        "share_referral_code_start_earning_credits":
            MessageLookupByLibrary.simpleMessage(
                "Condividi il codice per guadagnare crediti!"),
        "share_summary":
            MessageLookupByLibrary.simpleMessage("Copia riassunto"),
        "share_sync":
            MessageLookupByLibrary.simpleMessage("Condividi e sincronizza"),
        "share_transcript":
            MessageLookupByLibrary.simpleMessage("Copia trascrizione"),
        "share_with_link":
            MessageLookupByLibrary.simpleMessage("Condividi con link:"),
        "shared": MessageLookupByLibrary.simpleMessage("Condivise"),
        "sharing_export": MessageLookupByLibrary.simpleMessage(
            "Condivisione ed esportazione"),
        "short": MessageLookupByLibrary.simpleMessage("Breve"),
        "short_description":
            MessageLookupByLibrary.simpleMessage("Solo punti chiave"),
        "shorts": MessageLookupByLibrary.simpleMessage("Brevi"),
        "show_your_love": MessageLookupByLibrary.simpleMessage(
            "Mostra il tuo apprezzamento con una"),
        "signing_in":
            MessageLookupByLibrary.simpleMessage("Accesso in corso..."),
        "skip": MessageLookupByLibrary.simpleMessage("Salta"),
        "slide_count":
            MessageLookupByLibrary.simpleMessage("Crea un diaporama"),
        "slide_count_tooltip": MessageLookupByLibrary.simpleMessage(
            "Massimo 12 diapositive per set"),
        "slide_range":
            MessageLookupByLibrary.simpleMessage("Crea un diaporama"),
        "slide_show": MessageLookupByLibrary.simpleMessage("Presentazione"),
        "smart_learning":
            MessageLookupByLibrary.simpleMessage("Apprendimento intelligente"),
        "smart_note_big_ideas":
            MessageLookupByLibrary.simpleMessage("Note smart, grandi idee"),
        "smart_quizzes":
            MessageLookupByLibrary.simpleMessage("Quiz adattivi illimitati"),
        "smart_start":
            MessageLookupByLibrary.simpleMessage("Pacchetto iniziale smart"),
        "sort_by": MessageLookupByLibrary.simpleMessage("Ordina per"),
        "special_gift": MessageLookupByLibrary.simpleMessage("Regalo speciale"),
        "special_gift_title":
            MessageLookupByLibrary.simpleMessage("REGALO SPECIAL"),
        "special_offer":
            MessageLookupByLibrary.simpleMessage("OFFERTA\nSPECIALE"),
        "speech_language":
            MessageLookupByLibrary.simpleMessage("Lingua parlata"),
        "start_for_free": MessageLookupByLibrary.simpleMessage("Inizia gratis"),
        "start_free_trial":
            MessageLookupByLibrary.simpleMessage("Inizia prova gratuita"),
        "start_my_7_day_trial":
            MessageLookupByLibrary.simpleMessage("Inizia prova 7 giorni"),
        "start_record":
            MessageLookupByLibrary.simpleMessage("Avvia registrazione"),
        "start_speaking":
            MessageLookupByLibrary.simpleMessage("Inizia a parlare"),
        "step1": MessageLookupByLibrary.simpleMessage(
            "Nell’app NoteX, vai su Impostazioni."),
        "step2": MessageLookupByLibrary.simpleMessage(
            "Trova la versione dell’app in basso (es. v1.4.0(6))."),
        "step3": MessageLookupByLibrary.simpleMessage(
            "Tocca la versione 5 volte velocemente."),
        "step4": MessageLookupByLibrary.simpleMessage(
            "Il tuo userID unico sarà copiato negli appunti."),
        "step5":
            MessageLookupByLibrary.simpleMessage("Nel tuo messaggio, includi:"),
        "step51": MessageLookupByLibrary.simpleMessage(
            "Il tuo userID (incollalo dagli appunti)."),
        "step52": MessageLookupByLibrary.simpleMessage(
            "Breve descrizione del problema."),
        "step53": MessageLookupByLibrary.simpleMessage(
            "Dettagli rilevanti (es. modello dispositivo, versione iOS)."),
        "step6": MessageLookupByLibrary.simpleMessage("Invia un’email a "),
        "student": MessageLookupByLibrary.simpleMessage("Uso accademico"),
        "style": MessageLookupByLibrary.simpleMessage("Stile"),
        "sub_rip": MessageLookupByLibrary.simpleMessage("SubRip (.srt)"),
        "sub_user_have_unlimited": MessageLookupByLibrary.simpleMessage(
            "Utenti abbonati: uso illimitato e accesso a tutte le funzioni premium senza annunci.\nUtenti non abbonati: uso con annunci e limiti sulle funzioni premium.\nPagamento addebitato su Google Play alla conferma.\nRinnovo automatico salvo disattivazione entro 24 ore dalla scadenza.\nAddebito per rinnovo entro 24 ore dalla fine del periodo.\nPorzione inutilizzata di prova gratuita persa con l’acquisto.\nGestisci o disattiva il rinnovo su Google Play. Disinstallare l’app non annulla l’abbonamento."),
        "sub_will_auto_renew": MessageLookupByLibrary.simpleMessage(
            "L’abbonamento si rinnova automaticamente. Annulla quando vuoi."),
        "submit": MessageLookupByLibrary.simpleMessage("Invia"),
        "submit_button": MessageLookupByLibrary.simpleMessage("Invia"),
        "subscribe": MessageLookupByLibrary.simpleMessage("Abbonati"),
        "subscribe_via_web": MessageLookupByLibrary.simpleMessage(
            "Se iscritto via web, gestisci su notexapp.com/setting"),
        "success": MessageLookupByLibrary.simpleMessage("Successo"),
        "successfully": MessageLookupByLibrary.simpleMessage("Con successo"),
        "suggest_features":
            MessageLookupByLibrary.simpleMessage("Suggerisci funzioni"),
        "suggested": MessageLookupByLibrary.simpleMessage("Suggeriti"),
        "summarize_video": MessageLookupByLibrary.simpleMessage(
            "Riassumi video YouTube lunghi"),
        "summary": MessageLookupByLibrary.simpleMessage("Riassunto"),
        "summary_style":
            MessageLookupByLibrary.simpleMessage("Stile Riassunto"),
        "summary_successful": MessageLookupByLibrary.simpleMessage(
            "Riassunto creato con successo!"),
        "summary_usefulness":
            MessageLookupByLibrary.simpleMessage("Utilità riassunto"),
        "supercharge":
            MessageLookupByLibrary.simpleMessage("Ottieni di più, meno stress"),
        "support_audio": MessageLookupByLibrary.simpleMessage(
            "Tipi di file supportati: .mp3, .wav, .ogg, .m4a"),
        "support_for_up_to_10_images":
            MessageLookupByLibrary.simpleMessage("Supporto fino a 10 immagini"),
        "support_image": MessageLookupByLibrary.simpleMessage(
            "Tipi di immagine supportati: .png, .jpg, .heif, .heic"),
        "support_over_onehundred_languages":
            MessageLookupByLibrary.simpleMessage("Supporta oltre 100 lingue"),
        "support_youtube_and_more": MessageLookupByLibrary.simpleMessage(
            "Supporta YouTube, Web, TikTok, Instagram, Facebook e altro"),
        "switch_mode": MessageLookupByLibrary.simpleMessage("Cambia Modalità"),
        "sync_from_watch":
            MessageLookupByLibrary.simpleMessage("Sincronizza dall\'orologio"),
        "sync_notes": MessageLookupByLibrary.simpleMessage(
            "Sincronizza note sul browser"),
        "system": MessageLookupByLibrary.simpleMessage("Sistema"),
        "tap_cancel":
            MessageLookupByLibrary.simpleMessage("Tocca Annulla abbonamento"),
        "tap_menu": MessageLookupByLibrary.simpleMessage(
            "Tocca Menu > Abbonamenti e seleziona quello da annullare"),
        "tap_the": MessageLookupByLibrary.simpleMessage("Tocca il"),
        "tap_the_record":
            MessageLookupByLibrary.simpleMessage("Tocca Registra"),
        "tap_to_record": MessageLookupByLibrary.simpleMessage(
            "Tocca per registrare i tuoi pensieri"),
        "task_create_err": MessageLookupByLibrary.simpleMessage(
            "Errore creazione attività. Riprova più tardi."),
        "templates": MessageLookupByLibrary.simpleMessage("Modelli"),
        "term_and_cond":
            MessageLookupByLibrary.simpleMessage("Termini e condizioni"),
        "terms": MessageLookupByLibrary.simpleMessage("Termini"),
        "terms_of_sub":
            MessageLookupByLibrary.simpleMessage("Termini abbonamento"),
        "terms_of_use": MessageLookupByLibrary.simpleMessage("Termini d\'uso"),
        "text": MessageLookupByLibrary.simpleMessage("Aggiungi Testo"),
        "text_must_not_exceed_50_chars":
            MessageLookupByLibrary.simpleMessage("Massimo 50 caratteri"),
        "thank_feedback":
            MessageLookupByLibrary.simpleMessage("Grazie per il feedback!"),
        "thinking": MessageLookupByLibrary.simpleMessage("Pensando..."),
        "thirty_min_per":
            MessageLookupByLibrary.simpleMessage("30 min a\nsettimana"),
        "this_folder_empty": MessageLookupByLibrary.simpleMessage(
            "È ora di creare la tua prima nota AI! ✨"),
        "this_free_trial": MessageLookupByLibrary.simpleMessage(
            "Prova gratuita per nuovi utenti. Scopri tutte le funzioni Pro per una settimana."),
        "this_is_the_language": MessageLookupByLibrary.simpleMessage(
            "Questa è la lingua che vedrai nell\'output del riepilogo"),
        "thousands_trusted": MessageLookupByLibrary.simpleMessage(
            "Valutazione 4.8/5: scelto da migliaia"),
        "time": MessageLookupByLibrary.simpleMessage("Ora"),
        "time_black_friday":
            MessageLookupByLibrary.simpleMessage("22 - 30 Nov"),
        "time_black_friday_2":
            MessageLookupByLibrary.simpleMessage("22 - 30 Novembre"),
        "time_out":
            MessageLookupByLibrary.simpleMessage("Richiesta scaduta. Riprova."),
        "title": MessageLookupByLibrary.simpleMessage("Titolo"),
        "title_error_note":
            MessageLookupByLibrary.simpleMessage("Creazione nota fallita"),
        "title_success_note":
            MessageLookupByLibrary.simpleMessage("Note AI create con successo"),
        "to": MessageLookupByLibrary.simpleMessage("per"),
        "to_day": MessageLookupByLibrary.simpleMessage("Oggi"),
        "token_expired": MessageLookupByLibrary.simpleMessage("Token scaduto!"),
        "tolower_credits": MessageLookupByLibrary.simpleMessage("crediti"),
        "tool_tip_language": MessageLookupByLibrary.simpleMessage(
            "Seleziona la lingua principale prima di salvare"),
        "topic_option":
            MessageLookupByLibrary.simpleMessage("Argomento (opzionale)"),
        "total": MessageLookupByLibrary.simpleMessage("Totale"),
        "transcribing": MessageLookupByLibrary.simpleMessage(
            "Trascrizione con AI avanzata"),
        "transcribing_audio":
            MessageLookupByLibrary.simpleMessage("Trascrizione audio..."),
        "transcript": MessageLookupByLibrary.simpleMessage("Trascrizione"),
        "transcript_context":
            MessageLookupByLibrary.simpleMessage("Contesto Trascrizione"),
        "transcript_language":
            MessageLookupByLibrary.simpleMessage("Lingua trascrizione"),
        "transcript_line_cannot_be_empty": MessageLookupByLibrary.simpleMessage(
            "La riga trascrizione non può essere vuota"),
        "transcript_line_tool_tip": MessageLookupByLibrary.simpleMessage(
            "Clicca sull’elemento trascrizione per modificare"),
        "transcription_precision":
            MessageLookupByLibrary.simpleMessage("Precisione trascrizione"),
        "transform_meetings":
            MessageLookupByLibrary.simpleMessage("Trasforma riunioni in"),
        "transform_meetings_into_actionable_intelligence":
            MessageLookupByLibrary.simpleMessage(
                "Trasforma riunioni in dati utili"),
        "translate_note": MessageLookupByLibrary.simpleMessage("Traduci nota"),
        "translating_note":
            MessageLookupByLibrary.simpleMessage("Traduzione nota..."),
        "translation_completed":
            MessageLookupByLibrary.simpleMessage("Traduzione completata"),
        "translation_failed":
            MessageLookupByLibrary.simpleMessage("Traduzione fallita"),
        "trouble_connecting_to_server": MessageLookupByLibrary.simpleMessage(
            "Stiamo riscontrando problemi di connessione al server. Riprova tra un momento."),
        "try_3_day":
            MessageLookupByLibrary.simpleMessage("Prova 3 giorni gratis"),
        "try_7_day": MessageLookupByLibrary.simpleMessage("Prova 7 giorni"),
        "try_again": MessageLookupByLibrary.simpleMessage(
            "Piccolo problema nella creazione delle note. Riprova!"),
        "try_again_button": MessageLookupByLibrary.simpleMessage("Riprova"),
        "try_pro_free_7_day":
            MessageLookupByLibrary.simpleMessage("Prova Pro gratis 7 giorni"),
        "type_or_paste_any_text_here": MessageLookupByLibrary.simpleMessage(
            "Digita o incolla testo qui. L’AI lo trasforma in un riassunto chiaro."),
        "uidCopied": m3,
        "unable_download_file":
            MessageLookupByLibrary.simpleMessage("Impossibile scaricare file"),
        "unable_load_audio":
            MessageLookupByLibrary.simpleMessage("Impossibile caricare audio:"),
        "unable_share_audio": MessageLookupByLibrary.simpleMessage(
            "Impossibile condividere file audio"),
        "unable_to_connect_to_server": MessageLookupByLibrary.simpleMessage(
            "Assicurati che il tuo telefono sia connesso a internet"),
        "unable_to_extract_web_url": MessageLookupByLibrary.simpleMessage(
            "Impossibile estrarre contenuti da URL web"),
        "unable_to_open_store":
            MessageLookupByLibrary.simpleMessage("Impossibile aprire lo store"),
        "uncover_opportunities":
            MessageLookupByLibrary.simpleMessage("scopri opportunità"),
        "unknown_error":
            MessageLookupByLibrary.simpleMessage("Errore sconosciuto nell’app"),
        "unknown_server_error": MessageLookupByLibrary.simpleMessage(
            "Ops! Problema server. Riprova."),
        "unlimited_ai_chat": MessageLookupByLibrary.simpleMessage(
            "Chat IA illimitata, Mappe mentali IA, Flashcard, Quiz"),
        "unlimited_ai_chat_ai_mind_map_flashcard_quiz":
            MessageLookupByLibrary.simpleMessage(
                "Chat AI, mappe mentali, flashcard, quiz illimitati"),
        "unlimited_ai_note": MessageLookupByLibrary.simpleMessage(
            "Note AI illimitate da tutte le fonti (YouTube, documenti, registrazioni, audio)"),
        "unlimited_ai_notes_from_youtube_and_document":
            MessageLookupByLibrary.simpleMessage(
                "Note AI illimitate da YouTube e documenti"),
        "unlimited_audio_youtube_website_to_ai_notes":
            MessageLookupByLibrary.simpleMessage(
                "Note AI illimitate da audio, YouTube, documenti e siti"),
        "unlimited_everything": MessageLookupByLibrary.simpleMessage(
            "Sperimenta note AI illimitate, servizio prioritario e funzioni premium"),
        "unlimited_youtube_document_ai_notes":
            MessageLookupByLibrary.simpleMessage(
                "Note AI illimitate da YouTube e documenti"),
        "unlock_all_features": MessageLookupByLibrary.simpleMessage(
            "Sbloccare tutte le funzionalità"),
        "unlock_essential_life_time":
            MessageLookupByLibrary.simpleMessage("Sblocca Essential a vita"),
        "unlock_lifetime_access":
            MessageLookupByLibrary.simpleMessage("Sblocca accesso a vita"),
        "unlock_pro_lifetime":
            MessageLookupByLibrary.simpleMessage("Sblocca PRO a vita"),
        "unlock_the_most_ipad": MessageLookupByLibrary.simpleMessage(
            "Sblocca l’assistente AI per note più potente"),
        "unlock_the_most_powerful_ai_note_taking_assistant":
            MessageLookupByLibrary.simpleMessage(
                "Sblocca l’assistente AI per note più potente"),
        "unlock_toge": MessageLookupByLibrary.simpleMessage("SBLOCCA INSIEME"),
        "unlock_together":
            MessageLookupByLibrary.simpleMessage("Sblocca insieme"),
        "unlock_unlimited_access_to_all_ai_features":
            MessageLookupByLibrary.simpleMessage(
                "Sblocca l\'accesso illimitato a tutte le funzionalità dell\'IA"),
        "unlock_unlimited_ai": MessageLookupByLibrary.simpleMessage(
            "Sblocca esperienza AI illimitata"),
        "unsynced_notes":
            MessageLookupByLibrary.simpleMessage("Note non sincronizzate"),
        "update_available": MessageLookupByLibrary.simpleMessage(
            "Nuovo aggiornamento disponibile! Aggiorna per la migliore esperienza."),
        "update_failed": MessageLookupByLibrary.simpleMessage(
            "Aggiornamento note comunità fallito. Riprova."),
        "update_later": MessageLookupByLibrary.simpleMessage("Più tardi"),
        "update_now": MessageLookupByLibrary.simpleMessage("Aggiorna ora!"),
        "update_pro": MessageLookupByLibrary.simpleMessage("Passa a Pro"),
        "update_to_pro": MessageLookupByLibrary.simpleMessage("Passa a PRO"),
        "upgrade": MessageLookupByLibrary.simpleMessage("AGGIORNA"),
        "upgrade_now": MessageLookupByLibrary.simpleMessage("Aggiorna ora!"),
        "upgrade_plan": MessageLookupByLibrary.simpleMessage("Aggiorna piano"),
        "upgrade_to_full_pro_access": MessageLookupByLibrary.simpleMessage(
            "Aggiorna ad Accesso Pro Completo"),
        "upgrade_to_pro_tier_at_a_special_price":
            MessageLookupByLibrary.simpleMessage(
                "Passa a Pro a prezzo speciale"),
        "upload": MessageLookupByLibrary.simpleMessage("Carica"),
        "upload_audio": MessageLookupByLibrary.simpleMessage("Carica audio"),
        "upload_audio_file":
            MessageLookupByLibrary.simpleMessage("Carica file audio"),
        "upload_file": MessageLookupByLibrary.simpleMessage("Carica file"),
        "upload_image": MessageLookupByLibrary.simpleMessage("Carica immagine"),
        "upload_in_progress": MessageLookupByLibrary.simpleMessage(
            "Caricamento in corso. Tieni lo schermo acceso.\nDisattiva VPN per velocità."),
        "uploading_to_server": MessageLookupByLibrary.simpleMessage(
            "Caricamento su server sicuro"),
        "user_disabled": MessageLookupByLibrary.simpleMessage(
            "L’utente di questa email è disabilitato."),
        "user_not_found":
            MessageLookupByLibrary.simpleMessage("Utente non trovato."),
        "verifying_your_credentials":
            MessageLookupByLibrary.simpleMessage("Verifica credenziali"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "video_audio": MessageLookupByLibrary.simpleMessage(
            "Da registrazioni, video e doc a note AI"),
        "video_captions":
            MessageLookupByLibrary.simpleMessage("Sottotitoli video"),
        "video_is_temporary": MessageLookupByLibrary.simpleMessage(
            "*Video temporaneo - Salva prima di chiudere"),
        "visualize_strategies":
            MessageLookupByLibrary.simpleMessage("Visualizza strategie e"),
        "visualize_strategies_opportunities":
            MessageLookupByLibrary.simpleMessage(
                "Visualizza strategie e scopri opportunità"),
        "visualize_strategies_uncover": MessageLookupByLibrary.simpleMessage(
            "Visualizza strategie e scopri"),
        "visualize_strategies_uncover_opportunities":
            MessageLookupByLibrary.simpleMessage(
                "Visualizza strategie, scopri opportunità"),
        "voice": MessageLookupByLibrary.simpleMessage("Voce"),
        "warning_this_ai_note_taking_app_may_cause_excessive_productivity":
            MessageLookupByLibrary.simpleMessage(
                "Attenzione: Quest’app AI per note può causare troppa produttività! 🚀 Usa il mio codice per extra. Codice: "),
        "watch_sync_empty_message": MessageLookupByLibrary.simpleMessage(
            "I tuoi registri Apple Watch appariranno qui"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "web_link": MessageLookupByLibrary.simpleMessage("Link web"),
        "web_sync":
            MessageLookupByLibrary.simpleMessage("Sincronizzazione web"),
        "website_import":
            MessageLookupByLibrary.simpleMessage("Importazione sito web"),
        "week": MessageLookupByLibrary.simpleMessage("settimana"),
        "week_free_limit": MessageLookupByLibrary.simpleMessage(
            "Limite settimanale raggiunto"),
        "weekly": MessageLookupByLibrary.simpleMessage("Settimanale"),
        "weekly_free_limit_reached": MessageLookupByLibrary.simpleMessage(
            "Limite settimanale gratuito raggiunto"),
        "weekly_free_limit_reached_details": MessageLookupByLibrary.simpleMessage(
            "Hai usato tutte le trascrizioni gratuite questa settimana! Passa a Pro o attendi il reset."),
        "welcome_notex":
            MessageLookupByLibrary.simpleMessage("Benvenuto in NoteX!"),
        "welcome_title": MessageLookupByLibrary.simpleMessage(
            "Creiamo la tua\nprima nota AI"),
        "what_improve":
            MessageLookupByLibrary.simpleMessage("Cosa migliorare?"),
        "whats_new": MessageLookupByLibrary.simpleMessage("Novità"),
        "word_docx": MessageLookupByLibrary.simpleMessage("Word (.docx)"),
        "work_notes_projects":
            MessageLookupByLibrary.simpleMessage("Note e progetti lavoro"),
        "writing_style":
            MessageLookupByLibrary.simpleMessage("Stile di Scrittura"),
        "wrong": MessageLookupByLibrary.simpleMessage("Sbagliato"),
        "x": MessageLookupByLibrary.simpleMessage("X"),
        "x_skip": MessageLookupByLibrary.simpleMessage("X?"),
        "year": MessageLookupByLibrary.simpleMessage("anno"),
        "yearly": MessageLookupByLibrary.simpleMessage("Annuale"),
        "yes": MessageLookupByLibrary.simpleMessage("Sì"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Ieri"),
        "you_are_given_a_special_gift_today":
            MessageLookupByLibrary.simpleMessage(
                "Hai ricevuto un regalo speciale oggi 🎁"),
        "you_are_pro": MessageLookupByLibrary.simpleMessage("Accesso PRO"),
        "you_can_update_setting": MessageLookupByLibrary.simpleMessage(
            "Puoi aggiornare in qualsiasi momento nelle Impostazioni."),
        "you_have_received":
            MessageLookupByLibrary.simpleMessage("Hai ricevuto"),
        "you_have_received2": MessageLookupByLibrary.simpleMessage(
            "Avrai una chance di vincere NoteX Pro a vita! 3 vincitori scelti il 30 di ogni mese 🎁"),
        "you_will_get_one_entry_to_win_noteX":
            MessageLookupByLibrary.simpleMessage(
                "Avrai una chance di vincere NoteX"),
        "you_will_not_be": MessageLookupByLibrary.simpleMessage(
            "Non potrai recuperarlo in seguito"),
        "your_learning": MessageLookupByLibrary.simpleMessage(
            "Potenzia il tuo apprendimento!"),
        "your_learning_device":
            MessageLookupByLibrary.simpleMessage("Accesso NoteX Pro"),
        "your_note_are_ready":
            MessageLookupByLibrary.simpleMessage("Le tue note sono pronte."),
        "your_personal_study":
            MessageLookupByLibrary.simpleMessage("Il tuo studio personale"),
        "your_personal_study_assistant": MessageLookupByLibrary.simpleMessage(
            "Il tuo assistente studio personale"),
        "your_plan": MessageLookupByLibrary.simpleMessage("Il tuo piano"),
        "your_primary":
            MessageLookupByLibrary.simpleMessage("Qual è il tuo principale"),
        "your_product":
            MessageLookupByLibrary.simpleMessage("LA TUA PRODUTTIVITÀ"),
        "your_recording_will_save": MessageLookupByLibrary.simpleMessage(
            "Le registrazioni saranno salvate localmente senza trascrizione AI. Rimuovi i limiti per elaborarle."),
        "your_referrals":
            MessageLookupByLibrary.simpleMessage("I tuoi referral"),
        "youtube": MessageLookupByLibrary.simpleMessage("YouTube"),
        "youtube_import":
            MessageLookupByLibrary.simpleMessage("Importa YouTube"),
        "youtube_link": MessageLookupByLibrary.simpleMessage("Link YouTube"),
        "youtube_transcript_language_guidance":
            MessageLookupByLibrary.simpleMessage(
                "Seleziona lingua trascrizione YouTube - usata per generare note AI"),
        "youtube_video": MessageLookupByLibrary.simpleMessage("Video YouTube"),
        "youtube_video_note":
            MessageLookupByLibrary.simpleMessage("Video YouTube"),
        "yt_credit_err": MessageLookupByLibrary.simpleMessage(
            "Crediti YouTube insufficienti. Aggiorna il piano."),
        "yt_credit_use_err": MessageLookupByLibrary.simpleMessage(
            "Errore uso crediti YouTube. Riprova più tardi."),
        "yt_length_err": MessageLookupByLibrary.simpleMessage(
            "Video YouTube oltre 10 ore. Scegli un video più corto."),
        "yt_process_err": MessageLookupByLibrary.simpleMessage(
            "Errore elaborazione video YouTube. Controlla l’URL e riprova."),
        "yt_sum_limit":
            MessageLookupByLibrary.simpleMessage("Limite riassunto YouTube"),
        "z_to_a": MessageLookupByLibrary.simpleMessage("Z-A")
      };
}
