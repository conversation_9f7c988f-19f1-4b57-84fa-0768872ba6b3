// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'move_folder_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$MoveFolderState {
  MoveFolderOneShotEvent get oneShotEvent => throw _privateConstructorUsedError;
  List<MoveFolderViewData> get allFolderViewData =>
      throw _privateConstructorUsedError;
  MoveFolderViewData? get selectedFolder => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MoveFolderStateCopyWith<MoveFolderState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MoveFolderStateCopyWith<$Res> {
  factory $MoveFolderStateCopyWith(
          MoveFolderState value, $Res Function(MoveFolderState) then) =
      _$MoveFolderStateCopyWithImpl<$Res, MoveFolderState>;
  @useResult
  $Res call(
      {MoveFolderOneShotEvent oneShotEvent,
      List<MoveFolderViewData> allFolderViewData,
      MoveFolderViewData? selectedFolder,
      bool isLoading,
      String? errorMessage});

  $MoveFolderViewDataCopyWith<$Res>? get selectedFolder;
}

/// @nodoc
class _$MoveFolderStateCopyWithImpl<$Res, $Val extends MoveFolderState>
    implements $MoveFolderStateCopyWith<$Res> {
  _$MoveFolderStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? allFolderViewData = null,
    Object? selectedFolder = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as MoveFolderOneShotEvent,
      allFolderViewData: null == allFolderViewData
          ? _value.allFolderViewData
          : allFolderViewData // ignore: cast_nullable_to_non_nullable
              as List<MoveFolderViewData>,
      selectedFolder: freezed == selectedFolder
          ? _value.selectedFolder
          : selectedFolder // ignore: cast_nullable_to_non_nullable
              as MoveFolderViewData?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $MoveFolderViewDataCopyWith<$Res>? get selectedFolder {
    if (_value.selectedFolder == null) {
      return null;
    }

    return $MoveFolderViewDataCopyWith<$Res>(_value.selectedFolder!, (value) {
      return _then(_value.copyWith(selectedFolder: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MoveFolderStateImplCopyWith<$Res>
    implements $MoveFolderStateCopyWith<$Res> {
  factory _$$MoveFolderStateImplCopyWith(_$MoveFolderStateImpl value,
          $Res Function(_$MoveFolderStateImpl) then) =
      __$$MoveFolderStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {MoveFolderOneShotEvent oneShotEvent,
      List<MoveFolderViewData> allFolderViewData,
      MoveFolderViewData? selectedFolder,
      bool isLoading,
      String? errorMessage});

  @override
  $MoveFolderViewDataCopyWith<$Res>? get selectedFolder;
}

/// @nodoc
class __$$MoveFolderStateImplCopyWithImpl<$Res>
    extends _$MoveFolderStateCopyWithImpl<$Res, _$MoveFolderStateImpl>
    implements _$$MoveFolderStateImplCopyWith<$Res> {
  __$$MoveFolderStateImplCopyWithImpl(
      _$MoveFolderStateImpl _value, $Res Function(_$MoveFolderStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? oneShotEvent = null,
    Object? allFolderViewData = null,
    Object? selectedFolder = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$MoveFolderStateImpl(
      oneShotEvent: null == oneShotEvent
          ? _value.oneShotEvent
          : oneShotEvent // ignore: cast_nullable_to_non_nullable
              as MoveFolderOneShotEvent,
      allFolderViewData: null == allFolderViewData
          ? _value._allFolderViewData
          : allFolderViewData // ignore: cast_nullable_to_non_nullable
              as List<MoveFolderViewData>,
      selectedFolder: freezed == selectedFolder
          ? _value.selectedFolder
          : selectedFolder // ignore: cast_nullable_to_non_nullable
              as MoveFolderViewData?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$MoveFolderStateImpl implements _MoveFolderState {
  const _$MoveFolderStateImpl(
      {this.oneShotEvent = MoveFolderOneShotEvent.initial,
      final List<MoveFolderViewData> allFolderViewData = const [],
      this.selectedFolder,
      this.isLoading = false,
      this.errorMessage})
      : _allFolderViewData = allFolderViewData;

  @override
  @JsonKey()
  final MoveFolderOneShotEvent oneShotEvent;
  final List<MoveFolderViewData> _allFolderViewData;
  @override
  @JsonKey()
  List<MoveFolderViewData> get allFolderViewData {
    if (_allFolderViewData is EqualUnmodifiableListView)
      return _allFolderViewData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_allFolderViewData);
  }

  @override
  final MoveFolderViewData? selectedFolder;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'MoveFolderState(oneShotEvent: $oneShotEvent, allFolderViewData: $allFolderViewData, selectedFolder: $selectedFolder, isLoading: $isLoading, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MoveFolderStateImpl &&
            (identical(other.oneShotEvent, oneShotEvent) ||
                other.oneShotEvent == oneShotEvent) &&
            const DeepCollectionEquality()
                .equals(other._allFolderViewData, _allFolderViewData) &&
            (identical(other.selectedFolder, selectedFolder) ||
                other.selectedFolder == selectedFolder) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      oneShotEvent,
      const DeepCollectionEquality().hash(_allFolderViewData),
      selectedFolder,
      isLoading,
      errorMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MoveFolderStateImplCopyWith<_$MoveFolderStateImpl> get copyWith =>
      __$$MoveFolderStateImplCopyWithImpl<_$MoveFolderStateImpl>(
          this, _$identity);
}

abstract class _MoveFolderState implements MoveFolderState {
  const factory _MoveFolderState(
      {final MoveFolderOneShotEvent oneShotEvent,
      final List<MoveFolderViewData> allFolderViewData,
      final MoveFolderViewData? selectedFolder,
      final bool isLoading,
      final String? errorMessage}) = _$MoveFolderStateImpl;

  @override
  MoveFolderOneShotEvent get oneShotEvent;
  @override
  List<MoveFolderViewData> get allFolderViewData;
  @override
  MoveFolderViewData? get selectedFolder;
  @override
  bool get isLoading;
  @override
  String? get errorMessage;
  @override
  @JsonKey(ignore: true)
  _$$MoveFolderStateImplCopyWith<_$MoveFolderStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
