// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'move_folder_view_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

/// @nodoc
mixin _$MoveFolderViewData {
  String get id => throw _privateConstructorUsedError;
  String get backendId => throw _privateConstructorUsedError;
  String get folderName => throw _privateConstructorUsedError;
  String get icon => throw _privateConstructorUsedError;
  String get createdAt => throw _privateConstructorUsedError;
  String get updatedAt => throw _privateConstructorUsedError;
  String? get parentFolderId => throw _privateConstructorUsedError;
  List<MoveFolderViewData> get subfolders => throw _privateConstructorUsedError;
  String get path => throw _privateConstructorUsedError;
  int get level => throw _privateConstructorUsedError;
  int get noteCount => throw _privateConstructorUsedError;
  bool get isSelected => throw _privateConstructorUsedError;
  bool get isExpanded => throw _privateConstructorUsedError;
  bool get isInvalid => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $MoveFolderViewDataCopyWith<MoveFolderViewData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MoveFolderViewDataCopyWith<$Res> {
  factory $MoveFolderViewDataCopyWith(
          MoveFolderViewData value, $Res Function(MoveFolderViewData) then) =
      _$MoveFolderViewDataCopyWithImpl<$Res, MoveFolderViewData>;
  @useResult
  $Res call(
      {String id,
      String backendId,
      String folderName,
      String icon,
      String createdAt,
      String updatedAt,
      String? parentFolderId,
      List<MoveFolderViewData> subfolders,
      String path,
      int level,
      int noteCount,
      bool isSelected,
      bool isExpanded,
      bool isInvalid});
}

/// @nodoc
class _$MoveFolderViewDataCopyWithImpl<$Res, $Val extends MoveFolderViewData>
    implements $MoveFolderViewDataCopyWith<$Res> {
  _$MoveFolderViewDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? backendId = null,
    Object? folderName = null,
    Object? icon = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? parentFolderId = freezed,
    Object? subfolders = null,
    Object? path = null,
    Object? level = null,
    Object? noteCount = null,
    Object? isSelected = null,
    Object? isExpanded = null,
    Object? isInvalid = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      backendId: null == backendId
          ? _value.backendId
          : backendId // ignore: cast_nullable_to_non_nullable
              as String,
      folderName: null == folderName
          ? _value.folderName
          : folderName // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
      parentFolderId: freezed == parentFolderId
          ? _value.parentFolderId
          : parentFolderId // ignore: cast_nullable_to_non_nullable
              as String?,
      subfolders: null == subfolders
          ? _value.subfolders
          : subfolders // ignore: cast_nullable_to_non_nullable
              as List<MoveFolderViewData>,
      path: null == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      noteCount: null == noteCount
          ? _value.noteCount
          : noteCount // ignore: cast_nullable_to_non_nullable
              as int,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isExpanded: null == isExpanded
          ? _value.isExpanded
          : isExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      isInvalid: null == isInvalid
          ? _value.isInvalid
          : isInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MoveFolderViewModelImplCopyWith<$Res>
    implements $MoveFolderViewDataCopyWith<$Res> {
  factory _$$MoveFolderViewModelImplCopyWith(_$MoveFolderViewModelImpl value,
          $Res Function(_$MoveFolderViewModelImpl) then) =
      __$$MoveFolderViewModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String backendId,
      String folderName,
      String icon,
      String createdAt,
      String updatedAt,
      String? parentFolderId,
      List<MoveFolderViewData> subfolders,
      String path,
      int level,
      int noteCount,
      bool isSelected,
      bool isExpanded,
      bool isInvalid});
}

/// @nodoc
class __$$MoveFolderViewModelImplCopyWithImpl<$Res>
    extends _$MoveFolderViewDataCopyWithImpl<$Res, _$MoveFolderViewModelImpl>
    implements _$$MoveFolderViewModelImplCopyWith<$Res> {
  __$$MoveFolderViewModelImplCopyWithImpl(_$MoveFolderViewModelImpl _value,
      $Res Function(_$MoveFolderViewModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? backendId = null,
    Object? folderName = null,
    Object? icon = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? parentFolderId = freezed,
    Object? subfolders = null,
    Object? path = null,
    Object? level = null,
    Object? noteCount = null,
    Object? isSelected = null,
    Object? isExpanded = null,
    Object? isInvalid = null,
  }) {
    return _then(_$MoveFolderViewModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      backendId: null == backendId
          ? _value.backendId
          : backendId // ignore: cast_nullable_to_non_nullable
              as String,
      folderName: null == folderName
          ? _value.folderName
          : folderName // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as String,
      parentFolderId: freezed == parentFolderId
          ? _value.parentFolderId
          : parentFolderId // ignore: cast_nullable_to_non_nullable
              as String?,
      subfolders: null == subfolders
          ? _value._subfolders
          : subfolders // ignore: cast_nullable_to_non_nullable
              as List<MoveFolderViewData>,
      path: null == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _value.level
          : level // ignore: cast_nullable_to_non_nullable
              as int,
      noteCount: null == noteCount
          ? _value.noteCount
          : noteCount // ignore: cast_nullable_to_non_nullable
              as int,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
      isExpanded: null == isExpanded
          ? _value.isExpanded
          : isExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      isInvalid: null == isInvalid
          ? _value.isInvalid
          : isInvalid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$MoveFolderViewModelImpl implements _MoveFolderViewModel {
  const _$MoveFolderViewModelImpl(
      {required this.id,
      required this.backendId,
      required this.folderName,
      required this.icon,
      required this.createdAt,
      required this.updatedAt,
      this.parentFolderId,
      final List<MoveFolderViewData> subfolders = const [],
      required this.path,
      required this.level,
      required this.noteCount,
      this.isSelected = false,
      this.isExpanded = false,
      this.isInvalid = false})
      : _subfolders = subfolders;

  @override
  final String id;
  @override
  final String backendId;
  @override
  final String folderName;
  @override
  final String icon;
  @override
  final String createdAt;
  @override
  final String updatedAt;
  @override
  final String? parentFolderId;
  final List<MoveFolderViewData> _subfolders;
  @override
  @JsonKey()
  List<MoveFolderViewData> get subfolders {
    if (_subfolders is EqualUnmodifiableListView) return _subfolders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subfolders);
  }

  @override
  final String path;
  @override
  final int level;
  @override
  final int noteCount;
  @override
  @JsonKey()
  final bool isSelected;
  @override
  @JsonKey()
  final bool isExpanded;
  @override
  @JsonKey()
  final bool isInvalid;

  @override
  String toString() {
    return 'MoveFolderViewData(id: $id, backendId: $backendId, folderName: $folderName, icon: $icon, createdAt: $createdAt, updatedAt: $updatedAt, parentFolderId: $parentFolderId, subfolders: $subfolders, path: $path, level: $level, noteCount: $noteCount, isSelected: $isSelected, isExpanded: $isExpanded, isInvalid: $isInvalid)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MoveFolderViewModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.backendId, backendId) ||
                other.backendId == backendId) &&
            (identical(other.folderName, folderName) ||
                other.folderName == folderName) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.parentFolderId, parentFolderId) ||
                other.parentFolderId == parentFolderId) &&
            const DeepCollectionEquality()
                .equals(other._subfolders, _subfolders) &&
            (identical(other.path, path) || other.path == path) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.noteCount, noteCount) ||
                other.noteCount == noteCount) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected) &&
            (identical(other.isExpanded, isExpanded) ||
                other.isExpanded == isExpanded) &&
            (identical(other.isInvalid, isInvalid) ||
                other.isInvalid == isInvalid));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      backendId,
      folderName,
      icon,
      createdAt,
      updatedAt,
      parentFolderId,
      const DeepCollectionEquality().hash(_subfolders),
      path,
      level,
      noteCount,
      isSelected,
      isExpanded,
      isInvalid);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$MoveFolderViewModelImplCopyWith<_$MoveFolderViewModelImpl> get copyWith =>
      __$$MoveFolderViewModelImplCopyWithImpl<_$MoveFolderViewModelImpl>(
          this, _$identity);
}

abstract class _MoveFolderViewModel implements MoveFolderViewData {
  const factory _MoveFolderViewModel(
      {required final String id,
      required final String backendId,
      required final String folderName,
      required final String icon,
      required final String createdAt,
      required final String updatedAt,
      final String? parentFolderId,
      final List<MoveFolderViewData> subfolders,
      required final String path,
      required final int level,
      required final int noteCount,
      final bool isSelected,
      final bool isExpanded,
      final bool isInvalid}) = _$MoveFolderViewModelImpl;

  @override
  String get id;
  @override
  String get backendId;
  @override
  String get folderName;
  @override
  String get icon;
  @override
  String get createdAt;
  @override
  String get updatedAt;
  @override
  String? get parentFolderId;
  @override
  List<MoveFolderViewData> get subfolders;
  @override
  String get path;
  @override
  int get level;
  @override
  int get noteCount;
  @override
  bool get isSelected;
  @override
  bool get isExpanded;
  @override
  bool get isInvalid;
  @override
  @JsonKey(ignore: true)
  _$$MoveFolderViewModelImplCopyWith<_$MoveFolderViewModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
