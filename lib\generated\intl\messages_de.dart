// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a de locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'de';

  static String m0(date) => "Ihre Testversion läuft in ${date} Tagen ab.";

  static String m1(images) => "${images} Fotos wurden hochgeladen";

  static String m2(price, date) =>
      "Ihre nächste Rechnung über ${price} am ${date}.";

  static String m3(uid) => "UID ${uid} in Zwischenablage kopiert!";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "Regenerate": MessageLookupByLibrary.simpleMessage("Neu generieren"),
        "a_to_z": MessageLookupByLibrary.simpleMessage("A bis Z"),
        "about_us": MessageLookupByLibrary.simpleMessage("Über uns"),
        "access_notex_web":
            MessageLookupByLibrary.simpleMessage("Zugang zu NoteX web"),
        "account": MessageLookupByLibrary.simpleMessage("Konto"),
        "account_basic": MessageLookupByLibrary.simpleMessage("Basis"),
        "account_content_basic":
            MessageLookupByLibrary.simpleMessage("Begrenzte KI-Erfahrung"),
        "account_content_pro": MessageLookupByLibrary.simpleMessage(
            "Unbegrenzte KI-Erfahrung freischalten"),
        "account_lifetime": MessageLookupByLibrary.simpleMessage("Lebenslang"),
        "achieve_more": MessageLookupByLibrary.simpleMessage("MEHR ERREICHEN"),
        "action_items": MessageLookupByLibrary.simpleMessage("Aufgaben"),
        "actionable_intelligence":
            MessageLookupByLibrary.simpleMessage("Aktionen"),
        "active_description": MessageLookupByLibrary.simpleMessage(
            "Keine aktive Beschreibung gefunden."),
        "active_recall":
            MessageLookupByLibrary.simpleMessage("aktives Erinnern"),
        "add_folder":
            MessageLookupByLibrary.simpleMessage("In Ordner verschieben"),
        "add_note": MessageLookupByLibrary.simpleMessage("Notiz erstellen"),
        "add_password":
            MessageLookupByLibrary.simpleMessage("Passwort hinzufügen"),
        "add_password_to_public": MessageLookupByLibrary.simpleMessage(
            "Passwort zum öffentlichen Link hinzufügen"),
        "add_to": MessageLookupByLibrary.simpleMessage("Verschieben nach"),
        "add_to_notes":
            MessageLookupByLibrary.simpleMessage("Notizen hinzufügen"),
        "additional_ins": MessageLookupByLibrary.simpleMessage(
            "Zusätzliche Anweisungen (opt.)"),
        "advance_mode":
            MessageLookupByLibrary.simpleMessage("Erweiterter Modus"),
        "advanced": MessageLookupByLibrary.simpleMessage("Erweitert"),
        "afternoon_content": MessageLookupByLibrary.simpleMessage(
            "Kleine Notizen, große Wirkung"),
        "afternoon_content_1": MessageLookupByLibrary.simpleMessage(
            "Gedanken erfasst, Geist frei"),
        "afternoon_content_3":
            MessageLookupByLibrary.simpleMessage("Ordnung im Chaos"),
        "afternoon_content_4":
            MessageLookupByLibrary.simpleMessage("Deine Ideen, organisiert"),
        "afternoon_content_5":
            MessageLookupByLibrary.simpleMessage("Klarheit im Fortschritt"),
        "afternoon_content_6":
            MessageLookupByLibrary.simpleMessage("Behalte, was wichtig ist"),
        "ai_audio_transcription_per_day": MessageLookupByLibrary.simpleMessage(
            "3 AI Audio Transkription pro Tag *"),
        "ai_chat": MessageLookupByLibrary.simpleMessage("Nova KI"),
        "ai_chat_assistant":
            MessageLookupByLibrary.simpleMessage("KI-Chatassistent"),
        "ai_chat_with_notes":
            MessageLookupByLibrary.simpleMessage("KI-Chat mit Notizen"),
        "ai_insight": MessageLookupByLibrary.simpleMessage("KI-Einblicke"),
        "ai_learning": MessageLookupByLibrary.simpleMessage("KI-Lernen"),
        "ai_learning_companion":
            MessageLookupByLibrary.simpleMessage("Ich bin Nova AI von NoteX"),
        "ai_note_create":
            MessageLookupByLibrary.simpleMessage("KI-Notizen erstellen"),
        "ai_note_creation":
            MessageLookupByLibrary.simpleMessage("KI-Notizen erstellen"),
        "ai_note_from":
            MessageLookupByLibrary.simpleMessage("KI-Notizen aus Audio"),
        "ai_notes_10": MessageLookupByLibrary.simpleMessage(
            "Unbegrenzte KI-Notizen von YouTube & Dokumenten"),
        "ai_notes_3": MessageLookupByLibrary.simpleMessage(
            "3 KI-Notizen pro Tag aus Aufnahmen & Audio-Uploads (bis zu 60 Min. pro Datei)"),
        "ai_notes_from": MessageLookupByLibrary.simpleMessage(
            "KI-Notizen aus \nYouTube, Web, Docs"),
        "ai_short_1":
            MessageLookupByLibrary.simpleMessage("3 KI-Kurzvideos pro Tag"),
        "ai_short_3": MessageLookupByLibrary.simpleMessage(
            "5 KI-Kurzvideos pro Tag (Beta)"),
        "ai_short_video": MessageLookupByLibrary.simpleMessage("KI-Kurzvideos"),
        "ai_study_practice":
            MessageLookupByLibrary.simpleMessage("KI-Lernübungen"),
        "ai_study_tools": MessageLookupByLibrary.simpleMessage("KI-Lerntools"),
        "ai_summarize":
            MessageLookupByLibrary.simpleMessage("KI-Zusammenfassung"),
        "ai_transcription":
            MessageLookupByLibrary.simpleMessage("KI-Transkription"),
        "ai_workflow": MessageLookupByLibrary.simpleMessage("KI-Workflow"),
        "all": MessageLookupByLibrary.simpleMessage("Alle"),
        "all_note": MessageLookupByLibrary.simpleMessage("Alle Notizen"),
        "all_note_in_folder": MessageLookupByLibrary.simpleMessage(
            "Sind Sie sicher, dass Sie diesen Ordner entfernen möchten?"),
        "all_tabs": MessageLookupByLibrary.simpleMessage("Alle Tabs"),
        "allow": MessageLookupByLibrary.simpleMessage("Erlauben"),
        "almost_done": MessageLookupByLibrary.simpleMessage("Fast fertig"),
        "and": MessageLookupByLibrary.simpleMessage("und"),
        "answer": MessageLookupByLibrary.simpleMessage("Antwort"),
        "anyone_with_link":
            MessageLookupByLibrary.simpleMessage("Jeder mit Link kann ansehen"),
        "app_feedback":
            MessageLookupByLibrary.simpleMessage("NoteX App Feedback"),
        "app_store":
            MessageLookupByLibrary.simpleMessage("Bewertung im App Store"),
        "appearance": MessageLookupByLibrary.simpleMessage("Aussehen"),
        "appreciate_cooperation": MessageLookupByLibrary.simpleMessage(
            "Diese Informationen helfen unserem Support-Team, dein Problem schnell zu lösen. Danke für deine Mithilfe bei der Verbesserung von NoteX."),
        "appreciate_cooperation2": MessageLookupByLibrary.simpleMessage(
            "Dies hilft uns, dein Problem effektiv zu lösen."),
        "appreciate_cooperation3": MessageLookupByLibrary.simpleMessage(
            "Danke für dein Vertrauen in NoteX AI!"),
        "are_you_sure":
            MessageLookupByLibrary.simpleMessage("Einmaliges Angebot"),
        "ask_anything":
            MessageLookupByLibrary.simpleMessage("Hätte ich eine Frage..."),
        "assist_faster":
            MessageLookupByLibrary.simpleMessage("Für schnellere Hilfe:"),
        "assistant": MessageLookupByLibrary.simpleMessage("Assistent"),
        "at_your_pace": MessageLookupByLibrary.simpleMessage("Bestnote"),
        "audio": MessageLookupByLibrary.simpleMessage("Audio"),
        "audio_file": MessageLookupByLibrary.simpleMessage("Audiodatei"),
        "audio_is_temporary": MessageLookupByLibrary.simpleMessage(
            "*Audio ist temporär - Vor dem Schließen speichern"),
        "audio_length_err": MessageLookupByLibrary.simpleMessage(
            "Audio zu lang. Kürzere Datei hochladen."),
        "audio_length_limit":
            MessageLookupByLibrary.simpleMessage("Audio-Längenlimit"),
        "audio_process_err": MessageLookupByLibrary.simpleMessage(
            "Audiodatei nicht verarbeitbar. Andere Datei versuchen."),
        "audio_recording_ai_notes_daily": MessageLookupByLibrary.simpleMessage(
            "3 Audio- & Aufnahme-KI-Notizen täglich*"),
        "audio_to_ai_note":
            MessageLookupByLibrary.simpleMessage("Audio zu KI-Notizen"),
        "audio_upload_note":
            MessageLookupByLibrary.simpleMessage("Audio-Upload"),
        "auto": MessageLookupByLibrary.simpleMessage("Automatisch"),
        "auto_detect":
            MessageLookupByLibrary.simpleMessage("Automatisch erkennen"),
        "auto_generate_slides": MessageLookupByLibrary.simpleMessage(
            "Erstelle sofort ansprechende Folien"),
        "auto_renew_after_trial": MessageLookupByLibrary.simpleMessage(
            "Automatische Verlängerung nach Testphase • Jederzeit kündbar"),
        "auto_renewable_after_trial": MessageLookupByLibrary.simpleMessage(
            "Automatische Verlängerung nach Testphase. Jederzeit kündbar"),
        "auto_renewal": MessageLookupByLibrary.simpleMessage(
            "Automatische Verlängerung, jederzeit kündbar"),
        "available_credits":
            MessageLookupByLibrary.simpleMessage("Verfügbare Guthaben"),
        "available_transcript": MessageLookupByLibrary.simpleMessage(
            "Transkript nach Notiz-Erstellung verfügbar!"),
        "back_content":
            MessageLookupByLibrary.simpleMessage(" Punkte erreicht"),
        "background_style":
            MessageLookupByLibrary.simpleMessage("Hintergrundstil"),
        "balanced": MessageLookupByLibrary.simpleMessage("Ausgewogen"),
        "balanced_description":
            MessageLookupByLibrary.simpleMessage("Hauptideen mit Kontext"),
        "basic": MessageLookupByLibrary.simpleMessage("Basic-Tarif"),
        "basic_features":
            MessageLookupByLibrary.simpleMessage("Basis KI-Funktionen"),
        "beta": MessageLookupByLibrary.simpleMessage("Beta"),
        "between_concepts": MessageLookupByLibrary.simpleMessage(
            "Verbinde die Punkte zwischen Konzepten"),
        "black_friday_sale":
            MessageLookupByLibrary.simpleMessage("Weihnachten-Verkauf!"),
        "blurred_output_image": MessageLookupByLibrary.simpleMessage(
            "Stil-Generierung fehlgeschlagen! Anderen Stil oder Bild wählen!"),
        "body_error_document_upload": MessageLookupByLibrary.simpleMessage(
            "Fehler bei der Dokumentverarbeitung. Bitte in der App erneut versuchen."),
        "body_error_note_document": MessageLookupByLibrary.simpleMessage(
            "Fehler bei der Dokumentverarbeitung. Bitte in der App erneut versuchen."),
        "body_error_note_recording": MessageLookupByLibrary.simpleMessage(
            "Fehler bei der Aufnahme. Bitte in der App erneut versuchen."),
        "body_error_note_upload": MessageLookupByLibrary.simpleMessage(
            "Fehler beim Audio-Upload. Bitte in der App erneut versuchen."),
        "body_error_note_web": MessageLookupByLibrary.simpleMessage(
            "Beim Verarbeiten des Web-Links ist ein Fehler aufgetreten. Bitte öffnen Sie die App und versuchen Sie es erneut."),
        "body_error_note_youtube": MessageLookupByLibrary.simpleMessage(
            "Beim Verarbeiten des YouTube-Links ist ein Fehler aufgetreten. Bitte öffnen Sie die App und versuchen Sie es erneut."),
        "body_success_note":
            MessageLookupByLibrary.simpleMessage("Deine KI-Notiz ist bereit."),
        "bonus_credits_for_new_referred_friends_only":
            MessageLookupByLibrary.simpleMessage(
                "Bonus-Guthaben für neue empfohlene Freunde"),
        "boost_comprehension": MessageLookupByLibrary.simpleMessage(
            "Steigere Verständnis und Merkfähigkeit"),
        "boost_comprehension2":
            MessageLookupByLibrary.simpleMessage("Verständnis und"),
        "boost_flashcards_quizzes": MessageLookupByLibrary.simpleMessage(
            "Lernfortschritt mit KI-Lernkarten und Quiz steigern"),
        "boost_knowledge":
            MessageLookupByLibrary.simpleMessage("Verbinde die Punkte"),
        "boost_knowledge_retention": MessageLookupByLibrary.simpleMessage(
            "Verbinde die Punkte zwischen"),
        "both_you_friends_receive_usage_credits":
            MessageLookupByLibrary.simpleMessage(
                "Du und deine Freunde erhalten Nutzungsguthaben."),
        "brief_service_disruption": MessageLookupByLibrary.simpleMessage(
            "Aufgabe nicht gefunden oder abgelaufen. Bitte erneut versuchen. Aktuelle Statusinformationen auf Discord."),
        "business_uses":
            MessageLookupByLibrary.simpleMessage("Business-Nutzung"),
        "button_below": MessageLookupByLibrary.simpleMessage(
            "Drücke den Knopf unten oder wähle einen bestimmten Inhaltstyp zum Starten"),
        "buy_one_forever": MessageLookupByLibrary.simpleMessage(
            "Einmal kaufen. Max. Produktivität für immer."),
        "by_subscribing": MessageLookupByLibrary.simpleMessage(
            "Mit dem Abonnement stimmen Sie zu"),
        "by_taping_continue": MessageLookupByLibrary.simpleMessage(
            "Durch Fortfahren stimmen Sie zu"),
        "by_tapping_started": MessageLookupByLibrary.simpleMessage(
            "Durch Tippen auf \"Loslegen\" stimmen Sie unseren"),
        "camera": MessageLookupByLibrary.simpleMessage("Kamera"),
        "camera_access": MessageLookupByLibrary.simpleMessage(
            "\"NoteX\" Möchte auf die Kamera Zugreifen"),
        "camera_permission":
            MessageLookupByLibrary.simpleMessage("Bild nicht freigegeben"),
        "camera_permission_denied_details": MessageLookupByLibrary.simpleMessage(
            "Zugriff auf die Kamera erforderlich, um Bilder auszuwählen. Bitte gewähren Sie den Zugriff in den Einstellungen."),
        "can_improve":
            MessageLookupByLibrary.simpleMessage("Was können wir verbessern?"),
        "cancel": MessageLookupByLibrary.simpleMessage("Abbrechen"),
        "cannot_create_pdf_file_from_image":
            MessageLookupByLibrary.simpleMessage(
                "PDF-Datei kann nicht aus Bild erstellt werden"),
        "cannot_extract_text_from_pdf": MessageLookupByLibrary.simpleMessage(
            "Dokument nicht lesbar. Kein Text extrahierbar. Häufig bei Scans oder Bild-PDFs."),
        "card": MessageLookupByLibrary.simpleMessage("Karte"),
        "card_count": MessageLookupByLibrary.simpleMessage("Kartenanzahl"),
        "card_difficulty":
            MessageLookupByLibrary.simpleMessage("Kartenschwierigkeit"),
        "change": MessageLookupByLibrary.simpleMessage("Ändern"),
        "change_plan": MessageLookupByLibrary.simpleMessage("Tarif ändern"),
        "chaos_into_clarity": MessageLookupByLibrary.simpleMessage(
            "Chaos in Klarheit verwandeln"),
        "characters": MessageLookupByLibrary.simpleMessage("Zeichen"),
        "chat_empty": MessageLookupByLibrary.simpleMessage("Chat leer"),
        "chat_topic_temporary_stored": MessageLookupByLibrary.simpleMessage(
            "Temporäre Sitzung, \"Chat speichern\" zum Behalten"),
        "check_if_you": MessageLookupByLibrary.simpleMessage(
            "Prüfen Sie, ob Sie im richtigen Google-Konto angemeldet sind"),
        "check_update":
            MessageLookupByLibrary.simpleMessage("Neue Version verfügbar"),
        "child_detected": MessageLookupByLibrary.simpleMessage(
            "Kind erkannt. Anderes Bild wählen."),
        "choose_your_note":
            MessageLookupByLibrary.simpleMessage("Wähle dein NoteX"),
        "choose_your_note_experience":
            MessageLookupByLibrary.simpleMessage("Wähle dein NoteX-Erlebnis"),
        "click_create_podcast": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie auf \'Podcast erstellen\', um Ihre Notiz in ansprechendes Audio zu verwandeln"),
        "click_create_short": MessageLookupByLibrary.simpleMessage(
            "Klick auf \'Shorts erstellen\', um deine Notiz in ansprechende Shorts zu verwandeln"),
        "click_create_slide": MessageLookupByLibrary.simpleMessage(
            "Klicken Sie auf \'Erstellen Sie ein Folienshow\', um Ihre Notiz als Präsentation anzuzeigen"),
        "click_start_flashcard": MessageLookupByLibrary.simpleMessage(
            "Klicke auf \'Karteikarten erstellen\', um Karteikarten aus dem Transkript zu generieren. Du kannst mehrere Sets erstellen."),
        "click_start_mindmap": MessageLookupByLibrary.simpleMessage(
            "Klicke auf \'Mindmap erstellen\', um eine Mindmap auf Basis des Transkripts zu generieren"),
        "click_start_quiz": MessageLookupByLibrary.simpleMessage(
            "Klicke auf \'Quiz erstellen\', um Fragensets aus dem Transkript zu generieren. Du kannst mehrere Sets erstellen."),
        "click_to_flip":
            MessageLookupByLibrary.simpleMessage("Zum Umdrehen klicken"),
        "coming_soon":
            MessageLookupByLibrary.simpleMessage("Demnächst verfügbar"),
        "community": MessageLookupByLibrary.simpleMessage("Community"),
        "community_feedback":
            MessageLookupByLibrary.simpleMessage("Community & Feedback"),
        "comprehensive": MessageLookupByLibrary.simpleMessage("Umfassend"),
        "comprehensive_description": MessageLookupByLibrary.simpleMessage(
            "Detaillierte Abdeckung mit unterstützenden Punkten"),
        "congratulations": MessageLookupByLibrary.simpleMessage("Glückwunsch!"),
        "connect_friends": MessageLookupByLibrary.simpleMessage(
            "Geteilte Notiz-Links von Freunden einfach importieren"),
        "connection_fail":
            MessageLookupByLibrary.simpleMessage("Verbindungsfehler!"),
        "connection_timeout": MessageLookupByLibrary.simpleMessage(
            "Verbindungs-Timeout. Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut."),
        "contact_support": MessageLookupByLibrary.simpleMessage("Kontakt"),
        "content_account_trial": m0,
        "content_button_flashcard":
            MessageLookupByLibrary.simpleMessage("Karteikarten erstellen"),
        "content_button_mindmap":
            MessageLookupByLibrary.simpleMessage("Mindmap erstellen"),
        "content_button_quiz":
            MessageLookupByLibrary.simpleMessage("Quiz erstellen"),
        "content_button_summary":
            MessageLookupByLibrary.simpleMessage("Zusammenfassung erstellen"),
        "content_camera_access": MessageLookupByLibrary.simpleMessage(
            "NoteX benötigt Zugriff auf Ihre Kamera, um Texte aus Bildern zu erfassen, zu erkennen und zu digitalisieren"),
        "content_delete_note": MessageLookupByLibrary.simpleMessage(
            "Sie können sie danach nicht wiederherstellen"),
        "content_delete_note_detail": MessageLookupByLibrary.simpleMessage(
            "Möchten Sie diese Notiz wirklich entfernen?"),
        "content_delete_reminder": MessageLookupByLibrary.simpleMessage(
            "Sind Sie sicher, dass Sie diese Erinnerung löschen möchten?"),
        "content_discard_changes": MessageLookupByLibrary.simpleMessage(
            "Beim Verlassen wird die Aufnahme gestoppt und alle Änderungen verworfen."),
        "content_discard_changes_image": MessageLookupByLibrary.simpleMessage(
            "Beim Schließen werden die aufgenommenen Fotos verworfen"),
        "content_discard_changes_note": MessageLookupByLibrary.simpleMessage(
            "Diese Aktion verwirft alle Änderungen, sie können nicht rückgängig gemacht werden."),
        "content_discard_changes_reminder": MessageLookupByLibrary.simpleMessage(
            "Beim Verlassen wird die Erinnerungsbenachrichtigung geschlossen und alle Änderungen verworfen."),
        "content_empty_flashcard": MessageLookupByLibrary.simpleMessage(
            "Zusammenfassung erscheint nach Meetingende."),
        "content_empty_quiz": MessageLookupByLibrary.simpleMessage(
            "Zusammenfassung erscheint nach Meetingende."),
        "content_hour":
            MessageLookupByLibrary.simpleMessage("Stunden Inhalt zu"),
        "content_hour_insight": MessageLookupByLibrary.simpleMessage(
            "Stunden Inhalt zu Erkenntnissen"),
        "content_minute_left": MessageLookupByLibrary.simpleMessage(
            "Aufnahmen werden lokal ohne KI gespeichert, wenn Gratis-Limit überschritten. Pro-Version für unbegrenzte Nutzung."),
        "content_payment_successfully": MessageLookupByLibrary.simpleMessage(
            "Vielen Dank für Ihren Kauf. Ihre Transaktion wurde erfolgreich verarbeitet."),
        "content_quarter_01": MessageLookupByLibrary.simpleMessage(
            "Unbegrenzte KI-Notizen aus Aufnahmen, Dateien hochladen, YouTube-Links."),
        "content_quarter_02": MessageLookupByLibrary.simpleMessage(
            "Unbegrenzte KI-Chat, Mind Maps, Lernkarten, Quiz, Notizen teilen."),
        "content_save_changes": MessageLookupByLibrary.simpleMessage(
            "Diese Aktion speichert alle Änderungen dauerhaft."),
        "continue_3_day":
            MessageLookupByLibrary.simpleMessage("3 Tage kostenlos testen"),
        "continue_button": MessageLookupByLibrary.simpleMessage("Weiter"),
        "continue_with_apple":
            MessageLookupByLibrary.simpleMessage("Mit Apple fortfahren"),
        "continue_with_email":
            MessageLookupByLibrary.simpleMessage("Mit E-Mail fortfahren"),
        "continue_with_google":
            MessageLookupByLibrary.simpleMessage("Mit Google fortfahren"),
        "copied_to_clipboard":
            MessageLookupByLibrary.simpleMessage("In Zwischenablage kopiert"),
        "copy": MessageLookupByLibrary.simpleMessage("Kopieren"),
        "copy_your_referral_code": MessageLookupByLibrary.simpleMessage(
            "Kopiere deinen Empfehlungscode."),
        "correct": MessageLookupByLibrary.simpleMessage("Richtig"),
        "craft_visual_from_every_note": MessageLookupByLibrary.simpleMessage(
            "Verwandle deine Notizen in Folien"),
        "craft_visual_stories":
            MessageLookupByLibrary.simpleMessage("Verwandle deine Notizen"),
        "create": MessageLookupByLibrary.simpleMessage("Erstellen"),
        "create_folder":
            MessageLookupByLibrary.simpleMessage("Ordner erstellen"),
        "create_lecture": MessageLookupByLibrary.simpleMessage(
            "Erstelle präzise Vorlesungsnotizen"),
        "create_new_folder":
            MessageLookupByLibrary.simpleMessage("Neuen Ordner erstellen"),
        "create_note_successfully":
            MessageLookupByLibrary.simpleMessage("Notiz erfolgreich erstellt!"),
        "create_notes": MessageLookupByLibrary.simpleMessage(
            "KI-Notizen werden erstellt.."),
        "create_podcast":
            MessageLookupByLibrary.simpleMessage("Podcast erstellen"),
        "create_reminder":
            MessageLookupByLibrary.simpleMessage("Erinnerung erstellen"),
        "create_select_a_language":
            MessageLookupByLibrary.simpleMessage("Sprache auswählen"),
        "create_short": MessageLookupByLibrary.simpleMessage("Short erstellen"),
        "create_shorts":
            MessageLookupByLibrary.simpleMessage("Shorts erstellen"),
        "create_slide":
            MessageLookupByLibrary.simpleMessage("Folienshow erstellen"),
        "creating_note":
            MessageLookupByLibrary.simpleMessage("Notiz wird erstellt ..."),
        "creating_quiz":
            MessageLookupByLibrary.simpleMessage("Quizfragen werden erstellt"),
        "credit": MessageLookupByLibrary.simpleMessage("Guthaben"),
        "credits": MessageLookupByLibrary.simpleMessage("Guthaben"),
        "credits_can_be_used_to_create_notes_and_access_the_features_within_them":
            MessageLookupByLibrary.simpleMessage(
                "Guthaben können verwendet werden, um Notizen zu erstellen und auf deren Funktionen zuzugreifen. Wenn dein Abo ausläuft, kannst du Guthaben verwenden, um weiterhin Aktionen auszuführen."),
        "credits_earned":
            MessageLookupByLibrary.simpleMessage("Guthaben erhalten"),
        "credits_premium_features": MessageLookupByLibrary.simpleMessage(
            "Guthaben und Premium-Funktionen frei."),
        "credits_used":
            MessageLookupByLibrary.simpleMessage("Guthaben verwendet"),
        "current_plan": MessageLookupByLibrary.simpleMessage("Aktueller Tarif"),
        "custom_note_tabs":
            MessageLookupByLibrary.simpleMessage("Notiz-Tabs anpassen"),
        "customize_note_tabs":
            MessageLookupByLibrary.simpleMessage("Notiz-Tabs anpassen"),
        "customize_your_note_view":
            MessageLookupByLibrary.simpleMessage("Passe deine Notizansicht an"),
        "daily_10": MessageLookupByLibrary.simpleMessage("10 täglich"),
        "daily_3": MessageLookupByLibrary.simpleMessage("3 täglich"),
        "daily_5": MessageLookupByLibrary.simpleMessage("5 täglich"),
        "daily_rewards_limit_reached": MessageLookupByLibrary.simpleMessage(
            "Tägliches Limit erreicht. Morgen wieder!"),
        "daily_shorts_limit_reached": MessageLookupByLibrary.simpleMessage(
            "Tägliches Shorts-Limit erreicht (Beta - Frühzugriff)"),
        "daily_shorts_limit_reached_detail": MessageLookupByLibrary.simpleMessage(
            "Du hast heute alle Shorts-Generierungen aufgebraucht. Diese Beta-Funktion hat tägliche Limits für einen stabilen Dienst. \nKomm morgen wieder, um mehr KI-Kurzvideos zu erstellen!"),
        "daily_slideshow_limit_reached":
            MessageLookupByLibrary.simpleMessage("Tägliche Limit erreicht"),
        "daily_slideshow_limit_reached_detail":
            MessageLookupByLibrary.simpleMessage(
                "Sie haben alle Folienshow-Generierungen für heute verbraucht. Diese Beta-Funktion hat tägliche Limits, um eine stabile Leistung zu gewährleisten. Kommen Sie morgen wieder, um weitere KI-gesteuerte Folienshows zu erstellen!"),
        "dark": MessageLookupByLibrary.simpleMessage("Dunkel"),
        "data": MessageLookupByLibrary.simpleMessage("Daten"),
        "day_free_trial_access_all_features":
            MessageLookupByLibrary.simpleMessage(
                "7-Tage kostenlose Testversion für alle Funktionen, dann nur "),
        "days": MessageLookupByLibrary.simpleMessage("Tage"),
        "db_err": MessageLookupByLibrary.simpleMessage(
            "Datenbankfehler. Später erneut versuchen."),
        "deals_left_at_this_price": MessageLookupByLibrary.simpleMessage(
            "Lifetime-Angebote zu diesem Preis"),
        "decline_free_trial":
            MessageLookupByLibrary.simpleMessage("Testversion ablehnen"),
        "default_error": MessageLookupByLibrary.simpleMessage(
            "Etwas ist schiefgelaufen! Erneut versuchen!"),
        "delete": MessageLookupByLibrary.simpleMessage("Löschen"),
        "delete_account": MessageLookupByLibrary.simpleMessage("Konto löschen"),
        "delete_account_detail": MessageLookupByLibrary.simpleMessage(
            "Diese Aktion kann nicht rückgängig gemacht werden. Löschen entfernt dauerhaft: Alle Notizen und Aufnahmen"),
        "delete_all_note": MessageLookupByLibrary.simpleMessage(
            "Alle Notizen im Ordner löschen"),
        "delete_folder": MessageLookupByLibrary.simpleMessage("Ordner löschen"),
        "delete_note":
            MessageLookupByLibrary.simpleMessage("Diese Notiz löschen?"),
        "delete_note_item":
            MessageLookupByLibrary.simpleMessage("Notiz löschen"),
        "delete_recording":
            MessageLookupByLibrary.simpleMessage("Aufzeichnung löschen"),
        "delete_recording_confirmation": MessageLookupByLibrary.simpleMessage(
            "Sind Sie sicher, dass Sie diese Aufzeichnung löschen möchten"),
        "delete_recording_setting_confirmation":
            MessageLookupByLibrary.simpleMessage(
                "Der Audio-Datei wird dauerhaft von Ihrem Gerät gelöscht. Diese Aktion kann nicht rückgängig gemacht werden."),
        "delete_reminder":
            MessageLookupByLibrary.simpleMessage("Erinnerung löschen?"),
        "delete_success": MessageLookupByLibrary.simpleMessage(
            "Die Notiz wurde erfolgreich gelöscht."),
        "delete_this_folder":
            MessageLookupByLibrary.simpleMessage("Diesen Ordner löschen?"),
        "delete_this_item":
            MessageLookupByLibrary.simpleMessage("Dieses Element löschen?"),
        "deselect": MessageLookupByLibrary.simpleMessage("Abwählen"),
        "detail_unlimited_ai_summaries": MessageLookupByLibrary.simpleMessage(
            "Unbegrenzte KI-Notizen aus Aufnahmen, Audio, Dokumenten und YouTube"),
        "developing_quizzes":
            MessageLookupByLibrary.simpleMessage("Quiz wird entwickelt.."),
        "discard": MessageLookupByLibrary.simpleMessage("Verwerfen"),
        "discard_changes":
            MessageLookupByLibrary.simpleMessage("Änderungen verwerfen?"),
        "dissatisfied": MessageLookupByLibrary.simpleMessage(
            "Danke für dein Feedback. Deine Eingabe hilft uns, unsere Ergebnisse zu verbessern. Wir arbeiten daran, deine Erfahrung zu optimieren!"),
        "doc": MessageLookupByLibrary.simpleMessage("Dokument"),
        "document": MessageLookupByLibrary.simpleMessage(
            "Dokument hochladen (Demnächst)"),
        "document_available": MessageLookupByLibrary.simpleMessage(
            "Das Dokument wird nach erfolgreicher Erstellung der Notiz verfügbar sein!"),
        "document_exceed_limit": MessageLookupByLibrary.simpleMessage(
            "Datei über 20MB. Bitte kleinere Datei wählen"),
        "document_limit":
            MessageLookupByLibrary.simpleMessage("Dokument-Upload-Limit"),
        "document_limit_message": MessageLookupByLibrary.simpleMessage(
            "Kostenlose Nutzer können 1 Dokument pro Tag zusammenfassen."),
        "document_note": MessageLookupByLibrary.simpleMessage("Dokument-Notiz"),
        "document_tab": MessageLookupByLibrary.simpleMessage("Dokument"),
        "document_to_ai_note":
            MessageLookupByLibrary.simpleMessage("Dokument zu KI-Notizen"),
        "document_type": MessageLookupByLibrary.simpleMessage(
            "Unterstützte Dateitypen: .pdf, .doc, .docx, .txt, .md"),
        "document_upload_note":
            MessageLookupByLibrary.simpleMessage("Dokument-Upload"),
        "document_webview_loading_message":
            MessageLookupByLibrary.simpleMessage(
                "Dokumentinhalt wird geladen..."),
        "done_button_label": MessageLookupByLibrary.simpleMessage("Fertig"),
        "donotallow": MessageLookupByLibrary.simpleMessage("Nicht erlauben"),
        "double_the_benefits":
            MessageLookupByLibrary.simpleMessage("Doppelte Vorteile!"),
        "download_audio_file":
            MessageLookupByLibrary.simpleMessage("Audio herunterladen"),
        "download_sucess":
            MessageLookupByLibrary.simpleMessage("Download erfolgreich"),
        "duration": MessageLookupByLibrary.simpleMessage("Dauer"),
        "each_ai_note_generation_uses_1_credit":
            MessageLookupByLibrary.simpleMessage(
                "Jede KI-Notiz-Erstellung verwendet 1 Guthaben"),
        "each_referral_earns":
            MessageLookupByLibrary.simpleMessage("Jede Empfehlung bringt"),
        "early_access": MessageLookupByLibrary.simpleMessage(
            "Frühzugriff auf \nzukünftige Features"),
        "early_supporters_exclusive_offer":
            MessageLookupByLibrary.simpleMessage("Vorabend-Angebot"),
        "easily_import_shared_note_link": MessageLookupByLibrary.simpleMessage(
            "Einfach geteilte Notizen-Links von Freunden importieren"),
        "easy": MessageLookupByLibrary.simpleMessage("Leicht"),
        "edit": MessageLookupByLibrary.simpleMessage("Bearbeiten"),
        "edit_folder":
            MessageLookupByLibrary.simpleMessage("Ordner bearbeiten"),
        "edit_folder_name":
            MessageLookupByLibrary.simpleMessage("Ordnername eingeben"),
        "edit_name": MessageLookupByLibrary.simpleMessage("Name ändern"),
        "edit_note": MessageLookupByLibrary.simpleMessage("Notiz bearbeiten"),
        "edit_notes": MessageLookupByLibrary.simpleMessage("Notiz bearbeiten"),
        "edit_reminder":
            MessageLookupByLibrary.simpleMessage("Erinnerung bearbeiten"),
        "edit_transcript":
            MessageLookupByLibrary.simpleMessage("Transkript bearbeiten"),
        "edit_transcript_json_fail": MessageLookupByLibrary.simpleMessage(
            "Bearbeitung des zeitgestempelten Transkripts fehlgeschlagen. Bitte erneut versuchen."),
        "edit_transcript_json_success": MessageLookupByLibrary.simpleMessage(
            "Zeitgestempeltes Transkript erfolgreich bearbeitet"),
        "email_invalid": MessageLookupByLibrary.simpleMessage(
            "Die E-Mail-Adresse ist ungültig."),
        "email_sent":
            MessageLookupByLibrary.simpleMessage("Posteingang prüfen"),
        "email_sent_success": MessageLookupByLibrary.simpleMessage(
            "Wir haben dir einen Magic-Link gesendet. Klicke in deiner E-Mail darauf, um dich einzuloggen."),
        "enable_free":
            MessageLookupByLibrary.simpleMessage("Testphase aktivieren"),
        "enables_swap": MessageLookupByLibrary.simpleMessage(
            "Ermöglicht die Neuanordnung von Bildern durch Auswahl und Austausch"),
        "english": MessageLookupByLibrary.simpleMessage("Englisch"),
        "enter_card_count":
            MessageLookupByLibrary.simpleMessage("Kartenzahl eingeben"),
        "enter_email": MessageLookupByLibrary.simpleMessage(
            "Gib deine E-Mail-Adresse ein..."),
        "enter_feedback":
            MessageLookupByLibrary.simpleMessage("Feedback eingeben"),
        "enter_folder_name":
            MessageLookupByLibrary.simpleMessage("Ordnername eingeben"),
        "enter_new_name":
            MessageLookupByLibrary.simpleMessage("Neuen Namen eingeben"),
        "enter_quiz_count":
            MessageLookupByLibrary.simpleMessage("Quizanzahl eingeben"),
        "enter_referral_code":
            MessageLookupByLibrary.simpleMessage("Empfehlungscode eingeben"),
        "enter_slide_count": MessageLookupByLibrary.simpleMessage(
            "Geben Sie die Anzahl der Folien ein"),
        "enter_title": MessageLookupByLibrary.simpleMessage("Titel eingeben"),
        "enter_valid_email": MessageLookupByLibrary.simpleMessage(
            "Bitte gib eine gültige E-Mail-Adresse ein"),
        "error": MessageLookupByLibrary.simpleMessage("Fehler"),
        "error_connection": MessageLookupByLibrary.simpleMessage(
            "Verbindungsfehler.\nBitte erneut versuchen"),
        "error_convert_image":
            MessageLookupByLibrary.simpleMessage("Bildkonvertierungsfehler"),
        "error_logging_in":
            MessageLookupByLibrary.simpleMessage("Mit Internet verbinden"),
        "esc": MessageLookupByLibrary.simpleMessage("Esc"),
        "essential": MessageLookupByLibrary.simpleMessage("Essential"),
        "essential_lifetime":
            MessageLookupByLibrary.simpleMessage("Essential Lifetime"),
        "essential_lifetime_access":
            MessageLookupByLibrary.simpleMessage("Essential Lifetime Access"),
        "evening_content": MessageLookupByLibrary.simpleMessage(
            "Reflektieren, erfassen, wachsen"),
        "evening_content_2": MessageLookupByLibrary.simpleMessage(
            "Die Erkenntnisse des Tages bewahrt"),
        "evening_content_3": MessageLookupByLibrary.simpleMessage(
            "Morgen beginnt mit den Notizen von heute"),
        "evening_content_4": MessageLookupByLibrary.simpleMessage(
            "Gedanken geordnet, Geist entspannt"),
        "evening_content_5": MessageLookupByLibrary.simpleMessage(
            "Jetzt speichern, später danken"),
        "evening_content_6":
            MessageLookupByLibrary.simpleMessage("Fortschritt bewahrt"),
        "every_note_you_take":
            MessageLookupByLibrary.simpleMessage("in Folien"),
        "experience": MessageLookupByLibrary.simpleMessage("Erlebnis"),
        "export": MessageLookupByLibrary.simpleMessage("Exportieren"),
        "export_as": MessageLookupByLibrary.simpleMessage("Als exportieren"),
        "export_audio":
            MessageLookupByLibrary.simpleMessage("Audiodatei exportieren"),
        "export_failed": MessageLookupByLibrary.simpleMessage(
            "Export fehlgeschlagen. Bitte versuchen Sie es später erneut."),
        "export_flashcard":
            MessageLookupByLibrary.simpleMessage("Lernkarte exportieren"),
        "export_mind_map":
            MessageLookupByLibrary.simpleMessage("Mindmap exportieren als"),
        "export_pdf":
            MessageLookupByLibrary.simpleMessage("Zusammenfassung exportieren"),
        "export_quiz": MessageLookupByLibrary.simpleMessage("Quiz exportieren"),
        "export_to_pdf_share_notes": MessageLookupByLibrary.simpleMessage(
            "Als PDF exportieren & Notizen teilen"),
        "export_transcript":
            MessageLookupByLibrary.simpleMessage("Transkript exportieren"),
        "extracting_text_from_document": MessageLookupByLibrary.simpleMessage(
            "Text wird aus Dokument extrahiert"),
        "fail": MessageLookupByLibrary.simpleMessage("Fehlgeschlagen"),
        "fail_create_pdf": MessageLookupByLibrary.simpleMessage(
            "PDF-Erstellung fehlgeschlagen"),
        "fail_to_load_document": MessageLookupByLibrary.simpleMessage(
            "Dokument konnte nicht geladen werden!"),
        "fail_to_load_video": MessageLookupByLibrary.simpleMessage(
            "Video konnte nicht geladen werden"),
        "failed_get_anonymous_user": MessageLookupByLibrary.simpleMessage(
            "JWT-Anonymer Nutzer nicht verfügbar"),
        "failed_to_delete_recording": MessageLookupByLibrary.simpleMessage(
            "Fehler beim Löschen der Aufzeichnung"),
        "failed_to_load_slideshow": MessageLookupByLibrary.simpleMessage(
            "Das Präsentationsfolienset konnte nicht aus dem System geladen werden. Versuchen Sie es erneut für eine bessere Erfahrung."),
        "failed_to_save_file": MessageLookupByLibrary.simpleMessage(
            "Datei konnte nicht gespeichert werden"),
        "feedback": MessageLookupByLibrary.simpleMessage("Feedback"),
        "file_import": MessageLookupByLibrary.simpleMessage("Datei-Import"),
        "file_save_success": MessageLookupByLibrary.simpleMessage(
            "Datei erfolgreich gespeichert"),
        "file_size_err": MessageLookupByLibrary.simpleMessage(
            "Datei zu groß. Kleinere Datei hochladen."),
        "filter": MessageLookupByLibrary.simpleMessage("Filter"),
        "filter_and_sort":
            MessageLookupByLibrary.simpleMessage("Filter & Sortierung"),
        "finalizing":
            MessageLookupByLibrary.simpleMessage("Wird fertiggestellt.."),
        "find_and_replace":
            MessageLookupByLibrary.simpleMessage("Suchen und Ersetzen"),
        "flash_card_gen_success": MessageLookupByLibrary.simpleMessage(
            "Lernkarten erfolgreich erstellt"),
        "flash_card_iap":
            MessageLookupByLibrary.simpleMessage("Lernkarten-Set"),
        "flashcard": MessageLookupByLibrary.simpleMessage("Lernkarte"),
        "flashcard_set_not_found": MessageLookupByLibrary.simpleMessage(
            "Lernkartenset nicht gefunden"),
        "flashcard_sets":
            MessageLookupByLibrary.simpleMessage("Karteikartensätze"),
        "flashcards": MessageLookupByLibrary.simpleMessage("Karteikarten"),
        "flashcards_for":
            MessageLookupByLibrary.simpleMessage("Karteikarten für"),
        "focus_on":
            MessageLookupByLibrary.simpleMessage("Fokus aufs Wesentliche"),
        "folder": MessageLookupByLibrary.simpleMessage("Ordner"),
        "follow_steps_to_get_rewarded": MessageLookupByLibrary.simpleMessage(
            "Folge diesen Schritten für deine Belohnung"),
        "for_unlimited_experiences": MessageLookupByLibrary.simpleMessage(
            "für unbegrenzte Erfahrungen."),
        "free": MessageLookupByLibrary.simpleMessage("Kostenlos"),
        "free_30_minutes": MessageLookupByLibrary.simpleMessage(
            "Kostenlos: 30 Minuten pro Datei"),
        "free_messages":
            MessageLookupByLibrary.simpleMessage("kostenlose Nachrichten"),
        "free_recording_limit":
            MessageLookupByLibrary.simpleMessage("Aufnahmelimit (kostenlos)"),
        "free_recording_limit_details": MessageLookupByLibrary.simpleMessage(
            "Sie haben noch %s Minuten kostenlose Transkription und KI-Zusammenfassung diese Woche."),
        "free_trial":
            MessageLookupByLibrary.simpleMessage("Kostenlose Testphase"),
        "free_updates": MessageLookupByLibrary.simpleMessage(
            "Kostenlose lebenslange Updates und Verbesserungen"),
        "free_usage": MessageLookupByLibrary.simpleMessage(
            "Gratis-Nutzung - Wöchentlich"),
        "free_user_audio": MessageLookupByLibrary.simpleMessage(
            "Kostenlose Nutzer können bis zu 30 Minuten Audio verarbeiten"),
        "free_user_can": MessageLookupByLibrary.simpleMessage(
            "Kostenlose Nutzer können 1 YouTube-Video (max. 30 Min) pro Tag zusammenfassen."),
        "friendly": MessageLookupByLibrary.simpleMessage("Freundlich"),
        "friendly_description":
            MessageLookupByLibrary.simpleMessage("Gesprächig mit Emojis"),
        "front_content": MessageLookupByLibrary.simpleMessage("Du hast "),
        "future_features": MessageLookupByLibrary.simpleMessage(
            "Zukünftige Funktionen könnten Nutzungslimits haben"),
        "gen_ai": MessageLookupByLibrary.simpleMessage("KI wird generiert..."),
        "gen_ai_voice":
            MessageLookupByLibrary.simpleMessage("KI-Stimme wird generiert"),
        "gen_quiz_bgr": MessageLookupByLibrary.simpleMessage(
            "Quiz-Hintergrund wird generiert"),
        "generate_audio":
            MessageLookupByLibrary.simpleMessage("Audio generieren"),
        "generate_content": MessageLookupByLibrary.simpleMessage(
            "Erstelle smarte YouTube-Zusammenfassungen"),
        "generate_note_fail": MessageLookupByLibrary.simpleMessage(
            "KI-Notizen konnten nicht erstellt werden!"),
        "generate_shorts_step_1": MessageLookupByLibrary.simpleMessage(
            "Deine Geschichte wird gestaltet..."),
        "generate_shorts_step_2": MessageLookupByLibrary.simpleMessage(
            "Perfekte Stimme wird hinzugefügt..."),
        "generate_shorts_step_3": MessageLookupByLibrary.simpleMessage(
            "Es wird toll aussehen! Dieses Video ist teilenswert #NoteXAI"),
        "generate_shorts_study_guides": MessageLookupByLibrary.simpleMessage(
            "Shorts & Lernhilfen generieren"),
        "generate_transcript_notes": MessageLookupByLibrary.simpleMessage(
            "Wir erstellen ein Transkript, Notizen und eine Lernhilfe"),
        "generate_video":
            MessageLookupByLibrary.simpleMessage("Video generieren"),
        "generating_ai_note":
            MessageLookupByLibrary.simpleMessage("KI-Notiz wird erstellt"),
        "generating_summary": MessageLookupByLibrary.simpleMessage(
            "KI-Zusammenfassung wird erstellt.."),
        "get_fail": MessageLookupByLibrary.simpleMessage(
            "Quiz/Lernkarten/Mindmap-Abruf fehlgeschlagen. Erneut versuchen!"),
        "get_more_done": MessageLookupByLibrary.simpleMessage("Mehr erreichen"),
        "get_more_done_stay_on_track":
            MessageLookupByLibrary.simpleMessage("Mehr erreichen, Kurs halten"),
        "get_now": MessageLookupByLibrary.simpleMessage("Jetzt holen"),
        "get_offer_now":
            MessageLookupByLibrary.simpleMessage("Jetzt Angebot bekommen"),
        "get_start": MessageLookupByLibrary.simpleMessage("Loslegen"),
        "go_back": MessageLookupByLibrary.simpleMessage("Zurück"),
        "go_email": MessageLookupByLibrary.simpleMessage("E-Mail öffnen"),
        "go_pro": MessageLookupByLibrary.simpleMessage("Pro werden"),
        "go_unlimited":
            MessageLookupByLibrary.simpleMessage("Unlimited freischalten!"),
        "good_afternoon": MessageLookupByLibrary.simpleMessage("Guten Tag!"),
        "good_evening": MessageLookupByLibrary.simpleMessage("Guten Abend!"),
        "good_morning": MessageLookupByLibrary.simpleMessage("Guten Morgen!"),
        "got_it": MessageLookupByLibrary.simpleMessage("Verstanden!"),
        "hard": MessageLookupByLibrary.simpleMessage("Schwer"),
        "hello_welcome":
            MessageLookupByLibrary.simpleMessage("Willkommen zurück 👋"),
        "help_legal":
            MessageLookupByLibrary.simpleMessage("Hilfe & Rechtliches"),
        "help_us_grow":
            MessageLookupByLibrary.simpleMessage("Hilf uns wachsen!"),
        "hi": MessageLookupByLibrary.simpleMessage("Hallo"),
        "hope_enjoy_app": MessageLookupByLibrary.simpleMessage(
            "Wir hoffen, unsere App gefällt Ihnen. Danke für Ihre Unterstützung!"),
        "hours": MessageLookupByLibrary.simpleMessage("Stunden"),
        "how_will_you_use_notex":
            MessageLookupByLibrary.simpleMessage("Wie nutzen Sie NoteX?"),
        "http_failed": MessageLookupByLibrary.simpleMessage(
            "HTTP-Anfrage fehlgeschlagen. Bitte versuchen Sie es später erneut."),
        "idea1": MessageLookupByLibrary.simpleMessage(
            "Mit Google oder Apple anmelden, falls noch nicht geschehen"),
        "idea2":
            MessageLookupByLibrary.simpleMessage("Kurze Problembeschreibung"),
        "idea3": MessageLookupByLibrary.simpleMessage(
            "Relevante Details (Gerät, OS-Version)"),
        "idea4": MessageLookupByLibrary.simpleMessage("Zeitpunkt des Problems"),
        "idea5": MessageLookupByLibrary.simpleMessage("E-Mail direkt an"),
        "image": MessageLookupByLibrary.simpleMessage("Bild"),
        "image_jpeg": MessageLookupByLibrary.simpleMessage("Bild (.jpeg)"),
        "image_png": MessageLookupByLibrary.simpleMessage("Bild (.png)"),
        "image_quality_too_low": MessageLookupByLibrary.simpleMessage(
            "Bildqualität zu niedrig. Besseres Bild verwenden!"),
        "image_too_large": MessageLookupByLibrary.simpleMessage(
            "Bild zu groß. Max. 10MB erlaubt."),
        "images_have_been_uploaded": m1,
        "import_note_links":
            MessageLookupByLibrary.simpleMessage("Notizen-Links importieren"),
        "import_notes": MessageLookupByLibrary.simpleMessage(
            "Geteilte Notizen importieren"),
        "improve_responses":
            MessageLookupByLibrary.simpleMessage("Deine Antworten verbessern"),
        "initializing_camera": MessageLookupByLibrary.simpleMessage(
            "Kamera wird initialisiert..."),
        "insight_instantly": MessageLookupByLibrary.simpleMessage(
            "Stunden Inhalt sofort zu Erkenntnissen"),
        "insights_instantly":
            MessageLookupByLibrary.simpleMessage("Erkenntnissen sofort"),
        "instant_answers_from_your":
            MessageLookupByLibrary.simpleMessage("Sofortantworten aus"),
        "instant_answers_from_your_meeting_data":
            MessageLookupByLibrary.simpleMessage(
                "Sofortantworten aus Meetingdaten"),
        "instant_answers_meeting":
            MessageLookupByLibrary.simpleMessage("Sofortantworten vom Meeting"),
        "instantly": MessageLookupByLibrary.simpleMessage("sofort"),
        "interactive_ai_flashcards":
            MessageLookupByLibrary.simpleMessage("Interaktive KI-Mindmap"),
        "interactive_flash":
            MessageLookupByLibrary.simpleMessage("Interaktive Lernkarten"),
        "interactive_flashcards": MessageLookupByLibrary.simpleMessage(
            "Unbegrenzte Lernkarten & Mindmaps"),
        "interactive_flashcards_quiz":
            MessageLookupByLibrary.simpleMessage("Lernkarten & Quiz"),
        "introduce_guidance": MessageLookupByLibrary.simpleMessage(
            "Danke für deine Nachricht. Für eine schnelle Problemlösung, folge bitte diesen Schritten:"),
        "introduce_guidance2": MessageLookupByLibrary.simpleMessage(
            "Wir verstehen, wie frustrierend Probleme sein können, besonders bei wichtigen Notizen. Unser Support-Team hilft meist innerhalb von 12 Stunden."),
        "inv_audio": MessageLookupByLibrary.simpleMessage(
            "Ungültige Audiodatei. Unterstütztes Format verwenden."),
        "inv_yt_url": MessageLookupByLibrary.simpleMessage(
            "Ungültige YouTube-URL. Gültigen Link eingeben."),
        "invalid_code": MessageLookupByLibrary.simpleMessage(
            "Ungültiger Code. Versuche es erneut."),
        "invalid_file_type": MessageLookupByLibrary.simpleMessage(
            "Falsches Dateiformat. Bitte erneut hochladen."),
        "invalid_token":
            MessageLookupByLibrary.simpleMessage("Ungültiger Token"),
        "invite_friends": MessageLookupByLibrary.simpleMessage(
            "Lade Freunde ein - ihr beide schaltet extra"),
        "join_discord":
            MessageLookupByLibrary.simpleMessage("Discord beitreten"),
        "join_noteX_ai_lets_level_up_together":
            MessageLookupByLibrary.simpleMessage(
                "Tritt NoteX AI bei und lass uns gemeinsam aufleveln!"),
        "language": MessageLookupByLibrary.simpleMessage("Sprache"),
        "language_tip": MessageLookupByLibrary.simpleMessage(
            "Hauptsprache für beste Ergebnisse wählen"),
        "language_tip_1": MessageLookupByLibrary.simpleMessage(
            "Hauptsprache für beste Ergebnisse wählen"),
        "language_tip_2": MessageLookupByLibrary.simpleMessage(
            "Für gemischte Gespräche Mehrsprachig wählen"),
        "language_tip_3": MessageLookupByLibrary.simpleMessage(
            "Anrufe pausieren Aufnahme. App neu öffnen zum Fortsetzen"),
        "latest_ai_models":
            MessageLookupByLibrary.simpleMessage("Neueste KI-Modelle"),
        "learn_faster_through":
            MessageLookupByLibrary.simpleMessage("Schneller lernen durch"),
        "learn_faster_through_active_recall":
            MessageLookupByLibrary.simpleMessage(
                "Schneller lernen durch aktives Erinnern"),
        "learn_smart": MessageLookupByLibrary.simpleMessage("Smart Lernen "),
        "learn_unlimited":
            MessageLookupByLibrary.simpleMessage("Smart lernen - unbegrenzt!"),
        "lecture_notes_study_materials": MessageLookupByLibrary.simpleMessage(
            "Vorlesungsnotizen & Material"),
        "let_ai_handle":
            MessageLookupByLibrary.simpleMessage("Lass KI die Details regeln"),
        "let_note_ai":
            MessageLookupByLibrary.simpleMessage("Lass NoteX KI Informationen"),
        "let_start": MessageLookupByLibrary.simpleMessage("Los geht\'s"),
        "lets_create_your_first_ai_note": MessageLookupByLibrary.simpleMessage(
            "Lass uns deine erste KI-Notiz erstellen!"),
        "lifetime": MessageLookupByLibrary.simpleMessage("Lebenslang-Tarif"),
        "lifetime_pro_access_level_up_together":
            MessageLookupByLibrary.simpleMessage(
                "Pro-Zugang auf Lebenszeit zu gewinnen! Gemeinsam aufleveln ✨"),
        "lifetime_setting":
            MessageLookupByLibrary.simpleMessage("auf Lebenszeit"),
        "lifetime_spots_remaining": MessageLookupByLibrary.simpleMessage(
            "lebenslange Plätze verfügbar"),
        "light": MessageLookupByLibrary.simpleMessage("Hell"),
        "limited_notes":
            MessageLookupByLibrary.simpleMessage("Begrenzte Notizen pro Tag"),
        "limited_offer":
            MessageLookupByLibrary.simpleMessage("Begrenztes Angebot"),
        "limited_time":
            MessageLookupByLibrary.simpleMessage("Zeitlich begrenzt"),
        "link": MessageLookupByLibrary.simpleMessage("Link"),
        "link_error": MessageLookupByLibrary.simpleMessage("Fehler beim Link"),
        "link_expired": MessageLookupByLibrary.simpleMessage(
            "Der E-Mail-Link ist abgelaufen."),
        "link_invalid": MessageLookupByLibrary.simpleMessage(
            "Der verwendete Link ist ungültig, abgelaufen oder wurde bereits genutzt. Bitte fordere einen neuen Link an und versuche es erneut."),
        "loading": MessageLookupByLibrary.simpleMessage("Lädt"),
        "loading_content":
            MessageLookupByLibrary.simpleMessage("Inhalt Wird Geladen..."),
        "local_recording":
            MessageLookupByLibrary.simpleMessage("Smart-Aufnahme"),
        "login_failed":
            MessageLookupByLibrary.simpleMessage("Login fehlgeschlagen."),
        "login_info_1": MessageLookupByLibrary.simpleMessage(
            "Greifen Sie von jedem Gerät auf Ihre Notizen zu"),
        "login_info_2": MessageLookupByLibrary.simpleMessage(
            "Unternehmenssichere Sicherheit powered by AWS"),
        "login_info_3":
            MessageLookupByLibrary.simpleMessage("Ihre Daten bleiben privat"),
        "login_info_4": MessageLookupByLibrary.simpleMessage(
            "Zugriff im Web unter notexapp.com"),
        "login_success":
            MessageLookupByLibrary.simpleMessage("Anmeldung erfolgreich!"),
        "login_title": MessageLookupByLibrary.simpleMessage(
            "Produktivität maximieren, überall!"),
        "login_title_2":
            MessageLookupByLibrary.simpleMessage("Willkommen bei NoteX 2.0"),
        "login_unsuccessful": MessageLookupByLibrary.simpleMessage(
            "Anmeldung fehlgeschlagen. Erneut versuchen oder andere Methode wählen"),
        "logout": MessageLookupByLibrary.simpleMessage("Abmelden"),
        "logout_detail": MessageLookupByLibrary.simpleMessage(
            "Sind Sie sicher, dass Sie sich abmelden möchten?"),
        "logout_question_mark":
            MessageLookupByLibrary.simpleMessage("Abmelden?"),
        "loved_by": MessageLookupByLibrary.simpleMessage("Geliebt von "),
        "make_the_interface_feel_more_like_you":
            MessageLookupByLibrary.simpleMessage(
                "Gestalten Sie die Benutzeroberfläche so, dass sie mehr wie Sie wirkt — mit Thema-, Schrift- und Layout-Einstellungen direkt zur Hand."),
        "making_amazing": MessageLookupByLibrary.simpleMessage(
            "Wir machen es fantastisch! Dieses Quiz-Video wird es wert sein, geteilt zu werden #NoteXAI"),
        "manage_recordings":
            MessageLookupByLibrary.simpleMessage("Aufzeichnungen verwalten"),
        "map_all_together": MessageLookupByLibrary.simpleMessage(
            "Alle Elemente werden zusammengefügt"),
        "markdown_md": MessageLookupByLibrary.simpleMessage("Markdown (.md)"),
        "max_30_cards_per_set":
            MessageLookupByLibrary.simpleMessage("Maximal 30 Karten pro Satz"),
        "max_30_quiz_sets":
            MessageLookupByLibrary.simpleMessage("Maximal 30 Fragen pro Set"),
        "max_3_flashcard_sets":
            MessageLookupByLibrary.simpleMessage("Maximal 3 Karteikartensätze"),
        "max_3_quiz_sets":
            MessageLookupByLibrary.simpleMessage("Maximal 3 Quiz-Sets"),
        "max_60_min_per_file":
            MessageLookupByLibrary.simpleMessage("* max 60 min pro Datei"),
        "max_ai":
            MessageLookupByLibrary.simpleMessage("Max. KI-Transkription:"),
        "maybe_later":
            MessageLookupByLibrary.simpleMessage("Später vielleicht"),
        "medium": MessageLookupByLibrary.simpleMessage("Mittel"),
        "meeting_data": MessageLookupByLibrary.simpleMessage("Meetingdaten"),
        "migrating_your_notes": MessageLookupByLibrary.simpleMessage(
            "Notizen werden übertragen..."),
        "migration_complete":
            MessageLookupByLibrary.simpleMessage("Übertragung abgeschlossen!"),
        "mind_map": MessageLookupByLibrary.simpleMessage("Mindmap"),
        "mind_map_gen_success": MessageLookupByLibrary.simpleMessage(
            "Mindmap erfolgreich erstellt"),
        "mind_map_iap": MessageLookupByLibrary.simpleMessage("Mindmap"),
        "mind_map_study":
            MessageLookupByLibrary.simpleMessage("Mindmap, \nLernhilfen"),
        "minute_60_per_file": MessageLookupByLibrary.simpleMessage(
            "Essential: 60 Min. pro Datei"),
        "minute_free": MessageLookupByLibrary.simpleMessage(
            "30-Minuten Gratis-Transkription aufgebraucht. Pro-Version für unbegrenzten Zugang oder bis nächste Woche warten."),
        "minutes": MessageLookupByLibrary.simpleMessage("Minuten"),
        "minutes_free_left":
            MessageLookupByLibrary.simpleMessage(" Gratis-Minuten übrig"),
        "minutes_remaining":
            MessageLookupByLibrary.simpleMessage("Minuten übrig"),
        "mixed": MessageLookupByLibrary.simpleMessage("Gemischt"),
        "month": MessageLookupByLibrary.simpleMessage("Monat"),
        "monthly": MessageLookupByLibrary.simpleMessage("Monatlich"),
        "more_summarize": MessageLookupByLibrary.simpleMessage(
            "Meetings, Podcasts und mehr zusammenfassen"),
        "morning_content": MessageLookupByLibrary.simpleMessage(
            "Fange den Glanz des Tages ein"),
        "morning_content_2":
            MessageLookupByLibrary.simpleMessage("Klarer Kopf, klarer Weg"),
        "morning_content_3": MessageLookupByLibrary.simpleMessage(
            "Heutige Notizen formen das Morgen"),
        "morning_content_4": MessageLookupByLibrary.simpleMessage(
            "Erster Gedanke, bester Gedanke"),
        "morning_content_5":
            MessageLookupByLibrary.simpleMessage("Mit Klarheit beginnen"),
        "morning_content_6":
            MessageLookupByLibrary.simpleMessage("Ideen, die es wert sind"),
        "most_popular": MessageLookupByLibrary.simpleMessage("Beliebteste"),
        "multi_language": MessageLookupByLibrary.simpleMessage("Mehrsprachig"),
        "multiple_people_detected": MessageLookupByLibrary.simpleMessage(
            "Mehrere Personen erkannt. Einzelperson-Bild verwenden!"),
        "multiply_knowledge_with_friends":
            MessageLookupByLibrary.simpleMessage("Wissen mit Freunden teilen"),
        "my_notes": MessageLookupByLibrary.simpleMessage("Meine Notizen"),
        "name": MessageLookupByLibrary.simpleMessage("Name"),
        "network_error": MessageLookupByLibrary.simpleMessage(
            "Netzwerkfehler. Bitte überprüfe deine Internetverbindung und versuche es erneut."),
        "neutral": MessageLookupByLibrary.simpleMessage("Neutral"),
        "neutral_description": MessageLookupByLibrary.simpleMessage(
            "Sachliche, faktische Darstellung"),
        "new_new": MessageLookupByLibrary.simpleMessage("Neu"),
        "new_note": MessageLookupByLibrary.simpleMessage("Neue Notiz"),
        "new_recording":
            MessageLookupByLibrary.simpleMessage("Neue Aufnahme - "),
        "newest_first": MessageLookupByLibrary.simpleMessage("Neueste zuerst"),
        "next_bill_date": m2,
        "no": MessageLookupByLibrary.simpleMessage("Nein"),
        "no_generated": MessageLookupByLibrary.simpleMessage(
            "Kein Quiz/Lernkarten erstellt. Jetzt tippen zum Erstellen!"),
        "no_input": MessageLookupByLibrary.simpleMessage(
            "Keine Eingabe. Audio oder YouTube-URL erforderlich."),
        "no_internet":
            MessageLookupByLibrary.simpleMessage("Keine Internetverbindung"),
        "no_internet_connection":
            MessageLookupByLibrary.simpleMessage("Keine Internetverbindung!"),
        "no_notes_found": MessageLookupByLibrary.simpleMessage(
            "Keine Notizen mit diesem Filter.\nBitte Filter zurücksetzen"),
        "no_notes_in_folder": MessageLookupByLibrary.simpleMessage(
            "Keine Notizen in diesem Ordner."),
        "no_payment_now":
            MessageLookupByLibrary.simpleMessage("✓ Jetzt keine Zahlung"),
        "no_person_detected": MessageLookupByLibrary.simpleMessage(
            "Keine Person erkannt. Bild mit Person hochladen!"),
        "no_recording_credit": MessageLookupByLibrary.simpleMessage(
            "Aufnahme-Guthaben aufgebraucht. Bitte upgraden."),
        "no_recordings": MessageLookupByLibrary.simpleMessage(
            "Keine Aufzeichnungen gefunden"),
        "no_results_found":
            MessageLookupByLibrary.simpleMessage("Keine Ergebnisse für"),
        "no_speech_detected":
            MessageLookupByLibrary.simpleMessage("Keine Sprache erkannt"),
        "no_summary": MessageLookupByLibrary.simpleMessage(
            "Keine Zusammenfassung verfügbar."),
        "no_transcript":
            MessageLookupByLibrary.simpleMessage("Kein Transkript verfügbar."),
        "no_upload_credit": MessageLookupByLibrary.simpleMessage(
            "Upload-Guthaben aufgebraucht. Bitte upgraden."),
        "no_url_provided": MessageLookupByLibrary.simpleMessage(
            "Keine Export-URL bereitgestellt."),
        "no_voice_available":
            MessageLookupByLibrary.simpleMessage("Keine Stimme verfügbar"),
        "not_found_audio":
            MessageLookupByLibrary.simpleMessage("Audiodatei nicht gefunden"),
        "not_open_mail": MessageLookupByLibrary.simpleMessage(
            "E-Mail konnte nicht geöffnet werden!"),
        "not_open_web": MessageLookupByLibrary.simpleMessage(
            "Web konnte nicht geöffnet werden!"),
        "not_summarized_note": MessageLookupByLibrary.simpleMessage(
            "Zusammenfassung fehlt! KI mit Knopfdruck starten👇"),
        "note": MessageLookupByLibrary.simpleMessage("Notiz"),
        "noteX": MessageLookupByLibrary.simpleMessage("NoteX"),
        "noteX_lifetime_essential":
            MessageLookupByLibrary.simpleMessage("NoteX Essential Lifetime"),
        "noteX_pro_lifetime":
            MessageLookupByLibrary.simpleMessage("NoteX Pro a Vida"),
        "note_404": MessageLookupByLibrary.simpleMessage(
            "Notiz nicht gefunden. ID prüfen und erneut versuchen."),
        "note_not_ready": MessageLookupByLibrary.simpleMessage(
            "Notiz ist nicht zum Exportieren bereit. Bitte warten Sie, bis die Verarbeitung abgeschlossen ist."),
        "note_reminders": MessageLookupByLibrary.simpleMessage("Erinnerungen"),
        "note_sharing": MessageLookupByLibrary.simpleMessage("Notizen teilen"),
        "note_tabs": MessageLookupByLibrary.simpleMessage("Notiz-Tabs"),
        "note_taker":
            MessageLookupByLibrary.simpleMessage("#1 KI-Notizassistent"),
        "notes": MessageLookupByLibrary.simpleMessage("Notizen"),
        "notex_empty": MessageLookupByLibrary.simpleMessage("NoteX leer"),
        "notex_experience":
            MessageLookupByLibrary.simpleMessage("deine NoteX-Erfahrung"),
        "nothing_restore":
            MessageLookupByLibrary.simpleMessage("Nichts wiederherzustellen"),
        "noti_default_description": MessageLookupByLibrary.simpleMessage(
            "Bereitmachen und starten Sie die Aufnahme! 🚀"),
        "noti_default_title": MessageLookupByLibrary.simpleMessage(
            "Es ist Zeit für die Aufnahme"),
        "noti_req_description": MessageLookupByLibrary.simpleMessage(
            "Benachrichtigungen können Alarme, Töne und Symbolabzeichen enthalten. Diese können in den Einstellungen konfiguriert werden."),
        "noti_req_title": MessageLookupByLibrary.simpleMessage(
            "‘NoteX’ Möchte Ihnen Benachrichtigungen Senden"),
        "notifications":
            MessageLookupByLibrary.simpleMessage("Benachrichtigungen"),
        "notifications_note_created": MessageLookupByLibrary.simpleMessage(
            "Ihre Notizen wurden erfolgreich erstellt"),
        "notifications_note_ready": MessageLookupByLibrary.simpleMessage(
            "Benachrichtigen, wenn Notizen fertig sind"),
        "nova_ai_assistant_mind_mapping": MessageLookupByLibrary.simpleMessage(
            "Nova KI-Assistent & Mindmapping"),
        "nova_ai_chat": MessageLookupByLibrary.simpleMessage("Nova KI-Chat"),
        "nova_chat": MessageLookupByLibrary.simpleMessage("Nova Chat"),
        "of_index": MessageLookupByLibrary.simpleMessage("von"),
        "of_user": MessageLookupByLibrary.simpleMessage(" der Nutzer"),
        "offer_expires":
            MessageLookupByLibrary.simpleMessage("Zeitbegrenztes Angebot"),
        "ok": MessageLookupByLibrary.simpleMessage("OK"),
        "oldest_first": MessageLookupByLibrary.simpleMessage("Älteste zuerst"),
        "on_track": MessageLookupByLibrary.simpleMessage("Kurs halten"),
        "on_your_android": MessageLookupByLibrary.simpleMessage(
            "Öffnen Sie auf Ihrem Android-Gerät den Google Play Store"),
        "onboarding_generate_audio_video_content":
            MessageLookupByLibrary.simpleMessage("Notizen umwandeln in"),
        "onboarding_generate_audio_video_full_content":
            MessageLookupByLibrary.simpleMessage(
                "Notizen in fesselnden Inhalt umwandeln"),
        "onboarding_generate_audio_video_sub_content":
            MessageLookupByLibrary.simpleMessage("fesselnden Inhalt"),
        "onboarding_generate_audio_video_title":
            MessageLookupByLibrary.simpleMessage("Audio & Video erstellen"),
        "once_in_a_lifetime_offer":
            MessageLookupByLibrary.simpleMessage("Einmalige Angebote"),
        "one_per_day": MessageLookupByLibrary.simpleMessage("1 pro Tag"),
        "one_time_payment":
            MessageLookupByLibrary.simpleMessage("Einmalige Zahlung"),
        "only": MessageLookupByLibrary.simpleMessage("Nur noch"),
        "only_today": MessageLookupByLibrary.simpleMessage("Nur heute"),
        "oops_something_went_wrong": MessageLookupByLibrary.simpleMessage(
            "Hoppla!\nEtwas ist schiefgelaufen"),
        "open_now": MessageLookupByLibrary.simpleMessage("Jetzt öffnen"),
        "open_youtube": MessageLookupByLibrary.simpleMessage("YouTube öffnen"),
        "opportunities": MessageLookupByLibrary.simpleMessage("Chancen"),
        "or": MessageLookupByLibrary.simpleMessage("oder"),
        "or_upper": MessageLookupByLibrary.simpleMessage("Oder"),
        "organize_assign_action_items":
            MessageLookupByLibrary.simpleMessage("Aufgaben organisieren"),
        "organize_assign_items":
            MessageLookupByLibrary.simpleMessage("Aufgaben organisieren"),
        "others": MessageLookupByLibrary.simpleMessage("Sonstiges"),
        "output_language":
            MessageLookupByLibrary.simpleMessage("Ausgabesprache"),
        "pace": MessageLookupByLibrary.simpleMessage("Tempo"),
        "paste": MessageLookupByLibrary.simpleMessage("Einfügen"),
        "paste_url_here":
            MessageLookupByLibrary.simpleMessage("URL hier einfügen"),
        "paste_youtube_link":
            MessageLookupByLibrary.simpleMessage("YouTube-Link einfügen"),
        "payment_required": MessageLookupByLibrary.simpleMessage(
            "Gratis-Guthaben aufgebraucht"),
        "payment_successfully":
            MessageLookupByLibrary.simpleMessage("Zahlung erfolgreich!"),
        "pdf_export": MessageLookupByLibrary.simpleMessage("PDF-Export"),
        "pdf_pdf": MessageLookupByLibrary.simpleMessage("PDF (.pdf)"),
        "per_year": MessageLookupByLibrary.simpleMessage(" pro Jahr"),
        "period": MessageLookupByLibrary.simpleMessage(" von %s Min"),
        "personalized_learning":
            MessageLookupByLibrary.simpleMessage("Übe dich zur"),
        "personalized_learning_at":
            MessageLookupByLibrary.simpleMessage("Übe dich zur Bestnote"),
        "photos": MessageLookupByLibrary.simpleMessage("Fotos"),
        "pick_specific_language": MessageLookupByLibrary.simpleMessage(
            "Wähle die Hauptsprache für bessere Transkription"),
        "plan": MessageLookupByLibrary.simpleMessage("Tarif"),
        "please_select_a_language": MessageLookupByLibrary.simpleMessage(
            "Bitte erst Sprache wählen für genaue Transkription!"),
        "please_select_a_youtube_language": MessageLookupByLibrary.simpleMessage(
            "Bitte wählen Sie eine Zusammenfassungssprache aus. Dies ist die Sprache, die Sie in der Zusammenfassungsausgabe sehen werden"),
        "please_try_again":
            MessageLookupByLibrary.simpleMessage("Bitte erneut versuchen"),
        "please_wait": MessageLookupByLibrary.simpleMessage("Bitte warten"),
        "podcast": MessageLookupByLibrary.simpleMessage("Podcast"),
        "podcast_name": MessageLookupByLibrary.simpleMessage("Podcast-Name"),
        "policy": MessageLookupByLibrary.simpleMessage("Datenschutz"),
        "premium_features": MessageLookupByLibrary.simpleMessage(
            "Teste Premium-Funktionen und erlebe den Unterschied"),
        "preparing_video":
            MessageLookupByLibrary.simpleMessage("Video wird vorbereitet..."),
        "press_back_again_to_exit": MessageLookupByLibrary.simpleMessage(
            "Erneut zurück drücken zum Beenden!"),
        "preview_only": MessageLookupByLibrary.simpleMessage(
            "Nur Vorschau. Der Hintergrund wird basierend auf dem Inhalt von der KI generiert"),
        "priority_processing":
            MessageLookupByLibrary.simpleMessage("Priorisierte Verarbeitung"),
        "privacy_policy":
            MessageLookupByLibrary.simpleMessage("Datenschutzrichtlinie"),
        "private": MessageLookupByLibrary.simpleMessage("Privat"),
        "pro": MessageLookupByLibrary.simpleMessage("PRO-Tarif"),
        "proLite": MessageLookupByLibrary.simpleMessage("Pro Lite"),
        "pro_01": MessageLookupByLibrary.simpleMessage("Pro"),
        "pro_6_hours":
            MessageLookupByLibrary.simpleMessage("Pro: 6 Stunden pro Datei"),
        "pro_access_life_time":
            MessageLookupByLibrary.simpleMessage("PRO-ZUGANG LEBENSLANG"),
        "pro_lifetime": MessageLookupByLibrary.simpleMessage("PRO Lifetime"),
        "process_your_document": MessageLookupByLibrary.simpleMessage(
            "Dokument wird verarbeitet..."),
        "process_your_text": MessageLookupByLibrary.simpleMessage(
            "Dein Text wird verarbeitet..."),
        "processing_content":
            MessageLookupByLibrary.simpleMessage("Inhalt wird verarbeitet..."),
        "processing_file":
            MessageLookupByLibrary.simpleMessage("Datei wird verarbeitet.."),
        "processing_image":
            MessageLookupByLibrary.simpleMessage("Bild wird verarbeitet..."),
        "processing_note_audio_file":
            MessageLookupByLibrary.simpleMessage("Audio wird verarbeitet..."),
        "processing_note_recording": MessageLookupByLibrary.simpleMessage(
            "Aufnahme wird verarbeitet..."),
        "processing_note_youtube": MessageLookupByLibrary.simpleMessage(
            "YouTube-Video wird verarbeitet..."),
        "processing_web_link":
            MessageLookupByLibrary.simpleMessage("Weblink wird verarbeitet"),
        "producing_flashcards": MessageLookupByLibrary.simpleMessage(
            "KI-Lernkarten werden erstellt.."),
        "professional": MessageLookupByLibrary.simpleMessage("Profi"),
        "professional_description": MessageLookupByLibrary.simpleMessage(
            "Formelle Sprache für den Arbeitskontext"),
        "professional_style":
            MessageLookupByLibrary.simpleMessage("Professionell"),
        "public": MessageLookupByLibrary.simpleMessage("Öffentlich"),
        "purchase_fail": MessageLookupByLibrary.simpleMessage(
            "Kauf fehlgeschlagen! Bitte erneut versuchen!"),
        "purchase_init_fail": MessageLookupByLibrary.simpleMessage(
            "Ups! Kauf konnte nicht gestartet werden. Bitte erneut versuchen"),
        "purpose_using":
            MessageLookupByLibrary.simpleMessage("für die Nutzung von "),
        "quarter": MessageLookupByLibrary.simpleMessage("Quartal"),
        "quarterly": MessageLookupByLibrary.simpleMessage("Vierteljährlich"),
        "question": MessageLookupByLibrary.simpleMessage("Frage"),
        "quick_access": MessageLookupByLibrary.simpleMessage("Zugriff auf"),
        "quick_import": MessageLookupByLibrary.simpleMessage(
            "Oder schnell importieren aus"),
        "quickly": MessageLookupByLibrary.simpleMessage("Konzepten"),
        "quiz": MessageLookupByLibrary.simpleMessage("Quiz"),
        "quiz_count": MessageLookupByLibrary.simpleMessage("Quizanzahl"),
        "quiz_diff": MessageLookupByLibrary.simpleMessage("Quizschwierigkeit"),
        "quiz_gen_success":
            MessageLookupByLibrary.simpleMessage("Quiz erfolgreich erstellt"),
        "quiz_iap": MessageLookupByLibrary.simpleMessage("Quiz-Set"),
        "quiz_master": MessageLookupByLibrary.simpleMessage("KI-Quizmaster"),
        "quiz_score": MessageLookupByLibrary.simpleMessage("Quiz-Ergebnis"),
        "quiz_set": MessageLookupByLibrary.simpleMessage("Quizsätze"),
        "quiz_set_not_found":
            MessageLookupByLibrary.simpleMessage("Quiz-Set nicht gefunden"),
        "quizz_for": MessageLookupByLibrary.simpleMessage("Quiz für"),
        "quizzes": MessageLookupByLibrary.simpleMessage("Quizze"),
        "rate": MessageLookupByLibrary.simpleMessage("Bewerten"),
        "rate_five_stars":
            MessageLookupByLibrary.simpleMessage("Mit 5 Sternen bewerten"),
        "rate_us_on_store":
            MessageLookupByLibrary.simpleMessage("Im Store bewerten"),
        "rating_cmt1": MessageLookupByLibrary.simpleMessage(
            "Diese App ist wirklich erstaunlich und wird immer besser. Danke an die Entwickler für ihre harte Arbeit. Besser als alle anderen KI-Notiz-Apps"),
        "rating_cmt2": MessageLookupByLibrary.simpleMessage(
            "Ich liebe diese App absolut! Der perfekte Begleiter für all meine Meetings."),
        "rating_cmt3": MessageLookupByLibrary.simpleMessage(
            "Hat meine Studienzeit halbiert. Mehr Zeit für Kaffeepausen!"),
        "rating_cmt4": MessageLookupByLibrary.simpleMessage(
            "Diese App ist absolut verblüffend! Sie meistert nicht nur die Transkription, sondern hebt alles mit unglaublichen Zusammenfassungen, Gliederungen und Aktionspunkten auf ein neues Level. Reine Genialität!"),
        "rating_cmt5": MessageLookupByLibrary.simpleMessage(
            "Wunderbar, leistungsstark! Alles was man braucht und mehr"),
        "rating_cmt6": MessageLookupByLibrary.simpleMessage(
            "Heute zum ersten Mal mit einer YouTube-Präsentation getestet. Sofort Transkription, Mindmap und Lernkarten erstellt. Nutze ich täglich"),
        "rating_cmt7": MessageLookupByLibrary.simpleMessage(
            "Bisher die beste Notiz-App. Hat viele nützliche Funktionen"),
        "rating_cmt8": MessageLookupByLibrary.simpleMessage(
            "Erfasst jedes Detail aus Biologievorlesungen. Die Zusammenfassungsfunktion ist perfekt für die Prüfungsvorbereitung"),
        "rating_sub_context_1":
            MessageLookupByLibrary.simpleMessage("Umwerfend"),
        "rating_sub_context_2":
            MessageLookupByLibrary.simpleMessage("Besprechung Pro"),
        "rating_sub_context_3":
            MessageLookupByLibrary.simpleMessage("Zeitersparnis"),
        "rating_sub_context_4":
            MessageLookupByLibrary.simpleMessage("Beste KI-Notizen"),
        "rating_sub_context_5":
            MessageLookupByLibrary.simpleMessage("Liebe diese App sehr"),
        "rating_sub_context_6":
            MessageLookupByLibrary.simpleMessage("Beste KI-Notizen"),
        "rating_sub_context_7":
            MessageLookupByLibrary.simpleMessage("Beste Notiz-App bisher"),
        "rating_sub_context_8":
            MessageLookupByLibrary.simpleMessage("Bisher der Beste"),
        "record": MessageLookupByLibrary.simpleMessage("Aufnahme"),
        "record_audio": MessageLookupByLibrary.simpleMessage("Audio aufnehmen"),
        "record_audio_coming_soon":
            MessageLookupByLibrary.simpleMessage("Audioaufnahme (demnächst)"),
        "record_over_x_min":
            MessageLookupByLibrary.simpleMessage("Aufnahme über %s Minuten"),
        "record_over_x_min_details": MessageLookupByLibrary.simpleMessage(
            "Ihre Aufnahmen werden lokal ohne KI-Transkription und Zusammenfassung gespeichert. Sie können nach Abschluss alle Beschränkungen aufheben."),
        "record_summarize_lecture": MessageLookupByLibrary.simpleMessage(
            "Vorlesungen aufnehmen & zusammenfassen"),
        "recording": MessageLookupByLibrary.simpleMessage("Aufnahme"),
        "recording_in_progress":
            MessageLookupByLibrary.simpleMessage("Aufnahme läuft"),
        "recording_in_progress_content":
            MessageLookupByLibrary.simpleMessage("Nimmt auf..."),
        "recording_paused":
            MessageLookupByLibrary.simpleMessage("Aufnahme pausiert"),
        "recording_paused_content":
            MessageLookupByLibrary.simpleMessage("Zum Fortsetzen drücken"),
        "recording_permission_denied": MessageLookupByLibrary.simpleMessage(
            "Aufnahme-Berechtigung verweigert!"),
        "recording_permission_denied_details":
            MessageLookupByLibrary.simpleMessage("In Einstellungen erlauben"),
        "recording_quality":
            MessageLookupByLibrary.simpleMessage("Aufnahmequalität"),
        "recording_schedule":
            MessageLookupByLibrary.simpleMessage("Aufnahmezeitplan"),
        "recording_voice_note":
            MessageLookupByLibrary.simpleMessage("Sprachnotiz aufnehmen"),
        "redeem_7_days_for_0":
            MessageLookupByLibrary.simpleMessage("7 Tage für 0 einlösen"),
        "redeem_credits": MessageLookupByLibrary.simpleMessage("Einlösen"),
        "refer_now": MessageLookupByLibrary.simpleMessage("Jetzt empfehlen"),
        "refer_rewards":
            MessageLookupByLibrary.simpleMessage("Empfehlen & Belohnungen"),
        "referral": MessageLookupByLibrary.simpleMessage("Empfehlung"),
        "referral_already_used": MessageLookupByLibrary.simpleMessage(
            "Empfehlungscode wurde bereits verwendet."),
        "referral_code":
            MessageLookupByLibrary.simpleMessage("Empfehlungscode"),
        "referral_credits":
            MessageLookupByLibrary.simpleMessage("Empfehlungscredits"),
        "referral_not_found": MessageLookupByLibrary.simpleMessage(
            "Empfehlungscode nicht gefunden."),
        "referral_self_use": MessageLookupByLibrary.simpleMessage(
            "Du kannst deinen eigenen Empfehlungscode nicht verwenden."),
        "referral_time_expired": MessageLookupByLibrary.simpleMessage(
            "Empfehlungscode ist nach 24 Stunden abgelaufen."),
        "referral_validation_err": MessageLookupByLibrary.simpleMessage(
            "Fehler bei der Empfehlungsvalidierung."),
        "reload_tap":
            MessageLookupByLibrary.simpleMessage("Fehler, zum Neuladen tippen"),
        "remain_recording_length": MessageLookupByLibrary.simpleMessage(
            "30s - 5min je nach Aufnahmelänge..."),
        "reminders_record_audio": MessageLookupByLibrary.simpleMessage(
            "Richten Sie wöchentliche Audioaufnahmezeiten ein"),
        "remove_all_limits":
            MessageLookupByLibrary.simpleMessage("Alle Limits aufheben"),
        "replace": MessageLookupByLibrary.simpleMessage("Ersetzen"),
        "replace_all": MessageLookupByLibrary.simpleMessage("Alle ersetzen"),
        "report_issue": MessageLookupByLibrary.simpleMessage("Problem melden?"),
        "report_issue2":
            MessageLookupByLibrary.simpleMessage("Wir helfen dir:"),
        "required": MessageLookupByLibrary.simpleMessage("Bsp: Ordner_name A"),
        "reset": MessageLookupByLibrary.simpleMessage("Zurück"),
        "restart_now":
            MessageLookupByLibrary.simpleMessage("Jetzt neu starten"),
        "restore": MessageLookupByLibrary.simpleMessage("Wiederherstellen"),
        "restore_fail_message": MessageLookupByLibrary.simpleMessage(
            "Für Hilfe <NAME_EMAIL>"),
        "restore_fail_title": MessageLookupByLibrary.simpleMessage(
            "Keine Elemente zur Wiederherstellung"),
        "restore_purchase":
            MessageLookupByLibrary.simpleMessage("Käufe wiederherstellen"),
        "restore_success_title": MessageLookupByLibrary.simpleMessage(
            "Wiederherstellung erfolgreich"),
        "retention": MessageLookupByLibrary.simpleMessage("Gedächtnis stärken"),
        "retention_quickly":
            MessageLookupByLibrary.simpleMessage("zwischen Konzepten"),
        "retry": MessageLookupByLibrary.simpleMessage("Wiederholen"),
        "sale_off": MessageLookupByLibrary.simpleMessage("RABATT"),
        "satisfied":
            MessageLookupByLibrary.simpleMessage("Danke für dein Feedback!"),
        "satisfied_quality": MessageLookupByLibrary.simpleMessage(
            "Ist diese Notiz klar und nützlich?"),
        "save": MessageLookupByLibrary.simpleMessage("Speichern"),
        "save_50": MessageLookupByLibrary.simpleMessage("50% sparen"),
        "save_changes":
            MessageLookupByLibrary.simpleMessage("Änderungen speichern?"),
        "save_chat": MessageLookupByLibrary.simpleMessage("Chat speichern"),
        "save_file":
            MessageLookupByLibrary.simpleMessage("Datei wurde gespeichert"),
        "saved_chat":
            MessageLookupByLibrary.simpleMessage("Gespeicherter Chat"),
        "saved_successfully":
            MessageLookupByLibrary.simpleMessage("Erfolgreich gespeichert"),
        "saving_recording":
            MessageLookupByLibrary.simpleMessage("Aufnahme wird gespeichert"),
        "search": MessageLookupByLibrary.simpleMessage("Suchen"),
        "search_emoji": MessageLookupByLibrary.simpleMessage("Emoji suchen"),
        "search_in_files":
            MessageLookupByLibrary.simpleMessage("In Dateien suchen"),
        "searching_all_notes":
            MessageLookupByLibrary.simpleMessage("Durchsuche alle Notizen"),
        "seconds": MessageLookupByLibrary.simpleMessage("Sekunden"),
        "select_a_language": MessageLookupByLibrary.simpleMessage(
            "Sprache vor dem Speichern für den Aufnahmeprozess wählen."),
        "select_all": MessageLookupByLibrary.simpleMessage("Alle auswählen"),
        "select_and_reorder": MessageLookupByLibrary.simpleMessage(
            "Wähle und ordne deine Notizmodule neu. Mindestens 4 Tabs erforderlich."),
        "select_language":
            MessageLookupByLibrary.simpleMessage("Sprache wählen"),
        "select_your_note":
            MessageLookupByLibrary.simpleMessage("Wähle deine Notizmodule."),
        "select_your_primary_use_case":
            MessageLookupByLibrary.simpleMessage("Hauptanwendung wählen"),
        "server_err": MessageLookupByLibrary.simpleMessage(
            "Ein unbekannter Serverfehler ist aufgetreten."),
        "server_error":
            MessageLookupByLibrary.simpleMessage("Etwas ist schiefgelaufen"),
        "setting": MessageLookupByLibrary.simpleMessage("Einstellungen"),
        "settings": MessageLookupByLibrary.simpleMessage("Einstellungen"),
        "seven_day_free":
            MessageLookupByLibrary.simpleMessage("7 Tage kostenlos, dann"),
        "share": MessageLookupByLibrary.simpleMessage("Freunden senden"),
        "share_audio_file":
            MessageLookupByLibrary.simpleMessage("Audio teilen"),
        "share_code_friends": MessageLookupByLibrary.simpleMessage(
            "Teile den Code mit Freunden per E-Mail, Social Media oder Nachrichten."),
        "share_file": MessageLookupByLibrary.simpleMessage("Audiodatei teilen"),
        "share_note": MessageLookupByLibrary.simpleMessage("Notizen teilen"),
        "share_note_link": MessageLookupByLibrary.simpleMessage("Notiz teilen"),
        "share_only": MessageLookupByLibrary.simpleMessage("Teilen"),
        "share_referral_code_start_earning_credits":
            MessageLookupByLibrary.simpleMessage(
                "Teile deinen Empfehlungscode und beginne, Guthaben zu verdienen!"),
        "share_summary":
            MessageLookupByLibrary.simpleMessage("Zusammenfassung kopieren"),
        "share_sync": MessageLookupByLibrary.simpleMessage("Teilen & Sync"),
        "share_transcript":
            MessageLookupByLibrary.simpleMessage("Transkript kopieren"),
        "share_with_link":
            MessageLookupByLibrary.simpleMessage("Mit Link teilen:"),
        "shared": MessageLookupByLibrary.simpleMessage("Geteilt"),
        "sharing_export":
            MessageLookupByLibrary.simpleMessage("Teilen & Export"),
        "short": MessageLookupByLibrary.simpleMessage("Kurz"),
        "short_description":
            MessageLookupByLibrary.simpleMessage("Nur Kernpunkte"),
        "shorts": MessageLookupByLibrary.simpleMessage("Shorts"),
        "show_your_love": MessageLookupByLibrary.simpleMessage(
            "Zeig deine Unterstützung mit"),
        "signing_in":
            MessageLookupByLibrary.simpleMessage("Anmeldung läuft..."),
        "skip": MessageLookupByLibrary.simpleMessage("Überspringen"),
        "slide_count":
            MessageLookupByLibrary.simpleMessage("Folienshow erstellen"),
        "slide_count_tooltip": MessageLookupByLibrary.simpleMessage(
            "Sie können bis zu 12 Frage-Antwort-Sets erstellen"),
        "slide_range":
            MessageLookupByLibrary.simpleMessage("Folienshow erstellen"),
        "slide_show": MessageLookupByLibrary.simpleMessage("Präsentation"),
        "smart_learning":
            MessageLookupByLibrary.simpleMessage("Intelligentes Lernen"),
        "smart_note_big_ideas":
            MessageLookupByLibrary.simpleMessage("Smarte Notizen, Große Ideen"),
        "smart_quizzes":
            MessageLookupByLibrary.simpleMessage("Unbegrenzte adaptive Quizze"),
        "smart_start":
            MessageLookupByLibrary.simpleMessage("Smart-Starterpaket"),
        "sort_by": MessageLookupByLibrary.simpleMessage("Sortieren nach"),
        "special_gift":
            MessageLookupByLibrary.simpleMessage("Spezial-Geschenk"),
        "special_gift_title":
            MessageLookupByLibrary.simpleMessage("SPEZIALGABRIEL"),
        "special_offer":
            MessageLookupByLibrary.simpleMessage("SONDER\nANGEBOT"),
        "speech_language": MessageLookupByLibrary.simpleMessage("Sprache"),
        "start_for_free":
            MessageLookupByLibrary.simpleMessage("Kostenlos starten"),
        "start_free_trial":
            MessageLookupByLibrary.simpleMessage("Testversion starten"),
        "start_my_7_day_trial": MessageLookupByLibrary.simpleMessage(
            "Meine 7-Tage-Testversion starten"),
        "start_record":
            MessageLookupByLibrary.simpleMessage("Aufnahme starten"),
        "start_speaking":
            MessageLookupByLibrary.simpleMessage("Starte zu sprechen"),
        "step1": MessageLookupByLibrary.simpleMessage(
            "Öffne die Einstellungen in NoteX."),
        "step2": MessageLookupByLibrary.simpleMessage(
            "Finde die App-Version unten (z.B. v1.4.0(6))."),
        "step3": MessageLookupByLibrary.simpleMessage(
            "Tippe 5-mal schnell auf die Version."),
        "step4": MessageLookupByLibrary.simpleMessage(
            "Deine User-ID wird automatisch kopiert."),
        "step5": MessageLookupByLibrary.simpleMessage(
            "Füge deiner Nachricht bitte hinzu:"),
        "step51": MessageLookupByLibrary.simpleMessage(
            "Deine User-ID (aus Zwischenablage einfügen)."),
        "step52":
            MessageLookupByLibrary.simpleMessage("Kurze Problembeschreibung."),
        "step53": MessageLookupByLibrary.simpleMessage(
            "Relevante Details (z.B. Gerätemodell, iOS-Version)."),
        "step6":
            MessageLookupByLibrary.simpleMessage("Sende uns eine E-Mail an "),
        "student": MessageLookupByLibrary.simpleMessage("Student"),
        "style": MessageLookupByLibrary.simpleMessage("Stil"),
        "sub_rip": MessageLookupByLibrary.simpleMessage("SubRip (.srt)"),
        "sub_user_have_unlimited": MessageLookupByLibrary.simpleMessage(
            "Abonnenten haben unbegrenzten Zugriff auf alle Premium-Funktionen ohne Werbung\nNicht-Abonnenten können die App mit Werbung und eingeschränkten Premium-Funktionen nutzen\nZahlung wird beim Kaufabschluss über das Google Play-Konto abgewickelt\nDas Abonnement verlängert sich automatisch, wenn es nicht 24 Stunden vor Periodenende gekündigt wird\nIhr Konto wird entsprechend Ihres Plans 24 Stunden vor Periodenende zur Verlängerung belastet\nNicht genutzte Teile einer Testphase verfallen beim Kauf eines Abonnements\nSie können die automatische Verlängerung nach dem Kauf in den Abonnement-Einstellungen von Google Play verwalten. Die Deinstallation der App beendet Ihr Abonnement nicht."),
        "sub_will_auto_renew": MessageLookupByLibrary.simpleMessage(
            "Abonnements verlängern sich automatisch. Jederzeit kündbar."),
        "submit": MessageLookupByLibrary.simpleMessage("Absenden"),
        "submit_button": MessageLookupByLibrary.simpleMessage("Absenden"),
        "subscribe": MessageLookupByLibrary.simpleMessage("Abonnieren"),
        "subscribe_via_web": MessageLookupByLibrary.simpleMessage(
            "Bei Web-Abo verwalten Sie es unter notexapp.com/setting"),
        "success": MessageLookupByLibrary.simpleMessage("Erfolg"),
        "successfully": MessageLookupByLibrary.simpleMessage("Erfolgreich"),
        "suggest_features":
            MessageLookupByLibrary.simpleMessage("Features vorschlagen"),
        "suggested": MessageLookupByLibrary.simpleMessage("Vorgeschlagen"),
        "summarize_video": MessageLookupByLibrary.simpleMessage(
            "Lange YouTube-Videos zusammenfassen"),
        "summary": MessageLookupByLibrary.simpleMessage("Zusammenfassung"),
        "summary_style":
            MessageLookupByLibrary.simpleMessage("Zusammenfassungsstil"),
        "summary_successful": MessageLookupByLibrary.simpleMessage(
            "Zusammenfassung erfolgreich erstellt!"),
        "summary_usefulness": MessageLookupByLibrary.simpleMessage(
            "Nützlichkeit der Zusammenfassung"),
        "supercharge": MessageLookupByLibrary.simpleMessage(
            "Mehr erreichen, weniger Stress"),
        "support_audio": MessageLookupByLibrary.simpleMessage(
            "Unterstützte Dateiformate: .mp3, .wav, .ogg, .m4a"),
        "support_for_up_to_10_images": MessageLookupByLibrary.simpleMessage(
            "Unterstützung für bis zu 10 Bilder"),
        "support_image": MessageLookupByLibrary.simpleMessage(
            "Unterstützte Bildformate: .png, .jpg, .heif, .heic"),
        "support_over_onehundred_languages":
            MessageLookupByLibrary.simpleMessage("Über 100 Sprachen verfügbar"),
        "support_youtube_and_more": MessageLookupByLibrary.simpleMessage(
            "Unterstützt YouTube, Web, TikTok, Instagram, Facebook und mehr"),
        "switch_mode": MessageLookupByLibrary.simpleMessage("Modus wechseln"),
        "sync_from_watch":
            MessageLookupByLibrary.simpleMessage("Synchronisieren von der Uhr"),
        "sync_notes": MessageLookupByLibrary.simpleMessage(
            "Notizen im Browser synchronisieren"),
        "system": MessageLookupByLibrary.simpleMessage("System"),
        "tap_cancel": MessageLookupByLibrary.simpleMessage(
            "Tippen Sie auf Abonnement kündigen"),
        "tap_menu": MessageLookupByLibrary.simpleMessage(
            "Tippen Sie auf Menü > Abonnements und wählen Sie das zu kündigende Abonnement"),
        "tap_the": MessageLookupByLibrary.simpleMessage("Tippe auf"),
        "tap_the_record":
            MessageLookupByLibrary.simpleMessage("Tippe auf Aufnahme"),
        "tap_to_record": MessageLookupByLibrary.simpleMessage(
            "Tippe zum Aufnehmen deiner Gedanken"),
        "task_create_err": MessageLookupByLibrary.simpleMessage(
            "Aufgabenerstellung fehlgeschlagen. Später versuchen."),
        "templates": MessageLookupByLibrary.simpleMessage("Vorlagen"),
        "term_and_cond": MessageLookupByLibrary.simpleMessage(
            "Allgemeine Geschäftsbedingungen"),
        "terms": MessageLookupByLibrary.simpleMessage("AGB"),
        "terms_of_sub":
            MessageLookupByLibrary.simpleMessage("Abonnementbedingungen"),
        "terms_of_use":
            MessageLookupByLibrary.simpleMessage("Nutzungsbedingungen"),
        "text": MessageLookupByLibrary.simpleMessage("Text hinzufügen"),
        "text_must_not_exceed_50_chars": MessageLookupByLibrary.simpleMessage(
            "Text darf maximal 50 Zeichen enthalten"),
        "thank_feedback":
            MessageLookupByLibrary.simpleMessage("Danke für\'s Feedback!"),
        "thinking": MessageLookupByLibrary.simpleMessage("Denken..."),
        "thirty_min_per":
            MessageLookupByLibrary.simpleMessage("30 Min pro \n Woche"),
        "this_folder_empty": MessageLookupByLibrary.simpleMessage(
            "Zeit für deine erste KI-Notiz! ✨"),
        "this_free_trial": MessageLookupByLibrary.simpleMessage(
            "Diese Testversion ist nur für Neukunden. Teste alle Pro-Funktionen eine Woche lang."),
        "this_is_the_language": MessageLookupByLibrary.simpleMessage(
            "Dies ist die Sprache, die Sie in der Zusammenfassungsausgabe sehen werden"),
        "thousands_trusted": MessageLookupByLibrary.simpleMessage(
            "4.8/5 Sterne: Von Tausenden vertraut"),
        "time": MessageLookupByLibrary.simpleMessage("Zeit"),
        "time_black_friday":
            MessageLookupByLibrary.simpleMessage("22 - 30 Nov"),
        "time_black_friday_2":
            MessageLookupByLibrary.simpleMessage("22 - 30 November"),
        "time_out": MessageLookupByLibrary.simpleMessage(
            "Zeitüberschreitung. Erneut versuchen."),
        "title": MessageLookupByLibrary.simpleMessage("Titel"),
        "title_error_note": MessageLookupByLibrary.simpleMessage(
            "Notiz-Erstellung fehlgeschlagen"),
        "title_success_note":
            MessageLookupByLibrary.simpleMessage("KI-Notizen erstellt"),
        "to": MessageLookupByLibrary.simpleMessage("um"),
        "to_day": MessageLookupByLibrary.simpleMessage("Heute"),
        "token_expired":
            MessageLookupByLibrary.simpleMessage("Token abgelaufen!"),
        "tolower_credits": MessageLookupByLibrary.simpleMessage("Guthaben"),
        "tool_tip_language": MessageLookupByLibrary.simpleMessage(
            "Hauptsprache vor dem Speichern der Aufnahme wählen"),
        "topic_option":
            MessageLookupByLibrary.simpleMessage("Thema (optional)"),
        "total": MessageLookupByLibrary.simpleMessage("Gesamt"),
        "transcribing":
            MessageLookupByLibrary.simpleMessage("KI-Transkription läuft"),
        "transcribing_audio":
            MessageLookupByLibrary.simpleMessage("Audio wird transkribiert.."),
        "transcript": MessageLookupByLibrary.simpleMessage("Transkript"),
        "transcript_context":
            MessageLookupByLibrary.simpleMessage("Transkript-Kontext"),
        "transcript_language":
            MessageLookupByLibrary.simpleMessage("Transkriptsprache"),
        "transcript_line_cannot_be_empty": MessageLookupByLibrary.simpleMessage(
            "Transkriptzeile darf nicht leer sein"),
        "transcript_line_tool_tip": MessageLookupByLibrary.simpleMessage(
            "Klick auf das Transkriptelement zum Bearbeiten"),
        "transcription_precision":
            MessageLookupByLibrary.simpleMessage("Transkriptionsgenauigkeit"),
        "transform_meetings":
            MessageLookupByLibrary.simpleMessage("Meetings umwandeln in"),
        "transform_meetings_into_actionable_intelligence":
            MessageLookupByLibrary.simpleMessage(
                "Meetings in Aktionen umwandeln"),
        "translate_note":
            MessageLookupByLibrary.simpleMessage("Notiz Übersetzen"),
        "translating_note":
            MessageLookupByLibrary.simpleMessage("Übersetzen..."),
        "translation_completed":
            MessageLookupByLibrary.simpleMessage("Übersetzung Abgeschlossen"),
        "translation_failed":
            MessageLookupByLibrary.simpleMessage("Übersetzung Fehlgeschlagen"),
        "trouble_connecting_to_server": MessageLookupByLibrary.simpleMessage(
            "Wir haben Probleme bei der Verbindung zum Server. Bitte versuchen Sie es in einem Moment erneut."),
        "try_3_day":
            MessageLookupByLibrary.simpleMessage("3 Tage kostenlos testen"),
        "try_7_day": MessageLookupByLibrary.simpleMessage(
            "Meine 7-Tage-Testversion starten"),
        "try_again": MessageLookupByLibrary.simpleMessage(
            "Fehler beim Erstellen. Bitte erneut versuchen!"),
        "try_again_button":
            MessageLookupByLibrary.simpleMessage("Erneut versuchen"),
        "try_pro_free_7_day":
            MessageLookupByLibrary.simpleMessage("7 Tage Pro kostenlos testen"),
        "type_or_paste_any_text_here": MessageLookupByLibrary.simpleMessage(
            "Gib hier Text ein oder füge ihn ein. KI erstellt eine klare, strukturierte Zusammenfassung mit Highlights."),
        "uidCopied": m3,
        "unable_download_file":
            MessageLookupByLibrary.simpleMessage("Download nicht möglich"),
        "unable_load_audio":
            MessageLookupByLibrary.simpleMessage("Audio nicht ladbar:"),
        "unable_share_audio":
            MessageLookupByLibrary.simpleMessage("Audiodatei nicht teilbar"),
        "unable_to_connect_to_server": MessageLookupByLibrary.simpleMessage(
            "Stellen Sie sicher, dass Ihr Telefon mit dem Internet verbunden ist"),
        "unable_to_extract_web_url": MessageLookupByLibrary.simpleMessage(
            "URL-Inhalt nicht extrahierbar"),
        "unable_to_open_store": MessageLookupByLibrary.simpleMessage(
            "Store kann nicht geöffnet werden"),
        "uncover_opportunities":
            MessageLookupByLibrary.simpleMessage("Chancen finden"),
        "unknown_error": MessageLookupByLibrary.simpleMessage(
            "Ein unbekannter Fehler ist aufgetreten"),
        "unknown_server_error": MessageLookupByLibrary.simpleMessage(
            "Ups! Serverfehler. Bitte erneut versuchen."),
        "unlimited_ai_chat": MessageLookupByLibrary.simpleMessage(
            "Unbegrenzter KI-Chat, KI-Mindmap, Karteikarten, Quiz"),
        "unlimited_ai_chat_ai_mind_map_flashcard_quiz":
            MessageLookupByLibrary.simpleMessage(
                "Unbegrenzte KI-Chat, KI-Mindmaps, Lernkarten, Quiz"),
        "unlimited_ai_note": MessageLookupByLibrary.simpleMessage(
            "Unbegrenzte KI-Notizen aus allen Quellen (YouTube, Dokumente, Aufnahmen, Audio)"),
        "unlimited_ai_notes_from_youtube_and_document":
            MessageLookupByLibrary.simpleMessage(
                "Unbegrenzte KI-Notizen aus YouTube & Dokumenten"),
        "unlimited_audio_youtube_website_to_ai_notes":
            MessageLookupByLibrary.simpleMessage(
                "Unbegrenzte Audio-, YouTube-, Dokument- & Website-zu-KI-Notizen"),
        "unlimited_everything": MessageLookupByLibrary.simpleMessage(
            "Erleben Sie unbegrenzte KI-Notizen, priorisierten Service und Premium-Funktionen"),
        "unlimited_youtube_document_ai_notes":
            MessageLookupByLibrary.simpleMessage(
                "Unbegrenzte YouTube- & Dokument-KI-Notizen"),
        "unlock_all_features": MessageLookupByLibrary.simpleMessage(
            "Alle Funktionen freischalten"),
        "unlock_essential_life_time": MessageLookupByLibrary.simpleMessage(
            "Essential Lifetime entsperren"),
        "unlock_lifetime_access": MessageLookupByLibrary.simpleMessage(
            "Leben lang Zugang entsperren"),
        "unlock_pro_lifetime":
            MessageLookupByLibrary.simpleMessage("PRO Lifetime freischalten"),
        "unlock_the_most_ipad": MessageLookupByLibrary.simpleMessage(
            "Schalte den mächtigsten KI-Notizassistenten frei"),
        "unlock_the_most_powerful_ai_note_taking_assistant":
            MessageLookupByLibrary.simpleMessage(
                "Schalte den mächtigsten KI-\nNotizassistenten frei"),
        "unlock_toge":
            MessageLookupByLibrary.simpleMessage("GEMEINSAM FREISCHALTEN"),
        "unlock_together":
            MessageLookupByLibrary.simpleMessage("Gemeinsam freischalten"),
        "unlock_unlimited_access_to_all_ai_features":
            MessageLookupByLibrary.simpleMessage(
                "Unbegrenzten Zugang zu allen KI-Funktionen freischalten"),
        "unlock_unlimited_ai": MessageLookupByLibrary.simpleMessage(
            "Unbegrenztes KI-Erlebnis freischalten"),
        "unsynced_notes": MessageLookupByLibrary.simpleMessage(
            "Nicht synchronisierte Notizen"),
        "update_available": MessageLookupByLibrary.simpleMessage(
            "Ein neues Update ist verfügbar! Aktualisieren für beste Erfahrung."),
        "update_failed": MessageLookupByLibrary.simpleMessage(
            "Community-Notizen nicht aktualisiert. Erneut versuchen."),
        "update_later": MessageLookupByLibrary.simpleMessage("Später"),
        "update_now": MessageLookupByLibrary.simpleMessage("Jetzt updating!"),
        "update_pro":
            MessageLookupByLibrary.simpleMessage("Auf Pro-Erfahrung upgraden"),
        "update_to_pro":
            MessageLookupByLibrary.simpleMessage("Auf PRO upgraden"),
        "upgrade": MessageLookupByLibrary.simpleMessage("UPGRADEN"),
        "upgrade_now": MessageLookupByLibrary.simpleMessage("Jetzt Upgraden!"),
        "upgrade_plan": MessageLookupByLibrary.simpleMessage("Tarif upgraden"),
        "upgrade_to_full_pro_access": MessageLookupByLibrary.simpleMessage(
            "Upgrade auf vollen Pro-Zugang"),
        "upgrade_to_pro_tier_at_a_special_price":
            MessageLookupByLibrary.simpleMessage(
                "Auf Pro-Tarif zum Sonderpreis upgraden"),
        "upload": MessageLookupByLibrary.simpleMessage("Upload"),
        "upload_audio": MessageLookupByLibrary.simpleMessage("Audio hochladen"),
        "upload_audio_file":
            MessageLookupByLibrary.simpleMessage("Audiodatei hochladen"),
        "upload_file": MessageLookupByLibrary.simpleMessage("Datei hochladen"),
        "upload_image": MessageLookupByLibrary.simpleMessage("Bild hochladen"),
        "upload_in_progress": MessageLookupByLibrary.simpleMessage(
            "Upload läuft. Bildschirm aktiv lassen.\nVPN für schnelleren Upload deaktivieren."),
        "uploading_to_server":
            MessageLookupByLibrary.simpleMessage("Upload zum sicheren Server"),
        "user_disabled": MessageLookupByLibrary.simpleMessage(
            "Der Nutzer zu dieser E-Mail wurde deaktiviert."),
        "user_not_found": MessageLookupByLibrary.simpleMessage(
            "Benutzerinformationen nicht gefunden."),
        "verifying_your_credentials": MessageLookupByLibrary.simpleMessage(
            "Anmeldedaten werden überprüft"),
        "video": MessageLookupByLibrary.simpleMessage("Video"),
        "video_audio": MessageLookupByLibrary.simpleMessage(
            "Aufnahmen, Videos & Dokumente KI-Notizen"),
        "video_captions":
            MessageLookupByLibrary.simpleMessage("Videountertitel"),
        "video_is_temporary": MessageLookupByLibrary.simpleMessage(
            "*Video ist temporär - Vor dem Schließen speichern"),
        "visualize_strategies":
            MessageLookupByLibrary.simpleMessage("Strategien sehen und"),
        "visualize_strategies_opportunities":
            MessageLookupByLibrary.simpleMessage("Strategien & Chancen sehen"),
        "visualize_strategies_uncover":
            MessageLookupByLibrary.simpleMessage("Strategien und"),
        "visualize_strategies_uncover_opportunities":
            MessageLookupByLibrary.simpleMessage("Strategien visualisieren"),
        "voice": MessageLookupByLibrary.simpleMessage("Stimme"),
        "warning_this_ai_note_taking_app_may_cause_excessive_productivity":
            MessageLookupByLibrary.simpleMessage(
                "Warnung: Diese KI-Notiz-App könnte übermäßige Produktivität verursachen! 🚀 Nutze meinen Code und wir beide erhalten extra Nutzung. Code: "),
        "watch_sync_empty_message": MessageLookupByLibrary.simpleMessage(
            "Ihre Aufzeichnungen werden hier angezeigt"),
        "web": MessageLookupByLibrary.simpleMessage("Web"),
        "web_link": MessageLookupByLibrary.simpleMessage("Weblink"),
        "web_sync": MessageLookupByLibrary.simpleMessage("Web-Sync"),
        "website_import":
            MessageLookupByLibrary.simpleMessage("Website-Import"),
        "week": MessageLookupByLibrary.simpleMessage("Woche"),
        "week_free_limit": MessageLookupByLibrary.simpleMessage(
            "Wöchentliches Limit erreicht"),
        "weekly": MessageLookupByLibrary.simpleMessage("Wöchentlich"),
        "weekly_free_limit_reached":
            MessageLookupByLibrary.simpleMessage("Alle Limits aufheben"),
        "weekly_free_limit_reached_details": MessageLookupByLibrary.simpleMessage(
            "Sie haben Ihr kostenloses Transkriptions- und KI-Kontingent für diese Woche aufgebraucht! Upgraden Sie auf Pro für unbegrenzten Zugriff oder warten Sie bis nächste Woche."),
        "welcome_notex":
            MessageLookupByLibrary.simpleMessage("Willkommen bei NoteX!"),
        "welcome_title": MessageLookupByLibrary.simpleMessage(
            "Lass uns deine\nerste KI-Notiz erstellen"),
        "what_improve":
            MessageLookupByLibrary.simpleMessage("Was sollen wir verbessern"),
        "whats_new": MessageLookupByLibrary.simpleMessage("Neuigkeiten"),
        "word_docx": MessageLookupByLibrary.simpleMessage("Word (.docx)"),
        "work_notes_projects":
            MessageLookupByLibrary.simpleMessage("Arbeitsnotizen & Projekte"),
        "writing_style": MessageLookupByLibrary.simpleMessage("Schreibstil"),
        "wrong": MessageLookupByLibrary.simpleMessage("Falsch"),
        "x": MessageLookupByLibrary.simpleMessage("X"),
        "x_skip": MessageLookupByLibrary.simpleMessage("X?"),
        "year": MessageLookupByLibrary.simpleMessage("Jahr"),
        "yearly": MessageLookupByLibrary.simpleMessage("Jährlich"),
        "yes": MessageLookupByLibrary.simpleMessage("Ja"),
        "yesterday": MessageLookupByLibrary.simpleMessage("Gestern"),
        "you_are_given_a_special_gift_today":
            MessageLookupByLibrary.simpleMessage(
                "Sie erhalten heute einen besonderen Geschenk 🎁"),
        "you_are_pro": MessageLookupByLibrary.simpleMessage("PRO-Zugang"),
        "you_can_update_setting": MessageLookupByLibrary.simpleMessage(
            "Du kannst es jederzeit in den Einstellungen ändern."),
        "you_have_received": MessageLookupByLibrary.simpleMessage("Du hast"),
        "you_have_received2": MessageLookupByLibrary.simpleMessage(
            "Du erhältst eine Chance, NoteX Pro-Zugang auf Lebenszeit zu gewinnen! 3 Gewinner werden am 30. jeden Monats ausgewählt 🎁"),
        "you_will_get_one_entry_to_win_noteX":
            MessageLookupByLibrary.simpleMessage(
                "Du erhältst eine Chance, NoteX"),
        "you_will_not_be": MessageLookupByLibrary.simpleMessage(
            "Sie können es später nicht wiederherstellen"),
        "your_learning":
            MessageLookupByLibrary.simpleMessage("Optimiere dein Lernen!"),
        "your_learning_device":
            MessageLookupByLibrary.simpleMessage("NoteX Pro Zugang"),
        "your_note_are_ready":
            MessageLookupByLibrary.simpleMessage("Deine Notizen sind bereit."),
        "your_personal_study":
            MessageLookupByLibrary.simpleMessage("Ihr Studium"),
        "your_personal_study_assistant":
            MessageLookupByLibrary.simpleMessage("Ihr Studienassistent"),
        "your_plan": MessageLookupByLibrary.simpleMessage("Ihr Tarif"),
        "your_primary":
            MessageLookupByLibrary.simpleMessage("Was ist dein Hauptgrund"),
        "your_product":
            MessageLookupByLibrary.simpleMessage("IHR PRODUKTIVITÄT"),
        "your_recording_will_save": MessageLookupByLibrary.simpleMessage(
            "Aufnahmen werden lokal ohne KI-Funktionen gespeichert. Upgrade für volle Funktionalität."),
        "your_referrals":
            MessageLookupByLibrary.simpleMessage("Deine Empfehlungen"),
        "youtube": MessageLookupByLibrary.simpleMessage("YouTube"),
        "youtube_import":
            MessageLookupByLibrary.simpleMessage("YouTube-Import"),
        "youtube_link": MessageLookupByLibrary.simpleMessage("YouTube-Link"),
        "youtube_transcript_language_guidance":
            MessageLookupByLibrary.simpleMessage(
                "YouTube-Transkriptsprache wählen - Diese Sprache wird für KI-Notizen verwendet"),
        "youtube_video": MessageLookupByLibrary.simpleMessage("YouTube-Video"),
        "youtube_video_note":
            MessageLookupByLibrary.simpleMessage("YouTube-Video"),
        "yt_credit_err": MessageLookupByLibrary.simpleMessage(
            "YouTube-Guthaben aufgebraucht. Bitte upgraden."),
        "yt_credit_use_err": MessageLookupByLibrary.simpleMessage(
            "Fehler beim YouTube-Guthaben. Später versuchen."),
        "yt_length_err": MessageLookupByLibrary.simpleMessage(
            "Video überschreitet 10-Stunden-Limit. Kürzeres wählen."),
        "yt_process_err": MessageLookupByLibrary.simpleMessage(
            "YouTube-Verarbeitung fehlgeschlagen. URL prüfen."),
        "yt_sum_limit": MessageLookupByLibrary.simpleMessage(
            "YouTube-Zusammenfassungslimit"),
        "z_to_a": MessageLookupByLibrary.simpleMessage("Z bis A")
      };
}
